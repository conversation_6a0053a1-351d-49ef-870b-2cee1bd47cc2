package alipay

import (
	"fp-browser/pkg/log"
	"github.com/smartwalle/alipay/v3"
	"github.com/spf13/viper"
)

// Client 支付宝客户端包装器
type Client struct {
	client    *alipay.Client
	appID     string
	notifyURL string
	returnURL string
	logger    *log.Logger
}

// NewAlipay 创建支付宝客户端（使用viper配置）
func NewAlipay(conf *viper.Viper, l *log.Logger) *Client {
	// 从配置中获取支付宝相关参数
	appID := conf.GetString("payment.alipay.app_id")
	privateKey := conf.GetString("payment.alipay.private_key")
	isProduction := conf.GetBool("payment.alipay.is_production")
	notifyURL := conf.GetString("payment.alipay.notify_url")
	returnURL := conf.GetString("payment.alipay.return_url")

	// 检查必要参数
	if appID == "" {
		l.<PERSON>rror("支付宝 app_id 配置为空")
		panic("支付宝 app_id 不能为空")
	}

	if privateKey == "" {
		l.<PERSON>rror("支付宝 private_key 配置为空")
		panic("支付宝 private_key 不能为空")
	}

	if notifyURL == "" {
		l.Error("支付宝 notify_url 配置为空")
		panic("支付宝 notify_url 不能为空")
	}

	if returnURL == "" {
		l.Error("支付宝 return_url 配置为空")
		panic("支付宝 return_url 不能为空")
	}

	// 创建支付宝客户端
	client, err := alipay.New(appID, privateKey, isProduction)
	if err != nil {
		l.Error("创建支付宝客户端失败", "error", err, "appID", appID, "isProduction", isProduction)
		panic("创建支付宝客户端失败: " + err.Error())
	}

	// 配置证书模式（如果启用）
	if conf.GetBool("payment.alipay.cert_mode") {
		if err := setupCertMode(client, conf, l); err != nil {
			l.Error("配置支付宝证书模式失败", "error", err)
			panic("配置支付宝证书模式失败: " + err.Error())
		}
		l.Info("支付宝证书模式配置成功")
	}

	// 设置内容加密密钥（如果有）
	encryptKey := conf.GetString("payment.alipay.encrypt_key")
	if encryptKey != "" {
		if err := client.SetEncryptKey(encryptKey); err != nil {
			l.Error("设置支付宝加密密钥失败", "error", err)
			panic("设置支付宝加密密钥失败: " + err.Error())
		}
		l.Info("支付宝加密密钥设置成功")
	}

	l.Info("支付宝客户端创建成功",
		"appID", appID,
		"isProduction", isProduction,
		"notifyURL", notifyURL,
		"returnURL", returnURL,
		"certMode", conf.GetBool("payment.alipay.cert_mode"),
		"encryptEnabled", encryptKey != "")

	return &Client{
		client:    client,
		appID:     appID,
		notifyURL: notifyURL,
		returnURL: returnURL,
		logger:    l,
	}
}

// setupCertMode 配置证书模式
func setupCertMode(client *alipay.Client, conf *viper.Viper, l *log.Logger) error {
	appCertPath := conf.GetString("payment.alipay.app_cert_path")
	rootCertPath := conf.GetString("payment.alipay.root_cert_path")
	publicCertPath := conf.GetString("payment.alipay.public_cert_path")

	// 检查证书路径配置
	if appCertPath == "" {
		l.Error("支付宝应用证书路径配置为空")
		panic("支付宝应用证书路径不能为空")
	}
	if rootCertPath == "" {
		l.Error("支付宝根证书路径配置为空")
		panic("支付宝根证书路径不能为空")
	}
	if publicCertPath == "" {
		l.Error("支付宝公钥证书路径配置为空")
		panic("支付宝公钥证书路径不能为空")
	}

	// 加载应用证书
	if err := client.LoadAppCertPublicKeyFromFile(appCertPath); err != nil {
		l.Error("加载支付宝应用证书失败", "path", appCertPath, "error", err)
		return err
	}

	// 加载根证书
	if err := client.LoadAliPayRootCertFromFile(rootCertPath); err != nil {
		l.Error("加载支付宝根证书失败", "path", rootCertPath, "error", err)
		return err
	}

	// 加载公钥证书
	if err := client.LoadAlipayCertPublicKeyFromFile(publicCertPath); err != nil {
		l.Error("加载支付宝公钥证书失败", "path", publicCertPath, "error", err)
		return err
	}

	l.Info("支付宝证书加载成功",
		"appCert", appCertPath,
		"rootCert", rootCertPath,
		"publicCert", publicCertPath)

	return nil
}
