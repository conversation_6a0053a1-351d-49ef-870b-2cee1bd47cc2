package alipay

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"github.com/smartwalle/alipay/v3"
)

// Refund 申请退款
func (c *Client) Refund(ctx context.Context, orderID, tradeNo string, amount float64, reason string) (success bool, err error) {
	c.logger.Info("申请支付宝退款",
		"orderID", orderID,
		"tradeNo", tradeNo,
		"amount", amount,
		"reason", reason)

	// 创建退款请求
	p := alipay.TradeRefund{}

	// 订单号和交易号至少要有一个
	if orderID != "" {
		p.OutTradeNo = orderID
	}
	if tradeNo != "" {
		p.TradeNo = tradeNo
	}

	p.RefundAmount = fmt.Sprintf("%.2f", amount)
	if reason != "" {
		p.RefundReason = reason
	}

	// 发起退款请求
	rsp, err := c.client.TradeRefund(ctx, p)
	if err != nil {
		c.logger.Error("退款请求失败",
			"orderID", orderID,
			"tradeNo", tradeNo,
			"amount", amount,
			"error", err)
		return false, fmt.Errorf("退款请求失败: %w", err)
	}

	// 检查退款响应
	if rsp.IsFailure() {
		c.logger.Error("退款失败",
			"orderID", orderID,
			"tradeNo", tradeNo,
			"amount", amount,
			"msg", rsp.Msg,
			"subMsg", rsp.SubMsg)
		return false, fmt.Errorf("退款失败: %s - %s", rsp.Msg, rsp.SubMsg)
	}

	// 验证退款金额
	refundAmount, err := strconv.ParseFloat(rsp.RefundFee, 64)
	if err != nil {
		c.logger.Error("解析退款金额失败",
			"orderID", orderID,
			"refundFee", rsp.RefundFee,
			"error", err)
		return false, fmt.Errorf("解析退款金额失败: %w", err)
	}

	// 确保退款金额正确
	if !strings.EqualFold(fmt.Sprintf("%.2f", refundAmount), fmt.Sprintf("%.2f", amount)) {
		c.logger.Error("退款金额不匹配",
			"orderID", orderID,
			"requestAmount", amount,
			"actualAmount", refundAmount)
		return false, fmt.Errorf("退款金额不匹配: 请求 %.2f, 实际 %.2f", amount, refundAmount)
	}

	c.logger.Info("支付宝退款成功",
		"orderID", orderID,
		"tradeNo", tradeNo,
		"amount", amount,
		"refundAmount", refundAmount,
		"outTradeNo", rsp.OutTradeNo,
		"alipayTradeNo", rsp.TradeNo,
		"fundChange", rsp.FundChange)

	return true, nil
}
