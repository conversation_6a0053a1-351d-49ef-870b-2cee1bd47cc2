package alipay

import (
	"context"
	"encoding/json"
	"fmt"
)

// HandleCallback 处理支付回调通知
func (c *Client) HandleCallback(ctx context.Context, payload []byte) (string, error) {
	c.logger.Debug("处理支付宝回调通知", "payloadSize", len(payload))

	// 将payload转换为url.Values格式
	var values map[string][]string
	if err := json.Unmarshal(payload, &values); err != nil {
		c.logger.Error("解析回调数据失败", "error", err, "payloadSize", len(payload))
		return "", fmt.Errorf("解析回调数据失败: %w", err)
	}

	// 验证签名
	if err := c.client.VerifySign(values); err != nil {
		c.logger.Error("回调验证签名失败", "error", err)
		return "", fmt.Errorf("回调验证签名失败: %w", err)
	}

	// 处理异步通知
	notification, err := c.client.DecodeNotification(values)
	if err != nil {
		c.logger.Error("解析异步通知失败", "error", err)
		return "", fmt.Errorf("解析异步通知失败: %w", err)
	}

	c.logger.Info("收到支付宝异步通知",
		"orderID", notification.OutTradeNo,
		"tradeNo", notification.TradeNo,
		"tradeStatus", notification.TradeStatus,
		"totalAmount", notification.TotalAmount)

	// 验证交易状态
	if notification.TradeStatus != "TRADE_SUCCESS" {
		c.logger.Warn("交易未成功完成",
			"orderID", notification.OutTradeNo,
			"tradeStatus", notification.TradeStatus)
		return "", fmt.Errorf("交易未成功完成，当前状态: %s", notification.TradeStatus)
	}

	c.logger.Info("支付宝回调处理成功",
		"orderID", notification.OutTradeNo,
		"tradeNo", notification.TradeNo,
		"tradeStatus", notification.TradeStatus)

	// 返回订单号
	return notification.OutTradeNo, nil
}
