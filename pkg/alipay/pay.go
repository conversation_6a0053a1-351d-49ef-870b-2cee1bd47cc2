package alipay

import (
	"context"
	"fmt"

	"github.com/smartwalle/alipay/v3"
)

// CreatePayment 创建支付订单
func (c *Client) CreatePayment(ctx context.Context, orderID string, amount float64, subject, description, notifyURL string) (payURL string, err error) {
	// 如果传入了notifyURL，使用传入的，否则使用客户端配置的默认notifyURL
	callbackNotifyURL := c.notifyURL
	if notifyURL != "" {
		callbackNotifyURL = notifyURL
	}

	// 创建支付宝交易请求
	p := alipay.TradePagePay{}
	p.NotifyURL = callbackNotifyURL
	p.ReturnURL = c.returnURL
	p.Subject = subject
	p.OutTradeNo = orderID
	p.TotalAmount = fmt.Sprintf("%.2f", amount)
	p.ProductCode = "FAST_INSTANT_TRADE_PAY"

	if description != "" {
		p.Body = description
	}

	// 记录支付请求日志
	c.logger.Info("创建支付宝支付订单",
		"orderID", orderID,
		"amount", amount,
		"subject", subject,
		"notifyURL", callbackNotifyURL,
		"returnURL", c.returnURL)

	// 发起支付请求
	url, err := c.client.TradePagePay(p)
	if err != nil {
		c.logger.Error("创建支付宝支付订单失败",
			"orderID", orderID,
			"amount", amount,
			"error", err)
		return "", fmt.Errorf("创建支付宝支付订单失败: %w", err)
	}

	c.logger.Info("支付宝支付订单创建成功",
		"orderID", orderID,
		"amount", amount,
		"payURL", url.String())

	return url.String(), nil
}
