package http

import (
	"context"
	"fmt"
	"fp-browser/pkg/log"
	"github.com/kataras/iris/v12"
	"time"
)

type Server struct {
	*iris.Application
	host   string
	port   int
	logger *log.Logger
}

type Option func(s *Server)

func NewServer(app *iris.Application, logger *log.Logger, opts ...Option) *Server {
	s := &Server{
		Application: app,
		logger:      logger,
	}
	for _, opt := range opts {
		opt(s)
	}
	return s
}

func WithServerHost(host string) Option {
	return func(s *Server) {
		s.host = host
	}
}

func WithServerPort(port int) Option {
	return func(s *Server) {
		s.port = port
	}
}

func (s *Server) Start(ctx context.Context) error {
	addr := fmt.Sprintf("%s:%d", s.host, s.port)
	s.logger.Info("HTTP server starting", "addr", addr)

	// 非阻塞方式启动服务器
	go func() {
		// 配置服务器
		opts := []iris.Configurator{
			iris.WithoutServerError(iris.ErrServerClosed),
			iris.WithoutStartupLog,
		}

		// 启动服务器
		if err := s.Application.Listen(addr, opts...); err != nil {
			s.logger.Error("HTTP server error", "error", err)
		}
	}()

	return nil
}

func (s *Server) Stop(ctx context.Context) error {
	s.logger.Info("Shutting down HTTP server...")

	// 创建带超时的上下文，给服务器5秒时间完成当前请求
	timeoutCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	// 优雅关闭服务器
	if err := s.Application.Shutdown(timeoutCtx); err != nil {
		s.logger.Error("Server forced to shutdown", "error", err)
		return err
	}

	s.logger.Info("HTTP server exited")
	return nil
}
