package oss

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"hash"
	"io"
	"time"
)

// PostPolicyResponse 表单上传响应结构
type PostPolicyResponse struct {
	URL       string            `json:"url"`       // 上传URL
	Fields    map[string]string `json:"fields"`    // 表单字段
	Policy    string            `json:"policy"`    // Base64编码的policy
	Signature string            `json:"signature"` // 签名
}

// GeneratePostPolicyOptions 生成表单上传policy的选项
type GeneratePostPolicyOptions struct {
	ObjectName         string            `json:"object_name"`         // 对象名称（文件名）
	ExpirationDuration time.Duration     `json:"expiration_duration"` // 有效期
	ContentLengthMin   int64             `json:"content_length_min"`  // 最小文件大小，默认1字节
	ContentLengthMax   int64             `json:"content_length_max"`  // 最大文件大小，默认100MB
	EnableCallback     bool              `json:"enable_callback"`     // 是否启用回调
	CallbackVars       map[string]string `json:"callback_vars"`       // 回调自定义变量
}

// GeneratePostPolicy 生成表单上传的policy和签名
func (c *Client) GeneratePostPolicy(ctx context.Context, options GeneratePostPolicyOptions) (*PostPolicyResponse, error) {
	// 获取凭证
	cred, err := c.credentialsProvider.GetCredentials(ctx)
	if err != nil {
		c.logger.Error("获取凭证失败", "error", err)
		return nil, fmt.Errorf("获取凭证失败: %v", err)
	}

	// 设置默认值
	if options.ExpirationDuration == 0 {
		options.ExpirationDuration = 15 * time.Minute // 默认15分钟
	}
	if options.ContentLengthMin == 0 {
		options.ContentLengthMin = 1 // 默认最小1字节
	}
	if options.ContentLengthMax == 0 {
		options.ContentLengthMax = 100 * 1024 * 1024 // 默认最大100MB
	}

	// 构建Post Policy
	utcTime := time.Now().UTC()
	date := utcTime.Format("20060102")
	expiration := utcTime.Add(options.ExpirationDuration)

	// 构建conditions数组 - 只包含必需字段和用户需要的限制
	conditions := []any{
		map[string]string{"bucket": c.bucketName},
		map[string]string{"x-oss-signature-version": "OSS4-HMAC-SHA256"},
		map[string]string{"x-oss-credential": fmt.Sprintf("%v/%v/%v/oss/aliyun_v4_request",
			cred.AccessKeyID, date, c.region)},
		map[string]string{"x-oss-date": utcTime.Format("20060102T150405Z")},
		[]any{"content-length-range", options.ContentLengthMin, options.ContentLengthMax},
	}

	// 添加对象名限制（如果指定了文件名）
	if options.ObjectName != "" {
		conditions = append(conditions, []any{"eq", "$key", options.ObjectName})
	}

	// 构建policy映射
	policyMap := map[string]any{
		"expiration": expiration.Format("2006-01-02T15:04:05.000Z"),
		"conditions": conditions,
	}

	// 将Post Policy序列化为JSON字符串
	policy, err := json.Marshal(policyMap)
	if err != nil {
		c.logger.Error("序列化policy失败", "error", err)
		return nil, fmt.Errorf("序列化policy失败: %v", err)
	}

	// 将Post Policy编码为Base64字符串
	stringToSign := base64.StdEncoding.EncodeToString(policy)

	// 生成签名密钥
	signature, err := c.generateSignature(stringToSign, cred.AccessKeySecret, date, c.region)
	if err != nil {
		c.logger.Error("生成签名失败", "error", err)
		return nil, fmt.Errorf("生成签名失败: %v", err)
	}

	// 构建表单字段 - 只包含必需字段
	fields := map[string]string{
		"policy":                  stringToSign,
		"x-oss-signature-version": "OSS4-HMAC-SHA256",
		"x-oss-credential":        fmt.Sprintf("%v/%v/%v/oss/aliyun_v4_request", cred.AccessKeyID, date, c.region),
		"x-oss-date":              utcTime.Format("20060102T150405Z"),
		"x-oss-signature":         signature,
	}

	// 如果指定了对象名，添加到字段中
	if options.ObjectName != "" {
		fields["key"] = options.ObjectName
	}

	// 如果启用了回调且配置了回调URL，添加回调参数
	if options.EnableCallback && c.callbackUrl != "" {
		// 构建callback参数
		callbackBody := "bucket=${bucket}&object=${object}&size=${size}"
		if len(options.CallbackVars) > 0 {
			for key := range options.CallbackVars {
				callbackBody += "&" + key + "=${x:" + key + "}"
			}
		}

		callbackMap := map[string]interface{}{
			"callbackUrl":      c.callbackUrl,
			"callbackBody":     callbackBody,
			"callbackBodyType": "application/x-www-form-urlencoded",
		}

		// 序列化并Base64编码callback参数
		callbackJSON, err := json.Marshal(callbackMap)
		if err != nil {
			c.logger.Error("序列化callback参数失败", "error", err)
			return nil, fmt.Errorf("序列化callback参数失败: %v", err)
		}
		callbackBase64 := base64.StdEncoding.EncodeToString(callbackJSON)
		fields["x-oss-callback"] = callbackBase64

		// 如果有自定义变量，构建callback-var参数
		if len(options.CallbackVars) > 0 {
			callbackVarMap := make(map[string]string)
			for key, value := range options.CallbackVars {
				callbackVarMap["x:"+key] = value
			}

			// 序列化并Base64编码callback-var参数
			callbackVarJSON, err := json.Marshal(callbackVarMap)
			if err != nil {
				c.logger.Error("序列化callback-var参数失败", "error", err)
				return nil, fmt.Errorf("序列化callback-var参数失败: %v", err)
			}
			callbackVarBase64 := base64.StdEncoding.EncodeToString(callbackVarJSON)
			fields["x-oss-callback-var"] = callbackVarBase64
		}

		c.logger.Info("已添加回调参数", "callback_url", c.callbackUrl, "callback_vars", options.CallbackVars)
	}

	// 构建上传URL
	uploadURL := fmt.Sprintf("https://%s.oss-%s.aliyuncs.com/", c.bucketName, c.region)

	response := &PostPolicyResponse{
		URL:       uploadURL,
		Fields:    fields,
		Policy:    stringToSign,
		Signature: signature,
	}

	c.logger.Info("生成表单上传policy成功",
		"object_name", options.ObjectName,
		"expiration", expiration.Format("2006-01-02T15:04:05.000Z"),
		"content_length_min", options.ContentLengthMin,
		"content_length_max", options.ContentLengthMax,
		"callback_enabled", options.EnableCallback,
		"url", uploadURL)

	return response, nil
}

// generateSignature 生成签名
func (c *Client) generateSignature(stringToSign, accessKeySecret, date, region string) (string, error) {
	const product = "oss"

	// 生成签名密钥
	hmacHash := func() hash.Hash { return sha256.New() }
	signingKey := "aliyun_v4" + accessKeySecret

	// 第一步：HMAC-SHA256(key=signingKey, data=date)
	h1 := hmac.New(hmacHash, []byte(signingKey))
	io.WriteString(h1, date)
	h1Key := h1.Sum(nil)

	// 第二步：HMAC-SHA256(key=h1Key, data=region)
	h2 := hmac.New(hmacHash, h1Key)
	io.WriteString(h2, region)
	h2Key := h2.Sum(nil)

	// 第三步：HMAC-SHA256(key=h2Key, data=product)
	h3 := hmac.New(hmacHash, h2Key)
	io.WriteString(h3, product)
	h3Key := h3.Sum(nil)

	// 第四步：HMAC-SHA256(key=h3Key, data="aliyun_v4_request")
	h4 := hmac.New(hmacHash, h3Key)
	io.WriteString(h4, "aliyun_v4_request")
	h4Key := h4.Sum(nil)

	// 计算Post签名
	h := hmac.New(hmacHash, h4Key)
	io.WriteString(h, stringToSign)
	signature := hex.EncodeToString(h.Sum(nil))

	return signature, nil
}

// GenerateSimplePostPolicy 生成简单的表单上传policy（常用场景的快捷方法）
func (c *Client) GenerateSimplePostPolicy(ctx context.Context, objectName string, expirationDuration time.Duration, envid string) (*PostPolicyResponse, error) {
	options := GeneratePostPolicyOptions{
		ObjectName:         objectName,
		ExpirationDuration: expirationDuration,
		ContentLengthMin:   1,
		ContentLengthMax:   100 * 1024 * 1024, // 100MB
		EnableCallback:     envid != "",       // 如果传入了envid就启用回调
		CallbackVars: map[string]string{
			"envid": envid,
		},
	}

	return c.GeneratePostPolicy(ctx, options)
}
