package oss

import (
	"crypto"
	"crypto/md5"
	"crypto/rsa"
	"crypto/x509"
	"encoding/base64"
	"encoding/pem"
	"net/url"
)

// OSS回调验证用的固定公钥
const ossCallbackPublicKey = `-----BEGIN PUBLIC KEY-----
MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAKs/JBGzwUB2aVht4crBx3oIPBLNsjGs
C0fTXv+nvlmklvkcolvpvXLTjaxUHR3W9LXxQ2EHXAJfCB+6H2YF1k8CAwEAAQ==
-----END PUBLIC KEY-----`

// VerifyOSSCallback 验证OSS回调签名
func VerifyOSSCallback(authorization, path, queryString, body string) bool {
	// 1. Base64解码签名
	signature, err := base64.StdEncoding.DecodeString(authorization)
	if err != nil {
		return false
	}

	// 2. URL解码path
	decodedPath, err := url.QueryUnescape(path)
	if err != nil {
		return false
	}

	// 3. 构造待验证字符串：url_decode(path) + query_string + '\n' + body
	var signStr string
	if queryString != "" {
		signStr = decodedPath + "?" + queryString + "\n" + body
	} else {
		signStr = decodedPath + "\n" + body
	}

	// 4. 验证签名
	return verifySignature(signStr, signature)
}

// verifySignature 使用RSA公钥验证签名
func verifySignature(signStr string, signature []byte) bool {
	// 1. 解析公钥
	publicKey, err := parsePublicKey()
	if err != nil {
		return false
	}

	// 2. 计算MD5哈希
	hash := md5.Sum([]byte(signStr))

	// 3. 验证签名
	err = rsa.VerifyPKCS1v15(publicKey, crypto.MD5, hash[:], signature)
	return err == nil
}

// parsePublicKey 解析固定的公钥
func parsePublicKey() (*rsa.PublicKey, error) {
	// 解析PEM格式的公钥
	block, _ := pem.Decode([]byte(ossCallbackPublicKey))
	if block == nil {
		return nil, nil
	}

	// 解析公钥
	pub, err := x509.ParsePKIXPublicKey(block.Bytes)
	if err != nil {
		return nil, err
	}

	// 转换为RSA公钥
	rsaPub, ok := pub.(*rsa.PublicKey)
	if !ok {
		return nil, nil
	}

	return rsaPub, nil
}
