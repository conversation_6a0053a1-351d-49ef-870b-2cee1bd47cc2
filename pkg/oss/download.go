package oss

import (
	"context"
	"fmt"
	"io"
	"time"

	"github.com/aliyun/alibabacloud-oss-go-sdk-v2/oss"
)

// DownloadResult 下载结果结构
type DownloadResult struct {
	Body          io.ReadCloser     `json:"-"`              // 文件流
	ContentLength *int64            `json:"content_length"` // 文件大小
	ContentType   *string           `json:"content_type"`   // 文件类型
	ETag          *string           `json:"etag"`           // 文件ETag
	LastModified  *string           `json:"last_modified"`  // 最后修改时间
	Metadata      map[string]string `json:"metadata"`       // 文件元数据
}

// DownloadObject 下载对象（返回流，支持实时传输）
func (c *Client) DownloadObject(ctx context.Context, objectName string) (*DownloadResult, error) {
	// 创建获取对象的请求
	request := &oss.GetObjectRequest{
		Bucket: oss.Ptr(c.bucketName),
		Key:    oss.Ptr(objectName),
	}

	// 执行获取对象的操作
	result, err := c.client.GetObject(ctx, request)
	if err != nil {
		c.logger.Error("下载对象失败", "error", err, "object_name", objectName)
		return nil, fmt.Errorf("下载对象失败: %v", err)
	}

	// 构建下载结果
	downloadResult := &DownloadResult{
		Body:          result.Body,
		ContentLength: &result.ContentLength,
		ContentType:   result.ContentType,
		ETag:          result.ETag,
		LastModified:  timeToStringPtr(result.LastModified),
		Metadata:      result.Metadata,
	}

	c.logger.Info("开始流式下载对象",
		"object_name", objectName,
		"content_length", result.ContentLength,
		"content_type", getStringValue(result.ContentType))

	return downloadResult, nil
}

// CheckObjectExists 检查对象是否存在
func (c *Client) CheckObjectExists(ctx context.Context, objectName string) (bool, error) {
	// 创建头部请求
	request := &oss.HeadObjectRequest{
		Bucket: oss.Ptr(c.bucketName),
		Key:    oss.Ptr(objectName),
	}

	// 执行头部请求
	_, err := c.client.HeadObject(ctx, request)
	if err != nil {
		// 检查是否是404错误（对象不存在）
		if IsNotFoundError(err) {
			return false, nil
		}
		c.logger.Error("检查对象存在性失败", "error", err, "object_name", objectName)
		return false, fmt.Errorf("检查对象存在性失败: %v", err)
	}

	return true, nil
}

// GetObjectInfo 获取对象信息（不下载内容）
func (c *Client) GetObjectInfo(ctx context.Context, objectName string) (*DownloadResult, error) {
	// 创建头部请求
	request := &oss.HeadObjectRequest{
		Bucket: oss.Ptr(c.bucketName),
		Key:    oss.Ptr(objectName),
	}

	// 执行头部请求
	result, err := c.client.HeadObject(ctx, request)
	if err != nil {
		c.logger.Error("获取对象信息失败", "error", err, "object_name", objectName)
		return nil, fmt.Errorf("获取对象信息失败: %v", err)
	}

	// 构建对象信息
	info := &DownloadResult{
		Body:          nil, // 头部请求不返回内容
		ContentLength: &result.ContentLength,
		ContentType:   result.ContentType,
		ETag:          result.ETag,
		LastModified:  timeToStringPtr(result.LastModified),
		Metadata:      result.Metadata,
	}

	c.logger.Info("获取对象信息成功",
		"object_name", objectName,
		"content_length", result.ContentLength,
		"content_type", getStringValue(result.ContentType))

	return info, nil
}

// 工具函数：安全获取int64指针的值
func getInt64Value(ptr *int64) int64 {
	if ptr == nil {
		return 0
	}
	return *ptr
}

// 工具函数：安全获取string指针的值
func getStringValue(ptr *string) string {
	if ptr == nil {
		return ""
	}
	return *ptr
}

// 工具函数：将time.Time指针转换为string指针
func timeToStringPtr(t *time.Time) *string {
	if t == nil {
		return nil
	}
	str := t.Format(time.RFC3339)
	return &str
}

// IsNotFoundError 检查是否是404错误
func IsNotFoundError(err error) bool {
	if err == nil {
		return false
	}
	// 检查错误字符串中是否包含"NoSuchKey"或"404"等关键词
	errStr := err.Error()
	return contains(errStr, "NoSuchKey") || contains(errStr, "404") || contains(errStr, "not found")
}

// 工具函数：检查字符串是否包含子字符串
func contains(str, substr string) bool {
	return len(str) >= len(substr) &&
		(str == substr ||
			(len(str) > len(substr) &&
				containsSubstring(str, substr)))
}

func containsSubstring(str, substr string) bool {
	for i := 0; i <= len(str)-len(substr); i++ {
		if str[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
