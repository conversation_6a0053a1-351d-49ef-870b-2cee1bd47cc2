package oss

import (
	"context"
	"fmt"

	"github.com/aliyun/alibabacloud-oss-go-sdk-v2/oss"
)

// DeleteResult 删除结果结构
type DeleteResult struct {
	ObjectName   string `json:"object_name"`   // 对象名称
	DeleteMarker bool   `json:"delete_marker"` // 删除标记
	VersionId    string `json:"version_id"`    // 版本ID
}

// DeleteObject 删除对象
func (c *Client) DeleteObject(ctx context.Context, objectName string) (*DeleteResult, error) {
	// 创建删除对象的请求
	request := &oss.DeleteObjectRequest{
		Bucket: oss.Ptr(c.bucketName),
		Key:    oss.Ptr(objectName),
	}

	// 执行删除对象的操作
	result, err := c.client.DeleteObject(ctx, request)
	if err != nil {
		c.logger.Error("删除对象失败", "error", err, "object_name", objectName)
		return nil, fmt.Errorf("删除对象失败: %v", err)
	}

	// 构建删除结果
	deleteResult := &DeleteResult{
		ObjectName:   objectName,
		DeleteMarker: result.DeleteMarker,
		VersionId:    getStringValue(result.VersionId),
	}

	c.logger.Info("删除对象成功",
		"object_name", objectName,
		"delete_marker", result.DeleteMarker)

	return deleteResult, nil
}

// DeleteObjectIfExists 删除对象（如果存在）
func (c *Client) DeleteObjectIfExists(ctx context.Context, objectName string) (*DeleteResult, error) {
	// 先检查对象是否存在
	exists, err := c.CheckObjectExists(ctx, objectName)
	if err != nil {
		c.logger.Error("检查对象存在性失败", "error", err, "object_name", objectName)
		return nil, fmt.Errorf("检查对象存在性失败: %v", err)
	}

	if !exists {
		c.logger.Warn("对象不存在，无需删除", "object_name", objectName)
		return &DeleteResult{
			ObjectName:   objectName,
			DeleteMarker: false,
			VersionId:    "",
		}, nil
	}

	// 对象存在，执行删除
	return c.DeleteObject(ctx, objectName)
}
