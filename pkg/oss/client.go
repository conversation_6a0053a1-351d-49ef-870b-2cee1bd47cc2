package oss

import (
	"fp-browser/pkg/log"
	"github.com/aliyun/alibabacloud-oss-go-sdk-v2/oss"
	"github.com/aliyun/alibabacloud-oss-go-sdk-v2/oss/credentials"
	"github.com/spf13/viper"
)

// Client OSS客户端包装器
type Client struct {
	client              *oss.Client
	credentialsProvider credentials.CredentialsProvider
	bucketName          string
	region              string
	callbackUrl         string
	logger              *log.Logger
}

// NewOSS 创建OSS客户端（使用viper配置）
func NewOSS(conf *viper.Viper, l *log.Logger) *Client {
	// 从配置中获取OSS相关参数
	region := conf.GetString("oss.region")
	bucketName := conf.GetString("oss.bucket_name")
	accessKeyID := conf.GetString("oss.access_key_id")
	accessKeySecret := conf.GetString("oss.access_key_secret")
	callbackUrl := conf.GetString("oss.callback_url")

	// 检查必要参数
	if region == "" {
		l.Error("OSS region配置为空")
		panic("OSS region不能为空")
	}

	if bucketName == "" {
		l.Error("OSS bucket_name配置为空")
		panic("OSS bucket_name不能为空")
	}

	// 创建凭证提供者
	var credentialsProvider credentials.CredentialsProvider
	if accessKeyID != "" && accessKeySecret != "" {
		credentialsProvider = credentials.NewStaticCredentialsProvider(accessKeyID, accessKeySecret)
		l.Info("使用静态凭证初始化OSS客户端")
	} else {
		credentialsProvider = credentials.NewEnvironmentVariableCredentialsProvider()
		l.Info("使用环境变量凭证初始化OSS客户端")
	}

	// 创建配置
	cfg := oss.LoadDefaultConfig().
		WithCredentialsProvider(credentialsProvider).
		WithRegion(region)

	// 创建OSS客户端
	client := oss.NewClient(cfg)

	l.Info("OSS客户端创建成功", "region", region, "bucket", bucketName)

	return &Client{
		client:              client,
		credentialsProvider: credentialsProvider,
		bucketName:          bucketName,
		region:              region,
		callbackUrl:         callbackUrl,
		logger:              l,
	}
}

// GetClient 获取原生OSS客户端
func (c *Client) GetClient() *oss.Client {
	return c.client
}

// GetBucketName 获取bucket名称
func (c *Client) GetBucketName() string {
	return c.bucketName
}

// GetCredentialsProvider 获取凭证提供者
func (c *Client) GetCredentialsProvider() credentials.CredentialsProvider {
	return c.credentialsProvider
}

// GetCallbackUrl 获取回调URL
func (c *Client) GetCallbackUrl() string {
	return c.callbackUrl
}

// GetRegion 获取区域
func (c *Client) GetRegion() string {
	return c.region
}
