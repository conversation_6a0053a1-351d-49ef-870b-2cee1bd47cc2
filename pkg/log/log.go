package log

import (
	"context"
	"github.com/kataras/iris/v12"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/pkgerrors"
	"github.com/spf13/viper"
	"gopkg.in/natefinch/lumberjack.v2"
	"io"
	"os"
	"time"
)

const ctxLoggerKey = "zerologLogger"

// Logger 包装了zerolog的Logger
type Logger struct {
	zerolog.Logger
}

// NewLog 创建一个新的Logger实例
func NewLog(conf *viper.Viper) *Logger {
	// 配置日志文件路径
	logFilePath := conf.GetString("log.log_file_name")
	logLevel := conf.GetString("log.log_level")

	// 配置日志级别
	var level zerolog.Level
	switch logLevel {
	case "debug":
		level = zerolog.DebugLevel
	case "info":
		level = zerolog.InfoLevel
	case "warn":
		level = zerolog.WarnLevel
	case "error":
		level = zerolog.ErrorLevel
	default:
		level = zerolog.InfoLevel
	}
	zerolog.SetGlobalLevel(level)

	// 配置时间格式
	zerolog.TimeFieldFormat = time.RFC3339Nano

	// 配置错误堆栈跟踪
	zerolog.ErrorStackMarshaler = pkgerrors.MarshalStack

	// 配置日志轮转
	hook := &lumberjack.Logger{
		Filename:   logFilePath,
		MaxSize:    conf.GetInt("log.max_size"),    // 每个日志文件的最大大小，单位：MB
		MaxBackups: conf.GetInt("log.max_backups"), // 保留的旧日志文件最大数量
		MaxAge:     conf.GetInt("log.max_age"),     // 保留旧日志文件的最大天数
		Compress:   conf.GetBool("log.compress"),   // 是否压缩
	}

	// 创建多输出写入器，同时写入控制台和文件
	var writers []io.Writer
	writers = append(writers, os.Stdout)

	// 只有在指定了日志文件路径的情况下才使用文件输出
	if logFilePath != "" {
		writers = append(writers, hook)
	}

	multiWriter := io.MultiWriter(writers...)

	// 根据配置决定输出格式
	var logger zerolog.Logger
	if conf.GetString("log.encoding") == "console" {
		// 控制台格式输出
		output := zerolog.ConsoleWriter{
			Out:        multiWriter,
			TimeFormat: "2006-01-02 15:04:05.000000000",
			NoColor:    !conf.GetBool("log.colorize"),
		}
		logger = zerolog.New(output).With().Timestamp().Caller().Logger()
	} else {
		// JSON格式输出
		logger = zerolog.New(multiWriter).With().Timestamp().Caller().Logger()
	}

	// 在开发环境添加更多信息
	if conf.GetString("env") != "prod" {
		logger = logger.With().Stack().Logger()
	}

	return &Logger{logger}
}

// WithValue 向指定上下文添加字段
func (l *Logger) WithValue(ctx context.Context, key string, value interface{}) context.Context {
	newLogger := l.With().Interface(key, value).Logger()
	return context.WithValue(ctx, ctxLoggerKey, &Logger{newLogger})
}

// WithValueIris 为Iris上下文添加字段 (专门处理iris.Context)
func (l *Logger) WithValueIris(irisCtx iris.Context, key string, value interface{}) {
	newLogger := l.With().Interface(key, value).Logger()
	reqCtx := irisCtx.Request().Context()
	newCtx := context.WithValue(reqCtx, ctxLoggerKey, &Logger{newLogger})
	irisCtx.ResetRequest(irisCtx.Request().WithContext(newCtx))
}

// WithContext 从指定上下文返回一个zerolog实例
func (l *Logger) WithContext(ctx context.Context) *Logger {
	if ctxLogger, ok := ctx.Value(ctxLoggerKey).(*Logger); ok {
		return ctxLogger
	}
	return l
}

// WithContextIris 从Iris上下文返回一个zerolog实例
func (l *Logger) WithContextIris(irisCtx iris.Context) *Logger {
	reqCtx := irisCtx.Request().Context()
	if ctxLogger, ok := reqCtx.Value(ctxLoggerKey).(*Logger); ok {
		return ctxLogger
	}
	return l
}

// Debug 输出调试级别日志
func (l *Logger) Debug(msg string, args ...interface{}) {
	if len(args) > 0 && len(args)%2 == 0 {
		evt := l.Logger.Debug()
		for i := 0; i < len(args); i += 2 {
			key, ok := args[i].(string)
			if ok {
				evt = evt.Interface(key, args[i+1])
			}
		}
		evt.Msg(msg)
	} else {
		l.Logger.Debug().Msg(msg)
	}
}

// Info 输出信息级别日志
func (l *Logger) Info(msg string, args ...interface{}) {
	if len(args) > 0 && len(args)%2 == 0 {
		evt := l.Logger.Info()
		for i := 0; i < len(args); i += 2 {
			key, ok := args[i].(string)
			if ok {
				evt = evt.Interface(key, args[i+1])
			}
		}
		evt.Msg(msg)
	} else {
		l.Logger.Info().Msg(msg)
	}
}

// Warn 输出警告级别日志
func (l *Logger) Warn(msg string, args ...interface{}) {
	if len(args) > 0 && len(args)%2 == 0 {
		evt := l.Logger.Warn()
		for i := 0; i < len(args); i += 2 {
			key, ok := args[i].(string)
			if ok {
				evt = evt.Interface(key, args[i+1])
			}
		}
		evt.Msg(msg)
	} else {
		l.Logger.Warn().Msg(msg)
	}
}

// Error 输出错误级别日志
func (l *Logger) Error(msg string, args ...interface{}) {
	if len(args) > 0 && len(args)%2 == 0 {
		evt := l.Logger.Error()
		for i := 0; i < len(args); i += 2 {
			key, ok := args[i].(string)
			if ok {
				evt = evt.Interface(key, args[i+1])
			}
		}
		evt.Msg(msg)
	} else {
		l.Logger.Error().Msg(msg)
	}
}

// Fatal 输出致命级别日志
func (l *Logger) Fatal(msg string, args ...interface{}) {
	if len(args) > 0 && len(args)%2 == 0 {
		evt := l.Logger.Fatal()
		for i := 0; i < len(args); i += 2 {
			key, ok := args[i].(string)
			if ok {
				evt = evt.Interface(key, args[i+1])
			}
		}
		evt.Msg(msg)
	} else {
		l.Logger.Fatal().Msg(msg)
	}
}
