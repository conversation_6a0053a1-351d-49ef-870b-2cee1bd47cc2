package smtp

import (
	"fmt"
	"github.com/wneessen/go-mail"
	"math/rand"
	"time"
)

// SendHTMLMail 发送单个HTML邮件（包含纯文本和HTML内容）
func (c *Client) SendHTMLMail(email, subject, htmlBody, textBody string) error {
	message := mail.NewMsg()

	// 设置发件人
	if err := message.FromFormat(c.fromName, c.fromAddr); err != nil {
		c.logger.Error("设置发件人失败", "error", err, "from", c.fromAddr)
		return fmt.Errorf("设置发件人失败: %w", err)
	}

	// 设置收件人
	if err := message.To(email); err != nil {
		c.logger.Error("设置收件人失败", "error", err, "email", email)
		return fmt.Errorf("设置收件人失败: %w", err)
	}

	// 设置邮件头信息
	message.SetDate()
	message.Subject(subject)

	// 设置纯文本内容
	message.SetBodyString(mail.TypeTextPlain, textBody)

	// 添加HTML内容作为替代内容
	message.AddAlternativeString(mail.TypeTextHTML, htmlBody)

	// 发送邮件
	if err := c.client.DialAndSend(message); err != nil {
		c.logger.Error("发送邮件失败", "error", err, "to", email, "subject", subject)
		return fmt.Errorf("发送邮件失败: %w", err)
	}

	c.logger.Info("邮件发送成功", "to", email, "subject", subject)
	return nil
}

// SendBulkHTMLMail 批量发送HTML邮件（包含纯文本和HTML内容）
func (c *Client) SendBulkHTMLMail(emails []string, subject, htmlBody, textBody string) error {
	if len(emails) == 0 {
		c.logger.Warn("收件人列表为空")
		return fmt.Errorf("收件人列表不能为空")
	}

	var messages []*mail.Msg
	random := rand.New(rand.NewSource(time.Now().UnixNano()))

	// 为每个收件人创建邮件
	for _, email := range emails {
		message := mail.NewMsg()
		randNum := random.Int31()

		// 设置信封发件人
		envelopeFrom := fmt.Sprintf("%s+%d", c.fromAddr, randNum)
		if err := message.EnvelopeFrom(envelopeFrom); err != nil {
			c.logger.Error("设置信封发件人失败", "error", err, "envelope_from", envelopeFrom, "recipient", email)
			return fmt.Errorf("为收件人 %s 设置信封发件人失败: %w", email, err)
		}

		// 设置发件人
		if err := message.From(c.fromAddr); err != nil {
			c.logger.Error("设置发件人失败", "error", err, "from", c.fromAddr, "recipient", email)
			return fmt.Errorf("为收件人 %s 设置发件人失败: %w", email, err)
		}

		// 设置收件人
		if err := message.To(email); err != nil {
			c.logger.Error("设置收件人失败", "error", err, "email", email)
			return fmt.Errorf("设置收件人失败: %w", err)
		}

		// 设置邮件头信息
		message.SetMessageID()
		message.SetDate()
		message.SetBulk() // 标记为批量邮件
		message.Subject(subject)

		// 设置纯文本内容
		message.SetBodyString(mail.TypeTextPlain, textBody)

		// 添加HTML内容作为替代内容
		message.AddAlternativeString(mail.TypeTextHTML, htmlBody)

		messages = append(messages, message)
	}

	// 批量发送邮件
	if err := c.client.DialAndSend(messages...); err != nil {
		c.logger.Error("批量发送邮件失败", "error", err, "count", len(emails))
		return fmt.Errorf("批量发送邮件失败: %w", err)
	}

	c.logger.Info("批量邮件发送成功", "count", len(emails), "subject", subject)
	return nil
}
