package smtp

import (
	"fp-browser/pkg/log"
	"github.com/spf13/viper"
	"github.com/wneessen/go-mail"
)

// Client SMTP客户端包装器
type Client struct {
	client   *mail.Client
	host     string
	port     int
	username string
	password string
	fromAddr string
	fromName string
	logger   *log.Logger
}

// NewSMTP 创建SMTP客户端（使用viper配置）
func NewSMTP(conf *viper.Viper, l *log.Logger) *Client {
	// 从配置中获取SMTP相关参数
	host := conf.GetString("smtp.host")
	port := conf.GetInt("smtp.port")
	username := conf.GetString("smtp.username")
	password := conf.GetString("smtp.password")
	fromAddr := conf.GetString("smtp.from_address")
	fromName := conf.GetString("smtp.from_name")

	// 检查必要参数
	if host == "" {
		l.<PERSON>("SMTP host配置为空")
		panic("SMTP host不能为空")
	}

	if port == 0 {
		port = 587 // 默认端口
		l.Info("使用默认SMTP端口", "port", port)
	}

	if username == "" {
		l.<PERSON>("SMTP username配置为空")
		panic("SMTP username不能为空")
	}

	if password == "" {
		l.Error("SMTP password配置为空")
		panic("SMTP password不能为空")
	}

	if fromAddr == "" {
		l.Error("SMTP from_address配置为空")
		panic("SMTP from_address不能为空")
	}

	// 创建SMTP客户端选项
	options := []mail.Option{
		mail.WithSMTPAuth(mail.SMTPAuthPlain),
		mail.WithTLSPolicy(mail.TLSMandatory),
		mail.WithUsername(username),
		mail.WithPassword(password),
		mail.WithPort(port),
		mail.WithSSL(),
	}

	// 创建SMTP客户端
	client, err := mail.NewClient(host, options...)
	if err != nil {
		l.Error("创建SMTP客户端失败", "error", err, "host", host, "port", port)
		panic("创建SMTP客户端失败: " + err.Error())
	}

	l.Info("SMTP客户端创建成功", "host", host, "port", port, "username", username, "from", fromAddr)

	return &Client{
		client:   client,
		host:     host,
		port:     port,
		username: username,
		password: password,
		fromAddr: fromAddr,
		logger:   l,
		fromName: fromName,
	}
}
