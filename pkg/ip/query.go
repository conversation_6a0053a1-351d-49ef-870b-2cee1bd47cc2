package ip

import "fmt"

// IPInfo IP查询结果结构体（免费版本支持的字段）
type IPInfo struct {
	CountryCode string  `json:"countryCode"`
	Country     string  `json:"country"`
	Region      string  `json:"region"`
	City        string  `json:"city"`
	Latitude    float32 `json:"latitude"`
	Longitude   float32 `json:"longitude"`
	TimeZone    string  `json:"timeZone"`
	ZipCode     string  `json:"zipCode"`
}

// QueryIP 查询IP地址信息
func (c *Client) QueryIP(ip string) (*IPInfo, error) {
	if ip == "" {
		c.logger.Error("查询IP地址为空")
		return nil, fmt.Errorf("IP地址不能为空")
	}

	c.logger.Debug("查询IP地址信息", "ip", ip)

	// 查询IP地址信息
	results, err := c.db.Get_all(ip)
	if err != nil {
		c.logger.Error("查询IP地址失败", "error", err, "ip", ip)
		return nil, fmt.Errorf("查询IP地址失败: %w", err)
	}

	// 构建返回结果（只包含免费版本支持的字段）
	info := &IPInfo{
		CountryCode: results.Country_short,
		Country:     results.Country_long,
		Region:      results.Region,
		City:        results.City,
		Latitude:    results.Latitude,
		Longitude:   results.Longitude,
		TimeZone:    results.Timezone,
		ZipCode:     results.Zipcode,
	}

	c.logger.Debug("IP地址查询成功", "ip", ip, "country", info.Country, "city", info.City)

	return info, nil
}
