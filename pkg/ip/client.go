package ip

import (
	"fp-browser/pkg/log"
	"github.com/ip2location/ip2location-go/v9"
	"github.com/spf13/viper"
)

// Client IP2Location客户端包装器
type Client struct {
	db     *ip2location.DB
	dbPath string
	logger *log.Logger
}

// NewIP2Location 创建IP2Location客户端（使用viper配置）
func NewIP2Location(conf *viper.Viper, l *log.Logger) *Client {
	// 从配置中获取数据库文件路径
	dbPath := conf.GetString("ip2location.db_path")

	// 检查必要参数
	if dbPath == "" {
		l.<PERSON>r("IP2Location数据库路径配置为空")
		panic("IP2Location数据库路径不能为空")
	}

	// 打开IP2Location数据库
	db, err := ip2location.OpenDB(dbPath)
	if err != nil {
		l.Error("打开IP2Location数据库失败", "error", err, "db_path", dbPath)
		panic("打开IP2Location数据库失败: " + err.<PERSON>rror())
	}

	l.Info("IP2Location客户端创建成功", "db_path", dbPath, "api_version", ip2location.Api_version())

	return &Client{
		db:     db,
		dbPath: dbPath,
		logger: l,
	}
}

// Close 关闭数据库连接
func (c *Client) Close() error {
	if c.db != nil {
		c.logger.Info("关闭IP2Location数据库连接")
		c.db.Close()
	}
	return nil
}
