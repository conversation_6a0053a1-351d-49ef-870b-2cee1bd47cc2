package config

import (
	"github.com/spf13/viper"
	"time"
)

type Config struct {
	Server   ServerConfig
	Database DatabaseConfig
	Log      LogConfig
	JWT      JWTConfig
	Schedule ScheduleConfig
}

type ServerConfig struct {
	Port int
	Mode string
}

type JWTConfig struct {
	Secret      string
	ExpireHours int
}

type ScheduleConfig struct {
	Timezone string
}

type SessionConfig struct {
	CookieName string
	Expire     time.Duration
	UseRedis   bool
	Redis      RedisConfig
}

type RedisConfig struct {
	Host         string
	Port         int
	Password     string
	DB           int
	MinIdleConns int
	PoolSize     int
}

type SmtpConfig struct {
	Host      string
	Port      int
	Username  string
	Password  string
	FromName  string
	FromEmail string
}

// 数据库
type DatabaseConfig struct {
	Master   DBInstance   `mapstructure:"master"`
	Replicas []DBInstance `mapstructure:"replicas"`
	Pool     DBPoolConfig `mapstructure:"pool"`
}

type DBInstance struct {
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	User     string `mapstructure:"user"`
	Password string `mapstructure:"password"`
	DBName   string `mapstructure:"dbname"`
}

type DBPoolConfig struct {
	MaxOpenConns       int           `mapstructure:"max_open_conns"`
	MaxIdleConns       int           `mapstructure:"max_idle_conns"`
	ConnMaxLifetime    time.Duration `mapstructure:"conn_max_lifetime"`    // e.g. "1h"
	GormLogLevel       string        `mapstructure:"gorm_log_level"`       // silent, error, warn, info
	SlowQueryThreshold time.Duration `mapstructure:"slow_query_threshold"` // e.g. "300ms"
}

// 日志组件
type LogConfig struct {
	Level      string `mapstructure:"level"`       // debug, info, warn, error
	File       string `mapstructure:"file"`        // 文件路径，"" 为 stdout
	WithCaller bool   `mapstructure:"with_caller"` // 是否打印调用者信息
	Pretty     bool   `mapstructure:"pretty"`      // 控制台美化输出
}

func LoadConfig(path string) (*Config, error) {
	viper.SetConfigFile(path)
	viper.SetConfigType("yaml")

	if err := viper.ReadInConfig(); err != nil {
		return nil, err
	}

	var cfg Config
	if err := viper.Unmarshal(&cfg); err != nil {
		return nil, err
	}

	return &cfg, nil
}
