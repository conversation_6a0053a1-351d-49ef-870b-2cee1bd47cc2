package view_model

import (
	"fp-browser/internal/dao/model"
)

// User 聚合了用户、团队、角色、钱包、订阅信息
type User struct {
	model.User
	Team               *model.Team             `json:"team"`                 // 用户所属团队
	Role               *model.Role             `json:"role"`                 // 用户绑定角色
	WalletAmount       int64                   `json:"wallet_amount"`        // 钱包余额（单位：分）
	Subscription       *model.UserSubscription `json:"subscription"`         // 用户订阅信息（可以为 nil）
	EnvironmentSizeSum int64                   `json:"environment_size_sum"` // 团队环境大小总和
}
