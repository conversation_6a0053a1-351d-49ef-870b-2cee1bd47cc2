package view_model

import "time"

type ProxyInfo struct {
	Name     string `json:"name"`
	Type     int16  `json:"type"`
	Address  string `json:"address"`
	Port     int16  `json:"port"`
	Username string `json:"username"`
	Password string `json:"password"`
}

// EnvironmentListItem 用于环境列表展示（不包含敏感信息）
type EnvironmentListItem struct {
	ID        int32      `json:"id"`
	TeamID    int32      `json:"team_id"`
	Name      string     `json:"name"`
	UserID    int32      `json:"user_id"`
	GroupID   int32      `json:"group_id"`
	GroupName string     `json:"group_name"`
	Proxy     ProxyInfo  `json:"proxy"`
	ProxyType int16      `json:"proxy_type"`
	Platform  string     `json:"platform"`
	Storage   string     `json:"storage"`
	Tag       string     `json:"tag"`
	Comment   *string    `json:"comment"`
	Sort      *int16     `json:"sort"`
	Size      *int32     `json:"size"`
	CreatedAt *time.Time `json:"created_at"`
	UpdatedAt *time.Time `json:"updated_at"`
}

// EnvironmentWithProxy 用于环境详情展示（包含完整信息）
type EnvironmentWithProxy struct {
	ID         int32      `json:"id"`
	TeamID     int32      `json:"team_id"`
	Name       string     `json:"name"`
	UserID     int32      `json:"user_id"`
	GroupID    int32      `json:"group_id"`
	GroupName  string     `json:"group_name"`
	Proxy      ProxyInfo  `json:"proxy"`
	ProxyType  int16      `json:"proxy_type"`
	Platform   string     `json:"platform"`
	Parameters string     `json:"parameters"` // 详情中包含参数
	Storage    string     `json:"storage"`
	Tag        string     `json:"tag"`
	Comment    *string    `json:"comment"` // 详情中包含备注
}
