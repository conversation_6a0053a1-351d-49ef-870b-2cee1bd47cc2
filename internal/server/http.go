package server

import (
	authHandler "fp-browser/internal/handler/auth"
	sharedHandler "fp-browser/internal/handler/shared"
	userHandler "fp-browser/internal/handler/user"
	"fp-browser/internal/middleware"
	"fp-browser/pkg/log"
	httpserver "fp-browser/pkg/server/http"

	"github.com/kataras/iris/v12"
	"github.com/spf13/viper"
)

func NewHTTPServer(
	logger *log.Logger,
	conf *viper.Viper,
	userCommissionHandler *userHandler.CommissionHandler,
	userEnvironmentHandler *userHandler.EnvironmentHandler,
	userGroupHandler *userHandler.GroupHandler,
	userLoginLogHandler *userHandler.LoginLogHandler,
	userOperationLogHandler *userHandler.OperationLogHandler,
	userOrderHandler *userHandler.OrderHandler,
	userRoleHandler *userHandler.RoleHandler,
	userSelfHostProxyHandler *userHandler.SelfHostProxyHandler,
	userSubscriptionHandler *userHandler.Subscription<PERSON>and<PERSON>,
	userTeamHandler *userHandler.TeamHandler,
	userTeamIPWhitelistHandler *userHandler.TeamIPWhitelistHandler,
	userUserHandler *userHandler.UserHandler,
	userWalletHandler *userHandler.WalletHandler,
	userUserSubscriptionHandler *userHandler.UserSubscriptionHandler,
	authLoginHandler *authHandler.LoginHandler,
	authRegisterHandler *authHandler.RegisterHandler,
	sharedCaptchaHandler *sharedHandler.CaptchaHandler,
	sharedEmailHandler *sharedHandler.EmailHandler,
	// 添加其他handler...
) *httpserver.Server {
	app := iris.New()

	// 配置应用
	app.Configure(iris.WithOptimizations)

	s := httpserver.NewServer(
		app,
		logger,
		httpserver.WithServerHost(conf.GetString("http.host")),
		httpserver.WithServerPort(conf.GetInt("http.port")),
	)

	// 初始化session管理器
	sessionManager := middleware.InitSessionManager(conf, logger)

	// 注册全局中间件
	app.UseRouter(middleware.CORSMiddleware(conf))
	app.UseRouter(middleware.SessionMiddleware(sessionManager))

	// API路由
	v1 := app.Party("/api/v1")
	{
		// 无需认证的路由组
		publicAPI := v1.Party("/")
		{
			// 健康检查
			publicAPI.Get("/health", func(ctx iris.Context) {
				ctx.JSON(iris.Map{"status": "ok"})
			})

			// 认证相关路由
			publicAPI.Post("/login", authLoginHandler.Login)
			publicAPI.Post("/auth/register", authRegisterHandler.Register)

			// 临时开发API：修复用户数据
			// TODO: 生产环境需要删除
			publicAPI.Post("/dev/fix-user-data", func(ctx iris.Context) {
				var request struct {
					UserID int32 `json:"user_id"`
				}
				if err := ctx.ReadJSON(&request); err != nil {
					ctx.JSON(iris.Map{"error": "无效的请求"})
					return
				}

				// 这里可以添加修复逻辑
				ctx.JSON(iris.Map{
					"message":    "用户数据修复功能正在开发中",
					"userID":     request.UserID,
					"suggestion": "请检查数据库中users表的team_id和role_id字段，确保它们不为0且对应的团队和角色存在"})
			})

			// 验证码相关路由
			publicAPI.Post("/captcha/generate", sharedCaptchaHandler.Generate)
			publicAPI.Post("/captcha/verify", sharedCaptchaHandler.Verify)

			// 邮件验证相关路由
			publicAPI.Post("/email/send-login-verification", sharedEmailHandler.SendLoginVerification)
			publicAPI.Post("/email/send-register-verification", sharedEmailHandler.SendRegisterVerification)
			publicAPI.Post("/email/send-reset-verification", sharedEmailHandler.SendResetPasswordVerification)
			publicAPI.Post("/email/send-update-verification", sharedEmailHandler.SendUpdateEmailVerification)
		}

		// 需要认证的路由组
		userAPI := v1.Party("/", middleware.AuthRequired())
		{
			// 用户信息相关
			userAPI.Get("/users/profile", userUserHandler.GetProfile)
			userAPI.Put("/users/profile", userUserHandler.UpdateProfile)

			// 登录日志
			userAPI.Get("/login-logs", userLoginLogHandler.GetLoginLogs)

			// 操作日志
			userAPI.Get("/operation-logs", userOperationLogHandler.GetOperationLogs)

			// 佣金相关
			userAPI.Post("/commissions", userCommissionHandler.CreateCommission)
			userAPI.Get("/commissions", userCommissionHandler.GetTransactions)
			userAPI.Get("/commissions/withdrawable", userCommissionHandler.GetWithdrawalTransactions)
			userAPI.Get("/commissions/summary", userCommissionHandler.GetCommissionSummary)

			// 钱包相关
			userAPI.Post("/wallet/transactions", userWalletHandler.CreateTransaction)
			userAPI.Get("/wallet/transactions", userWalletHandler.GetTransactions)
			userAPI.Get("/wallet/balance", userWalletHandler.GetBalance)

			// 订阅相关
			userAPI.Get("/subscriptions/{id:int}", userUserSubscriptionHandler.GetSubscriptionByID)
			userAPI.Get("/teams/{team_id:int}/subscriptions/active", userUserSubscriptionHandler.GetActiveSubscriptionByTeam)
			userAPI.Get("/teams/{team_id:int}/subscriptions", userUserSubscriptionHandler.GetAllSubscriptionsByTeam)
			userAPI.Post("/subscriptions", userUserSubscriptionHandler.CreateSubscription)
			userAPI.Put("/subscriptions/{id:int}", userUserSubscriptionHandler.UpdateSubscription)

			// 订阅产品相关
			userAPI.Get("/subscriptions", userSubscriptionHandler.GetAllSubscriptions)
			userAPI.Post("/subscriptions/purchase", userSubscriptionHandler.PurchaseSubscription)

			// 角色管理
			userAPI.Post("/roles", userRoleHandler.CreateRole)
			userAPI.Get("/roles", userRoleHandler.GetRoles)
			userAPI.Put("/roles", userRoleHandler.UpdateRole)
			userAPI.Delete("/roles", userRoleHandler.DeleteRole)

			// 分组管理
			userAPI.Post("/groups", userGroupHandler.CreateGroup)
			userAPI.Get("/groups", userGroupHandler.GetGroups)
			userAPI.Put("/groups", userGroupHandler.UpdateGroup)
			userAPI.Delete("/groups", userGroupHandler.DeleteGroup)

			// 团队IP白名单
			userAPI.Post("/team-ip-whitelists", userTeamIPWhitelistHandler.CreateIPWhitelist)
			userAPI.Post("/team-ip-whitelists/batch", userTeamIPWhitelistHandler.CreateIPWhitelists)
			userAPI.Delete("/team-ip-whitelists", userTeamIPWhitelistHandler.DeleteIPWhitelists)
			userAPI.Get("/team-ip-whitelists", userTeamIPWhitelistHandler.GetIPWhitelists)
			userAPI.Get("/team-ip-whitelists/pending-logins", userTeamIPWhitelistHandler.GetPendingLoginRequests)
			userAPI.Post("/team-ip-whitelists/authorize-login", userTeamIPWhitelistHandler.AuthorizeLoginRequest)

			// 自托管代理
			userAPI.Post("/self-host-proxies", userSelfHostProxyHandler.CreateProxy)
			userAPI.Post("/self-host-proxies/batch", userSelfHostProxyHandler.CreateProxies)
			userAPI.Put("/self-host-proxies", userSelfHostProxyHandler.UpdateProxy)
			userAPI.Put("/self-host-proxies/batch", userSelfHostProxyHandler.UpdateProxies)
			userAPI.Delete("/self-host-proxies", userSelfHostProxyHandler.DeleteProxy)
			userAPI.Get("/self-host-proxies/{id:int}", userSelfHostProxyHandler.GetProxyByID)
			userAPI.Get("/self-host-proxies", userSelfHostProxyHandler.GetSelfHostProxies)

			// 环境管理
			userAPI.Post("/environments", userEnvironmentHandler.CreateEnvironment)
			userAPI.Get("/environments", userEnvironmentHandler.GetEnvironments)
			userAPI.Get("/environments/deleted", userEnvironmentHandler.GetDeletedEnvironments)
			userAPI.Put("/environments/restore", userEnvironmentHandler.RestoreEnvironment)
			userAPI.Delete("/environments/permanent", userEnvironmentHandler.PermanentDeleteEnvironment)
			userAPI.Get("/environments/{id:int}", userEnvironmentHandler.GetEnvironmentByID)
			userAPI.Put("/environments", userEnvironmentHandler.UpdateEnvironment)
			userAPI.Put("/environments/proxy", userEnvironmentHandler.UpdateEnvironmentProxy)
			userAPI.Delete("/environments", userEnvironmentHandler.DeleteEnvironments)
			userAPI.Post("/environments/{id:int}/refresh", userEnvironmentHandler.RefreshEnvironmentRedisRecord)
			userAPI.Delete("/environments/{id:int}/remove", userEnvironmentHandler.RemoveEnvironmentRedisRecord)
			userAPI.Get("/environments/{id:int}/download", userEnvironmentHandler.DownloadEnvironmentFile)

			userAPI.Post("/environments/file/upload-url", userEnvironmentHandler.GenerateUploadURL)
			userAPI.Delete("/environments/file", userEnvironmentHandler.DeleteEnvironmentFile)

			// 团队用户管理
			userAPI.Get("/teams/users", userTeamHandler.GetUserList)
			userAPI.Post("/teams/users", userTeamHandler.AddUsersToTeam)
			userAPI.Delete("/teams/users", userTeamHandler.DeleteUsersFromTeam)
			userAPI.Put("/teams/users", userTeamHandler.UpdateUsersInTeam)
			userAPI.Put("/teams", userTeamHandler.UpdateTeam)

			// 订单相关
			userAPI.Get("/orders/detail", userOrderHandler.GetOrder)
			userAPI.Get("/orders", userOrderHandler.GetOrders)
			userAPI.Post("/orders/cancel", userOrderHandler.CancelOrder)
			userAPI.Post("/orders", userOrderHandler.CreateOrder)
			userAPI.Post("/orders/price", userOrderHandler.GetOrderPrice)
		}
	}

	return s
}
