package model

type SelfHostProxy struct {
	ID            uint   `gorm:"primaryKey"`        // 自增ID
	Name          string `gorm:"type:varchar(20)"`  // 代理名称，用于标识代理实例
	Type          uint8  `gorm:"not null"`          // 代理类型 (1: HTTP, 2: HTTPS, 3: SOCKS5)
	Host          string `gorm:"type:varchar(255)"` // 主机地址，可以是 IP 或域名
	Port          uint16 `gorm:"not null"`          // 端口号
	Username      string `gorm:"type:varchar(50)"`  // 用户名，可选
	Password      string `gorm:"type:varchar(50)"`  // 密码，可选
	TeamID        uint   `gorm:"not null;index"`    // 团队ID，添加索引以加快查询速度
	EnvironmentID uint   `gorm:"not null"`          // 环境ID，添加索引以加快查询速度
}
