package model

import "time"

type Team struct {
	ID        uint      `gorm:"primaryKey" json:"id"`                                     // 团队ID
	Name      string    `gorm:"type:varchar(50);not null" json:"name"`                    // 团队名称，限制为50个字符
	OwnerID   uint      `gorm:"not null;index" json:"owner_id"`                           // 团队创建者的用户ID，添加索引以加速查询
	CreatedAt time.Time `gorm:"type:timestamp;autoCreateTime;not null" json:"created_at"` // 团队创建时间，改为TIMESTAMP类型
	UpdatedAt time.Time `gorm:"type:timestamp;autoUpdateTime;not null" json:"updated_at"` // 团队更新时间，改为TIMESTAMP类型
}
