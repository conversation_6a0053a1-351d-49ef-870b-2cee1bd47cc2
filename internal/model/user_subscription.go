package model

import "time"

type UserSubscription struct {
	ID                uint      `gorm:"primaryKey"`                             // 购买记录ID
	OrderID           uint      `gorm:"not null"`                               // 购买记录ID
	TeamID            uint      `gorm:"not null;index"`                         // 用户ID，添加索引以加速查询
	StartDate         time.Time `gorm:"type:timestamp;not null"`                // 订阅开始日期，使用TIMESTAMP
	EndDate           time.Time `gorm:"type:timestamp;not null"`                // 订阅结束日期，使用TIMESTAMP
	EnvironmentsCount uint16    `gorm:"not null"`                               // 环境数量，改为uint16以节省存储空间
	MembersCount      uint16    `gorm:"not null"`                               // 成员数量，改为uint16以节省存储空间
	TotalPrice        int64     `gorm:"not null"`                               // 购买时的总价，以分为单位
	Status            uint8     `gorm:"type:tinyint;not null"`                  // 状态（1: Active, 2: Expired, 3: Cancelled, 4: Pending），改为tinyint
	CreatedAt         time.Time `gorm:"type:timestamp;autoCreateTime;not null"` // 创建时间，使用TIMESTAMP
	UpdatedAt         time.Time `gorm:"type:timestamp;autoUpdateTime;not null"` // 更新时间，使用TIMESTAMP
}
