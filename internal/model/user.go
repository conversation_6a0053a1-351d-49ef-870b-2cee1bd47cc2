package model

import (
	"time"
)

type User struct {
	ID                       uint      `gorm:"primaryKey" json:"id"`                                         // 用户ID，主键
	UserName                 string    `gorm:"type:varchar(20);unique" json:"username"`                      // 用户名，限制为20个字符以内，唯一
	Email                    string    `gorm:"type:varchar(100);index;not null" json:"email"`                // 用户邮箱，唯一，不允许为空
	Password                 string    `gorm:"type:varchar(60);not null"`                                    // 密码，不允许为空
	Telephone                string    `gorm:"type:varchar(15);index;not null" json:"telephone"`             // 电话号码，唯一，不允许为空
	IsActive                 *bool     `gorm:"type:boolean;default:true" json:"is_active"`                   // 是否启用账户，默认为启用
	IsDeleted                *bool     `gorm:"type:boolean;default:false" json:"is_deleted"`                 // 是否删除，软删除标志，默认为否
	IsTwoFactorEnabled       *bool     `gorm:"type:boolean;default:false" json:"is_two_factor_enabled"`      // 是否开启两步验证
	TwoFactorSecret          string    `gorm:"type:varchar(255)" json:"two_factor_secret"`                   // 两步验证的值（密钥）
	RealNameType             int       `gorm:"type:int;default:0" json:"real_name_type"`                     // 实名认证类型：0 未认证，1 个人，2 企业
	RealName                 string    `gorm:"type:varchar(20);not null" json:"real_name"`                   // 实名认证的姓名，限制为20个中文字符，不允许为空
	IDCardNumber             string    `gorm:"type:varchar(20);not null" json:"id_card_number"`              // 实名认证的身份证号，不允许为空
	CompanyName              string    `gorm:"type:varchar(50);not null" json:"company_name"`                // 企业名称，限制为50个中文字符，不允许为空
	CompanyUnifiedSocialCode string    `gorm:"type:varchar(18);not null" json:"company_unified_social_code"` // 企业统一社会信用代码，不允许为空
	TeamID                   uint      `gorm:"not null;index" json:"team_id"`                                // 团队ID，关联团队，不允许为空
	RoleID                   uint      `gorm:"not null;index" json:"role_id"`                                // 角色ID，关联角色表，不允许为空
	CreatedAt                time.Time `gorm:"type:timestamp;autoCreateTime" json:"created_at"`              // 记录创建时间，使用TIMESTAMP
	UpdatedAt                time.Time `gorm:"type:timestamp;autoUpdateTime" json:"updated_at"`              // 记录最近更新时间，使用TIMESTAMP
}
