package model

import "time"

type Group struct {
	ID        uint      `gorm:"primaryKey"`                    // 自增ID
	Name      string    `gorm:"type:varchar(100);not null"`    // 分组名称，限制为100个字符以内
	UserID    uint      `gorm:"not null;index"`                // 绑定的用户ID，添加索引以加速查询
	TeamID    uint      `gorm:"not null;index"`                // 绑定的团队ID，添加索引以加速查询
	CreatedAt time.Time `gorm:"type:timestamp;autoCreateTime"` // 创建时间，自动填充
	UpdatedAt time.Time `gorm:"type:timestamp;autoUpdateTime"` // 更新时间，自动填充
}
