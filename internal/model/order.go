package model

import "time"

type Order struct {
	ID            uint      `gorm:"primaryKey" json:"id"`                           // 自增ID
	OrderNumber   string    `gorm:"uniqueIndex;not null" json:"order_number"`       // 唯一订单号
	UserID        uint      `gorm:"not null;index" json:"user_id"`                  // 用户ID，添加索引以加速查询
	TeamID        uint      `gorm:"not null;index" json:"team_id"`                  // 团队ID，添加索引以加速查询
	Amount        int64     `gorm:"not null" json:"amount"`                         // 应付金额，以分为单位
	RealAmount    int64     `gorm:"not null" json:"real_amount"`                    // 实付金额，以分为单位
	Currency      string    `gorm:"type:varchar(3);not null" json:"currency"`       // 货币类型，遵循ISO 4217标准，设置为3字符 (如 CNY, USD)
	Status        uint8     `gorm:"type:tinyint;not null" json:"status"`            // 订单状态 (1: Pending, 2: Paid, 3: Failed, 4: Cancelled)
	OrderType     uint8     `gorm:"type:tinyint;not null" json:"order_type"`        // 订单类型 (1: subscription, 2: proxy, 3: recharge)
	PaymentMethod uint8     `gorm:"type:tinyint;not null" json:"payment"`           // 支付方式 (1: balance, 2: stripe, 3: alipay)
	URL           string    `gorm:"type:varchar(255)" json:"url"`                   // 支付网关提供的支付链接
	OrderContent  string    `gorm:"type:json" json:"order_content"`                 // 订单内容，存储为 JSON 字符串
	ProductIDs    []uint    `gorm:"type:json" json:"product_ids"`                   // 关联的产品ID，存储为 JSON 数组
	ExpiresAt     time.Time `gorm:"type:timestamp;index;default:CURRENT_TIMESTAMP"` // 订单过期时间，默认为当前时间，改为TIMESTAMP类型
	CreatedAt     time.Time `gorm:"type:timestamp;autoCreateTime;not null"`         // 创建时间，TIMESTAMP类型
	UpdatedAt     time.Time `gorm:"type:timestamp;autoUpdateTime;not null"`         // 更新时间，TIMESTAMP类型
}
