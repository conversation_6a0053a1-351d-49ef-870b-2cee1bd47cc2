package model

import "time"

type Forward struct {
	ID        uint      `gorm:"primaryKey"`                    // 自增ID
	Name      string    `gorm:"type:varchar(255);not null"`    // 服务器名
	Domain    string    `gorm:"type:varchar(255);not null"`    // 转发的域名或IP
	Port      uint16    `gorm:"not null"`                      // 转发的端口
	CreatedAt time.Time `gorm:"type:timestamp;autoCreateTime"` // 创建时间，使用 TIMESTAMP
	UpdatedAt time.Time `gorm:"type:timestamp;autoUpdateTime"` // 更新时间，使用 TIMESTAMP
}
