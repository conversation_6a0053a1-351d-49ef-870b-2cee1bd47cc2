package model

import "time"

type EnvironmentPricingTier struct {
	ID                  uint      `gorm:"primaryKey"`                    // 阶梯ID
	MaxQuantity         uint      `gorm:"not null"`                      // 最大数量
	MonthlyPricePerUnit int64     `gorm:"not null"`                      // 每个环境数量的月单价
	CreatedAt           time.Time `gorm:"type:timestamp;autoCreateTime"` // 创建时间
	UpdatedAt           time.Time `gorm:"type:timestamp;autoUpdateTime"` // 更新时间
}

type MemberPricingTier struct {
	ID                  uint      `gorm:"primaryKey"`                    // 阶梯ID
	MaxQuantity         uint      `gorm:"not null"`                      // 最大数量
	MonthlyPricePerUnit int64     `gorm:"not null"`                      // 每个成员数量的月单价
	CreatedAt           time.Time `gorm:"type:timestamp;autoCreateTime"` // 创建时间
	UpdatedAt           time.Time `gorm:"type:timestamp;autoUpdateTime"` // 更新时间
}

type ProxyPricing struct {
	ID          uint               `gorm:"primaryKey"`
	ProxyType   uint8              `gorm:"not null"`                 // 代理类型 (1: 住宅代理, 2: 机房代理, 3: 动态代理, etc.)
	CountryCode string             `gorm:"type:varchar(2);not null"` // 国家代码 (ISO 3166-1)
	Region      map[string]string  `gorm:"type:json;not null"`       // 地区，JSON 格式，支持多语言
	PricingData map[string]float64 `gorm:"type:json;not null"`       // 定价信息，JSON 格式，key 为时长（月），value 为价格
	CreatedAt   time.Time          `gorm:"autoCreateTime"`           // 创建时间
	UpdatedAt   time.Time          `gorm:"autoUpdateTime"`           // 更新时间
}
