package model

import "time"

type CommissionTransaction struct {
	ID              uint      `gorm:"primaryKey"`                                              // 交易ID
	UserID          uint      `gorm:"not null;index"`                                          // 推荐人用户ID，添加索引以加速查询
	Amount          int64     `gorm:"not null"`                                                // 交易金额（正数表示收入，负数表示支出）
	Currency        string    `gorm:"type:varchar(3);not null"`                                // 货币类型，ISO 4217标准，设置为3字符 (如 CNY, USD)
	TransactionType uint8     `gorm:"type:tinyint;not null"`                                   // 交易类型，改为tinyint (1: 返利, 2: 提现, 3: 退款)
	ReferenceID     uint      `gorm:"not null;index;default:0"`                                // 相关订单ID，不能为 NULL，默认为0表示无关联订单
	Description     string    `gorm:"type:varchar(100);not null"`                              // 交易描述，优化长度为100，减少冗余
	UnlockTime      time.Time `gorm:"type:timestamp;not null;index;default:CURRENT_TIMESTAMP"` // 解锁时间，使用TIMESTAMP类型，默认值为当前时间
	CreatedAt       time.Time `gorm:"type:timestamp;autoCreateTime;not null"`                  // 记录创建时间，改为TIMESTAMP类型
}
