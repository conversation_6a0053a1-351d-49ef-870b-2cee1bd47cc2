package model

import (
	"time"
)

type Admin struct {
	ID        uint      `gorm:"primaryKey" json:"id"`                             // 管理员ID，主键
	UserName  string    `gorm:"type:varchar(20);unique" json:"username"`          // 用户名，限制为20个字符以内，唯一
	Telephone string    `gorm:"type:varchar(15);index;not null" json:"telephone"` // 电话号码，唯一，不允许为空
	Email     string    `gorm:"type:varchar(100);index;not null" json:"email"`    // 管理员邮箱，唯一，不允许为空
	Password  string    `gorm:"type:varchar(60);not null" json:"password"`        // 密码，不允许为空
	IsActive  *bool     `gorm:"type:boolean;default:true" json:"is_active"`       // 是否启用账户，默认为启用
	CreatedAt time.Time `gorm:"type:timestamp;autoCreateTime" json:"created_at"`  // 记录创建时间，使用TIMESTAMP
	UpdatedAt time.Time `gorm:"type:timestamp;autoUpdateTime" json:"updated_at"`  // 记录最近更新时间，使用TIMESTAMP
}
