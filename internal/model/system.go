package model

import (
	"encoding/json"
	"strconv"
	"strings"
)

// SystemEntry 供业务层使用的类型安全结构
type SystemEntry struct {
	Key         string
	Value       any
	Description *string
}

// ToEntry 把原始 System 记录解析成类型安全的 SystemEntry
func (s *System) ToEntry() (*SystemEntry, error) {
	parser := valueParsers[strings.ToLower(deref(s.ValueType))]
	if parser == nil {
		parser = parseString // 默认 string
	}
	v, err := parser(s.Value)
	if err != nil {
		return nil, err
	}
	return &SystemEntry{
		Key:         s.Key,
		Value:       v,
		Description: s.Description,
	}, nil
}

// ---------- private: 解析策略 ----------

type valParser func(string) (any, error)

var valueParsers = map[string]valParser{
	"int":     parseInt,
	"integer": parseInt,
	"bool":    parseBool,
	"boolean": parseBool,
	"json":    parseJSON,
	"object":  parseJSON,
	"map":     parseJSON,
	"string":  parseString,
}

func parseInt(s string) (any, error)    { return strconv.Atoi(s) }
func parseBool(s string) (any, error)   { return strconv.ParseBool(s) }
func parseJSON(s string) (any, error)   { var v any; err := json.Unmarshal([]byte(s), &v); return v, err }
func parseString(s string) (any, error) { return s, nil }

func deref(ptr *string) string {
	if ptr == nil {
		return "string"
	}
	return *ptr
}
