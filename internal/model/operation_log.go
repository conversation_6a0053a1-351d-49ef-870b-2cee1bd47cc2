package model

import "time"

type OperationLog struct {
	ID        uint      `gorm:"primaryKey"`                             // 日志ID
	UserID    uint      `gorm:"not null;index"`                         // 用户ID
	TeamID    uint      `gorm:"not null;index"`                         // 团队ID
	Action    uint      `gorm:"not null"`                               // 操作内容，使用uint类型
	Target    string    `gorm:"type:varchar(100);not null"`             // 操作目标（例如目标对象名称或ID）
	Category  uint      `gorm:"not null"`                               // 操作分类
	CreatedAt time.Time `gorm:"type:timestamp;autoCreateTime;not null"` // 操作时间
}
