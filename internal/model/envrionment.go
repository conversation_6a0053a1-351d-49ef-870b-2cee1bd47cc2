package model

import "time"

type Environment struct {
	ID         uint      `gorm:"primaryKey"`                             // 环境ID
	TeamID     uint      `gorm:"not null;index"`                         // 所属团队ID，添加索引
	Name       string    `gorm:"type:varchar(100);not null"`             // 环境名称，限制为100个字符以内
	UserID     *uint     `gorm:"not null;index"`                         // 当前分配给的用户ID，添加索引
	GroupID    *uint     `gorm:"not null;index"`                         // 所属分组，关联到 Group 表，添加索引
	ProxyID    *uint     `gorm:"not null"`                               // 代理ID，关联到 Proxy 表，添加索引
	ProxyType  uint8     `gorm:"not null"`                               // 代理类型，1平台代理，2自有代理
	Platform   string    `gorm:"type:varchar(255);not null;default:''"`  // 平台
	Parameters string    `gorm:"type:json;not null"`                     // 环境参数，JSON格式存储
	Storage    string    `gorm:"type:varchar(255);not null"`             // 备份路径，aws s3地址
	CreatedAt  time.Time `gorm:"type:timestamp;autoCreateTime;not null"` // 创建时间
	UpdatedAt  time.Time `gorm:"type:timestamp;autoUpdateTime;not null"` // 更新时间
}
