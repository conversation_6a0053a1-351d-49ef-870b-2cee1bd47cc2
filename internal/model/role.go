package model

import "time"

type Role struct {
	ID          uint      `gorm:"primaryKey"`                             // 角色ID
	Name        string    `gorm:"type:varchar(50);not null"`              // 角色名称，限制为50个字符，避免过长
	TeamID      uint      `gorm:"not null;index"`                         // 所属团队ID，添加索引以加速查询
	Permissions string    `gorm:"type:json;not null"`                     // 权限数据，JSON格式存储
	CreatedAt   time.Time `gorm:"type:timestamp;autoCreateTime;not null"` // 创建时间，改为TIMESTAMP类型
	UpdatedAt   time.Time `gorm:"type:timestamp;autoUpdateTime;not null"` // 更新时间，改为TIMESTAMP类型
}
