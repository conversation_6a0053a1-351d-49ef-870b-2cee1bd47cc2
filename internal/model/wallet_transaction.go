package model

import "time"

type WalletTransaction struct {
	ID              uint      `gorm:"primaryKey"`                             // 交易ID
	UserID          uint      `gorm:"not null;index"`                         // 用户ID，添加索引以加速查询
	Amount          int64     `gorm:"not null"`                               // 交易金额（正数表示收入，负数表示支出）
	Currency        string    `gorm:"type:varchar(3);not null"`               // 货币类型，ISO 4217标准，改为3字符
	TransactionType uint8     `gorm:"type:tinyint;not null"`                  // 交易类型（1: 充值, 2: 消费, 3: 退款等），用整数代替字符串
	ReferenceID     uint      `gorm:"not null;index;default:0"`               // 相关订单ID，不能为 NULL，默认为0
	Description     string    `gorm:"type:varchar(100);not null"`             // 交易描述，长度缩减为100字符
	CreatedAt       time.Time `gorm:"type:timestamp;autoCreateTime;not null"` // 创建时间，改为TIMESTAMP类型
}
