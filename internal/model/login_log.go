package model

import "time"

type LoginLog struct {
	ID         uint      `gorm:"primaryKey"`                             // 日志ID
	UserID     uint      `gorm:"not null;index"`                         // 用户ID，添加索引以加速查询
	TeamID     uint      `gorm:"not null;index"`                         // 团队ID，添加索引以加速查询
	LoginIP    uint32    `gorm:"not null"`                               // 用户登录的IP地址，存储为整型以提高效率
	IPLocation string    `gorm:"type:varchar(100);not null"`             // 用户登录IP的地理位置（限制为100字符）
	CreatedAt  time.Time `gorm:"type:timestamp;autoCreateTime;not null"` // 登录时间，使用TIMESTAMP类型
}
