package model

import (
	"github.com/google/uuid"
	"time"
)

type Proxy struct {
	ID            uint      `gorm:"primaryKey"`                    // 自增ID
	Name          string    `gorm:"type:varchar(30)"`              // 名称
	IPAddress     uint32    `gorm:"not null"`                      // 使用整型存储IP地址
	Port          uint16    `gorm:"not null"`                      // 端口号，使用uint16
	SocksUsername string    `gorm:"type:varchar(30)"`              // Socks用户名，最大长度调整为30
	SocksPassword string    `gorm:"type:varchar(30)"`              // Socks密码，最大长度调整为30
	TeamID        uint      `gorm:"not null"`                      // 分配给的团队ID
	EnvironmentID uint      `gorm:"not null"`                      // 分配给的环境ID
	NodeID        uint      `gorm:"not null"`                      // 所属节点ID
	Region        string    `gorm:"type:varchar(100);not null"`    // 地区字段
	TrafficLimit  int64     `gorm:"not null"`                      // 流量额度（以字节为单位）
	Unlimited     *bool     `gorm:"default:false"`                 // 是否为无限流量
	IsForwarding  *bool     `gorm:"default:false"`                 // 是否启用转发
	ForwardID     uint      `gorm:"default:0;not null"`            // 绑定的 Forward 表的ID，不允许为空，默认值为 0
	AutoRenew     *bool     `gorm:"default:false"`                 // 自动续费
	SpeedLimit    uint16    `gorm:"not null"`                      // 限速值，使用uint16类型，最大值65535
	TUICUsername  uuid.UUID `gorm:"type:uuid;not null"`            // TUIC 用户名，UUID格式
	ExpiresAt     time.Time `gorm:"type:timestamp;not null"`       // 代理过期时间，使用 TIMESTAMP，且不可为空
	CreatedAt     time.Time `gorm:"type:timestamp;autoCreateTime"` // 记录创建时间，使用 TIMESTAMP
	UpdatedAt     time.Time `gorm:"type:timestamp;autoUpdateTime"` // 记录最近更新时间，使用 TIMESTAMP
}
