package middleware

import (
	"fmt"
	"fp-browser/pkg/log"

	"github.com/kataras/iris/v12"
	"github.com/kataras/iris/v12/sessions"
	"github.com/kataras/iris/v12/sessions/sessiondb/redis"
	"github.com/spf13/viper"
)

// InitSessionManager 初始化 Session 管理器
func InitSessionManager(conf *viper.Viper, logger *log.Logger) *sessions.Sessions {
	// 创建 Session 配置
	sessionConfig := sessions.Config{
		Cookie:          conf.GetString("session.cookie_name"),
		Expires:         conf.GetDuration("session.expire"),
		AllowReclaim:    true,
		CookieSecureTLS: conf.GetBool("session.secure"),
	}

	// 判断是否使用 Redis 作为 Session 存储
	if conf.GetBool("session.use_redis") {
		// 获取 Redis 配置
		redisHost := conf.GetString("session.redis.host")
		redisPort := conf.GetInt("session.redis.port")
		redisPassword := conf.GetString("session.redis.password")
		redisDB := conf.GetString("session.redis.db")
		redisPoolSize := conf.GetInt("session.redis.pool_size")

		// 初始化 Redis 数据库配置
		redisDBConn := redis.New(redis.Config{
			Network:   "tcp",
			Addr:      fmt.Sprintf("%s:%d", redisHost, redisPort),
			Timeout:   conf.GetDuration("session.redis.timeout"),
			Password:  redisPassword,
			Database:  redisDB,
			MaxActive: redisPoolSize,
		})

		// 使用 Redis 作为存储后端
		sessionManager := sessions.New(sessionConfig)
		sessionManager.UseDatabase(redisDBConn)
		return sessionManager
	}

	// 如果不使用 Redis，则使用内存存储
	logger.Info("Using memory session storage")
	return sessions.New(sessionConfig)
}

// SessionMiddleware 返回处理 Session 的中间件
func SessionMiddleware(sessionManager *sessions.Sessions) iris.Handler {
	return func(ctx iris.Context) {
		sess := sessionManager.Start(ctx)
		if sess == nil {
			ctx.StatusCode(iris.StatusInternalServerError)
			ctx.JSON(iris.Map{"error": "Session初始化失败"})
			return
		}
		ctx.Values().Set("session", sess)
		ctx.Next()
	}
}
