package middleware

import (
	"github.com/iris-contrib/middleware/cors"
	"github.com/kataras/iris/v12"
	"github.com/spf13/viper"
)

// CORSMiddleware 返回CORS中间件处理器
func CORSMiddleware(conf *viper.Viper) iris.Handler {
	// 从配置中读取CORS设置，如果没有则使用默认值
	allowedOrigins := conf.GetStringSlice("cors.allowed_origins")
	if len(allowedOrigins) == 0 {
		allowedOrigins = []string{"*"} // 默认允许所有来源
	}

	allowedMethods := conf.GetStringSlice("cors.allowed_methods")
	if len(allowedMethods) == 0 {
		allowedMethods = []string{"GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD", "PATCH"}
	}

	allowedHeaders := conf.GetStringSlice("cors.allowed_headers")
	if len(allowedHeaders) == 0 {
		allowedHeaders = []string{
			"Origin", "Content-Type", "Accept", "Authorization",
			"X-Requested-With", "X-CSRF-Token", "X-API-Key",
		}
	}

	exposedHeaders := conf.GetStringSlice("cors.exposed_headers")
	maxAge := conf.GetInt("cors.max_age")
	if maxAge == 0 {
		maxAge = 86400 // 默认24小时
	}

	// 创建CORS中间件
	crs := cors.New(cors.Options{
		AllowedOrigins:   allowedOrigins,
		AllowedMethods:   allowedMethods,
		AllowedHeaders:   allowedHeaders,
		ExposedHeaders:   exposedHeaders,
		MaxAge:           maxAge,
		AllowCredentials: conf.GetBool("cors.allow_credentials"),
		Debug:            conf.GetBool("cors.debug"),
	})

	return crs
}
