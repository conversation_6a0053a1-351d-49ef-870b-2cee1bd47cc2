package middleware

import (
	"regexp"
	"strings"
)

// 权限定义 - 使用资源:操作的格式
const (
	// 用户信息权限
	PermissionUserProfileRead   = "users:profile_read"   // 查看用户资料
	PermissionUserProfileUpdate = "users:profile_update" // 更新用户资料

	// 日志权限
	PermissionLoginLogRead     = "logs:login_read"     // 查看登录日志
	PermissionOperationLogRead = "logs:operation_read" // 查看操作日志

	// 佣金权限
	PermissionCommissionCreate = "commissions:create" // 创建佣金
	PermissionCommissionRead   = "commissions:read"   // 查看佣金和交易记录

	// 钱包权限
	PermissionWalletRead        = "wallet:read"        // 查看钱包余额和交易
	PermissionWalletTransaction = "wallet:transaction" // 创建钱包交易

	// 用户订阅权限
	PermissionUserSubscriptionRead   = "user_subscriptions:read"   // 查看用户订阅
	PermissionUserSubscriptionCreate = "user_subscriptions:create" // 创建用户订阅
	PermissionUserSubscriptionUpdate = "user_subscriptions:update" // 更新用户订阅

	// 订阅产品权限
	PermissionSubscriptionRead     = "subscriptions:read"     // 查看订阅产品
	PermissionSubscriptionPurchase = "subscriptions:purchase" // 购买订阅

	// 角色管理权限
	PermissionRoleCreate = "roles:create" // 创建角色
	PermissionRoleRead   = "roles:read"   // 查看角色
	PermissionRoleUpdate = "roles:update" // 更新角色
	PermissionRoleDelete = "roles:delete" // 删除角色

	// 分组管理权限
	PermissionGroupCreate = "groups:create" // 创建分组
	PermissionGroupRead   = "groups:read"   // 查看分组
	PermissionGroupUpdate = "groups:update" // 更新分组
	PermissionGroupDelete = "groups:delete" // 删除分组

	// 团队IP白名单权限
	PermissionIPWhitelistCreate    = "ip_whitelists:create"    // 创建IP白名单
	PermissionIPWhitelistRead      = "ip_whitelists:read"      // 查看IP白名单
	PermissionIPWhitelistDelete    = "ip_whitelists:delete"    // 删除IP白名单
	PermissionIPWhitelistAuthorize = "ip_whitelists:authorize" // 授权登录

	// 自托管代理权限
	PermissionSelfHostProxyCreate = "self_host_proxies:create" // 创建自托管代理
	PermissionSelfHostProxyRead   = "self_host_proxies:read"   // 查看自托管代理
	PermissionSelfHostProxyUpdate = "self_host_proxies:update" // 更新自托管代理
	PermissionSelfHostProxyDelete = "self_host_proxies:delete" // 删除自托管代理

	// 环境管理权限
	PermissionEnvironmentCreate          = "environments:create"           // 创建环境
	PermissionEnvironmentRead            = "environments:read"             // 查看环境列表和详情
	PermissionEnvironmentUpdate          = "environments:update"           // 更新环境
	PermissionEnvironmentDelete          = "environments:delete"           // 软删除环境
	PermissionEnvironmentListDeleted     = "environments:list_deleted"     // 查看已删除环境
	PermissionEnvironmentRestore         = "environments:restore"          // 恢复环境
	PermissionEnvironmentPermanentDelete = "environments:permanent_delete" // 永久删除
	PermissionEnvironmentDownload        = "environments:download"         // 下载环境文件
	PermissionEnvironmentRedisManage     = "environments:redis_manage"     // Redis管理
	PermissionEnvironmentProxyUpdate     = "environments:proxy_update"     // 代理设置
	PermissionEnvironmentFileManage      = "environments:file_manage"      // 文件管理

	// 团队用户管理权限
	PermissionTeamUserRead   = "team_users:read"   // 查看团队用户
	PermissionTeamUserCreate = "team_users:create" // 添加团队用户
	PermissionTeamUserUpdate = "team_users:update" // 更新团队用户
	PermissionTeamUserDelete = "team_users:delete" // 删除团队用户
	PermissionTeamUpdate     = "teams:update"      // 更新团队信息

	// 订单权限
	PermissionOrderRead   = "orders:read"   // 查看订单
	PermissionOrderCreate = "orders:create" // 创建订单
	PermissionOrderCancel = "orders:cancel" // 取消订单
)

// 路由权限映射表
var routePermissionMap = map[string]map[string]string{
	"GET": {
		// 用户信息
		"/api/v1/users/profile": PermissionUserProfileRead,

		// 日志
		"/api/v1/login-logs":     PermissionLoginLogRead,
		"/api/v1/operation-logs": PermissionOperationLogRead,

		// 佣金
		"/api/v1/commissions":              PermissionCommissionRead,
		"/api/v1/commissions/withdrawable": PermissionCommissionRead,
		"/api/v1/commissions/summary":      PermissionCommissionRead,

		// 钱包
		"/api/v1/wallet/transactions": PermissionWalletRead,
		"/api/v1/wallet/balance":      PermissionWalletRead,

		// 用户订阅
		"/api/v1/subscriptions/*":              PermissionUserSubscriptionRead,
		"/api/v1/teams/*/subscriptions/active": PermissionUserSubscriptionRead,
		"/api/v1/teams/*/subscriptions":        PermissionUserSubscriptionRead,

		// 订阅产品
		"/api/v1/subscriptions": PermissionSubscriptionRead,

		// 角色管理
		"/api/v1/roles": PermissionRoleRead,

		// 分组管理
		"/api/v1/groups": PermissionGroupRead,

		// 团队IP白名单
		"/api/v1/team-ip-whitelists":                PermissionIPWhitelistRead,
		"/api/v1/team-ip-whitelists/pending-logins": PermissionIPWhitelistRead,

		// 自托管代理
		"/api/v1/self-host-proxies":   PermissionSelfHostProxyRead,
		"/api/v1/self-host-proxies/*": PermissionSelfHostProxyRead,

		// 环境管理
		"/api/v1/environments":            PermissionEnvironmentRead,
		"/api/v1/environments/deleted":    PermissionEnvironmentListDeleted,
		"/api/v1/environments/*":          PermissionEnvironmentRead,
		"/api/v1/environments/*/download": PermissionEnvironmentDownload,

		// 团队用户管理
		"/api/v1/teams/users": PermissionTeamUserRead,

		// 订单
		"/api/v1/orders/detail": PermissionOrderRead,
		"/api/v1/orders":        PermissionOrderRead,
	},
	"POST": {
		// 佣金
		"/api/v1/commissions": PermissionCommissionCreate,

		// 钱包
		"/api/v1/wallet/transactions": PermissionWalletTransaction,

		// 用户订阅
		"/api/v1/subscriptions": PermissionUserSubscriptionCreate,

		// 订阅产品
		"/api/v1/subscriptions/purchase": PermissionSubscriptionPurchase,

		// 角色管理
		"/api/v1/roles": PermissionRoleCreate,

		// 分组管理
		"/api/v1/groups": PermissionGroupCreate,

		// 团队IP白名单
		"/api/v1/team-ip-whitelists":                 PermissionIPWhitelistCreate,
		"/api/v1/team-ip-whitelists/batch":           PermissionIPWhitelistCreate,
		"/api/v1/team-ip-whitelists/authorize-login": PermissionIPWhitelistAuthorize,

		// 自托管代理
		"/api/v1/self-host-proxies":       PermissionSelfHostProxyCreate,
		"/api/v1/self-host-proxies/batch": PermissionSelfHostProxyCreate,

		// 环境管理
		"/api/v1/environments":                 PermissionEnvironmentCreate,
		"/api/v1/environments/*/refresh":       PermissionEnvironmentRedisManage,
		"/api/v1/environments/file/upload-url": PermissionEnvironmentFileManage,

		// 团队用户管理
		"/api/v1/teams/users": PermissionTeamUserCreate,

		// 订单
		"/api/v1/orders":        PermissionOrderCreate,
		"/api/v1/orders/cancel": PermissionOrderCancel,
		"/api/v1/orders/price":  PermissionOrderRead,
	},
	"PUT": {
		// 用户信息
		"/api/v1/users/profile": PermissionUserProfileUpdate,

		// 用户订阅
		"/api/v1/subscriptions/*": PermissionUserSubscriptionUpdate,

		// 角色管理
		"/api/v1/roles": PermissionRoleUpdate,

		// 分组管理
		"/api/v1/groups": PermissionGroupUpdate,

		// 自托管代理
		"/api/v1/self-host-proxies":       PermissionSelfHostProxyUpdate,
		"/api/v1/self-host-proxies/batch": PermissionSelfHostProxyUpdate,

		// 环境管理
		"/api/v1/environments":         PermissionEnvironmentUpdate,
		"/api/v1/environments/restore": PermissionEnvironmentRestore,
		"/api/v1/environments/proxy":   PermissionEnvironmentProxyUpdate,

		// 团队用户管理
		"/api/v1/teams/users": PermissionTeamUserUpdate,
		"/api/v1/teams":       PermissionTeamUpdate,
	},
	"DELETE": {
		// 角色管理
		"/api/v1/roles": PermissionRoleDelete,

		// 分组管理
		"/api/v1/groups": PermissionGroupDelete,

		// 团队IP白名单
		"/api/v1/team-ip-whitelists": PermissionIPWhitelistDelete,

		// 自托管代理
		"/api/v1/self-host-proxies": PermissionSelfHostProxyDelete,

		// 环境管理
		"/api/v1/environments":           PermissionEnvironmentDelete,
		"/api/v1/environments/permanent": PermissionEnvironmentPermanentDelete,
		"/api/v1/environments/*/remove":  PermissionEnvironmentRedisManage,
		"/api/v1/environments/file":      PermissionEnvironmentFileManage,

		// 团队用户管理
		"/api/v1/teams/users": PermissionTeamUserDelete,
	},
}

// HasPermission 检查用户是否具有访问特定路由的权限
func HasPermission(userPermissions []string, requestURI string, method string) bool {
	// 获取方法对应的路由映射
	methodRoutes, exists := routePermissionMap[method]
	if !exists {
		return false
	}

	// 找到匹配的路由模式和所需权限
	requiredPermission := findRequiredPermission(methodRoutes, requestURI)
	if requiredPermission == "" {
		return false
	}

	// 检查用户是否具有所需权限
	return hasSpecificPermission(userPermissions, requiredPermission)
}

// findRequiredPermission 根据请求URI找到所需的权限
func findRequiredPermission(methodRoutes map[string]string, requestURI string) string {
	// 去除查询参数，只保留路径部分
	path := requestURI
	if queryIndex := strings.Index(requestURI, "?"); queryIndex != -1 {
		path = requestURI[:queryIndex]
	}

	// 首先尝试精确匹配
	if permission, exists := methodRoutes[path]; exists {
		return permission
	}

	// 然后尝试模式匹配（支持通配符*）
	for pattern, permission := range methodRoutes {
		if matchRoute(pattern, path) {
			return permission
		}
	}

	return ""
}

// matchRoute 匹配路由模式（支持*通配符）
func matchRoute(pattern, uri string) bool {
	// 将*替换为正则表达式的\d+（匹配数字）
	regexPattern := strings.ReplaceAll(pattern, "*", `\d+`)
	regexPattern = "^" + regexPattern + "$"

	matched, err := regexp.MatchString(regexPattern, uri)
	if err != nil {
		return false
	}
	return matched
}

// hasSpecificPermission 检查用户是否具有特定权限
func hasSpecificPermission(userPermissions []string, requiredPermission string) bool {
	for _, permission := range userPermissions {
		// 支持完全匹配
		if permission == requiredPermission {
			return true
		}
		// 支持通配符权限，如 "environments:*" 表示所有环境权限
		if strings.HasSuffix(permission, ":*") {
			prefix := strings.TrimSuffix(permission, "*")
			if strings.HasPrefix(requiredPermission, prefix) {
				return true
			}
		}
		// 支持全局管理员权限
		if permission == "*" || permission == "admin" {
			return true
		}
	}
	return false
}
