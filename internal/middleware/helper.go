// internal/middleware/helper.go
package middleware

import (
	"regexp"
	"strings"
)

type PathPermission struct {
	Method string
	Path   string
}

//// HasPermission 判断权限字符串列表中是否包含当前请求
//func HasPermission(permissions []string, uri, method string) bool {
//	current := fmt.Sprintf("%s:%s", strings.ToUpper(method), uri)
//	for _, perm := range permissions {
//		if wildcardMatch(perm, current) {
//			return true
//		}
//	}
//	return false
//}

// wildcardMatch 使用正则匹配通配符 *，如 GET:/api/user/* 可以匹配 GET:/api/user/1
func wildcardMatch(pattern, target string) bool {
	pattern = regexp.QuoteMeta(pattern)
	pattern = strings.ReplaceAll(pattern, `\*`, ".*")
	match, _ := regexp.MatchString("^"+pattern+"$", target)
	return match
}
