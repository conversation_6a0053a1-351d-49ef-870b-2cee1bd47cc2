// internal/middleware/auth.go
package middleware

import (
	"fmt"
	"github.com/kataras/iris/v12"
	"github.com/kataras/iris/v12/sessions"
)

func AuthRequired() iris.Handler {
	return func(ctx iris.Context) {
		sessVal := ctx.Values().Get("session")
		if sessVal == nil {
			ctx.StopWithJSON(iris.StatusUnauthorized, iris.Map{"error": "未登录"})
			return
		}

		sess, ok := sessVal.(*sessions.Session)
		if !ok {
			ctx.StopWithJSON(iris.StatusInternalServerError, iris.Map{"error": "Session 类型异常"})
			return
		}

		// 检查是否为Owner用户
		isOwnerVal := sess.Get("is_owner")
		if isOwnerVal != nil {
			if isOwner, ok := isOwnerVal.(bool); ok && isOwner {
				// Owner用户拥有所有权限，直接放行
				ctx.Next()
				return
			}
		}

		permVal := sess.Get("permissions")
		if permVal == nil {
			ctx.StopWithJSON(iris.StatusForbidden, iris.Map{"error": "无权限信息"})
			return
		}

		permList, ok := permVal.([]string)
		if !ok {
			ctx.StopWithJSON(iris.StatusForbidden, iris.Map{"error": "权限信息格式错误"})
			return
		}

		requestURI := ctx.Request().RequestURI
		method := ctx.Method()

		if !HasPermission(permList, requestURI, method) {
			ctx.StopWithJSON(iris.StatusForbidden, iris.Map{
				"error":               fmt.Sprintf("无权访问: %s %s", method, requestURI),
				"required_permission": findRequiredPermission(routePermissionMap[method], requestURI),
			})
			return
		}

		ctx.Next()
	}
}

func AdminAuthRequired() iris.Handler {
	return func(ctx iris.Context) {
		sess := ctx.Values().Get("session").(*sessions.Session)
		if sess.Get("admin_id") == nil {
			ctx.StatusCode(iris.StatusUnauthorized)
			ctx.JSON(iris.Map{"error": "请登录"})
			return
		}
		ctx.Next()
	}
}
