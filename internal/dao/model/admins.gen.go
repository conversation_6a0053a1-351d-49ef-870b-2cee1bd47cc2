// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameAdmin = "admins"

// Admin mapped from table <admins>
type Admin struct {
	ID        int32      `gorm:"column:id;type:integer;primaryKey;autoIncrement:true;comment:管理员ID，主键" json:"id"`                                                                                    // 管理员ID，主键
	UserName  string     `gorm:"column:user_name;type:character varying(20);not null;comment:用户名，限制为20个字符以内，唯一，不可为空" json:"user_name"`                                                               // 用户名，限制为20个字符以内，唯一，不可为空
	Telephone *string    `gorm:"column:telephone;type:character varying(15);uniqueIndex:ux_admins_telephone,priority:1;index:idx_admins_telephone,priority:1;comment:电话号码，唯一，可以为空" json:"telephone"` // 电话号码，唯一，可以为空
	Email     *string    `gorm:"column:email;type:character varying(100);uniqueIndex:ux_admins_email,priority:1;index:idx_admins_email,priority:1;comment:管理员邮箱，唯一，可以为空" json:"email"`               // 管理员邮箱，唯一，可以为空
	Password  string     `gorm:"column:password;type:character varying(60);not null;comment:密码，bcrypt加密，不能为空" json:"password"`                                                                       // 密码，bcrypt加密，不能为空
	IsActive  *bool      `gorm:"column:is_active;type:boolean;default:true;comment:是否启用账户，默认为启用，可为 NULL" json:"is_active"`                                                                           // 是否启用账户，默认为启用，可为 NULL
	CreatedAt *time.Time `gorm:"column:created_at;type:timestamp without time zone;default:CURRENT_TIMESTAMP;comment:记录创建时间" json:"created_at"`                                                      // 记录创建时间
	UpdatedAt *time.Time `gorm:"column:updated_at;type:timestamp without time zone;default:CURRENT_TIMESTAMP;comment:记录最近更新时间" json:"updated_at"`                                                    // 记录最近更新时间
}

// TableName Admin's table name
func (*Admin) TableName() string {
	return TableNameAdmin
}
