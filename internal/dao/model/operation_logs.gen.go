// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameOperationLog = "operation_logs"

// OperationLog mapped from table <operation_logs>
type OperationLog struct {
	ID        int32      `gorm:"column:id;type:integer;primaryKey;autoIncrement:true;comment:日志 ID，自增主键" json:"id"`                                          // 日志 ID，自增主键
	UserID    int32      `gorm:"column:user_id;type:integer;not null;index:idx_oplog_user_id,priority:1;comment:操作发起人用户 ID" json:"user_id"`                  // 操作发起人用户 ID
	TeamID    int32      `gorm:"column:team_id;type:integer;not null;index:idx_oplog_team_id,priority:1;comment:操作所属团队 ID" json:"team_id"`                   // 操作所属团队 ID
	Action    int32      `gorm:"column:action;type:integer;not null;comment:操作行为编码" json:"action"`                                                           // 操作行为编码
	Target    string     `gorm:"column:target;type:character varying(100);not null;comment:操作目标标识，如资源名称或 ID" json:"target"`                                  // 操作目标标识，如资源名称或 ID
	Category  int32      `gorm:"column:category;type:integer;not null;comment:操作分类标识" json:"category"`                                                       // 操作分类标识
	CreatedAt *time.Time `gorm:"column:created_at;type:timestamp without time zone;not null;default:CURRENT_TIMESTAMP;comment:操作事件发生的时间戳" json:"created_at"` // 操作事件发生的时间戳
}

// TableName OperationLog's table name
func (*OperationLog) TableName() string {
	return TableNameOperationLog
}
