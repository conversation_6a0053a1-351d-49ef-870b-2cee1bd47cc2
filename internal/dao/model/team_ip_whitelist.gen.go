// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameTeamIPWhitelist = "team_ip_whitelist"

// TeamIPWhitelist mapped from table <team_ip_whitelist>
type TeamIPWhitelist struct {
	ID        int32      `gorm:"column:id;type:integer;primaryKey;autoIncrement:true" json:"id"`
	TeamID    int32      `gorm:"column:team_id;type:integer;not null;comment:关联的团队 ID" json:"team_id"`                                                 // 关联的团队 ID
	IPAddress string     `gorm:"column:ip_address;type:inet;not null;comment:允许登录的 IP 地址" json:"ip_address"`                                           // 允许登录的 IP 地址
	CreatedAt *time.Time `gorm:"column:created_at;type:timestamp without time zone;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"` // 创建时间
}

// TableName TeamIPWhitelist's table name
func (*TeamIPWhitelist) TableName() string {
	return TableNameTeamIPWhitelist
}
