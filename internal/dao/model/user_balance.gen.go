// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameUserBalance = "user_balance"

// UserBalance mapped from table <user_balance>
type UserBalance struct {
	UserID       int32      `gorm:"column:user_id;type:integer;primaryKey;comment:用户 ID，主键" json:"user_id"`                                                 // 用户 ID，主键
	WalletAmount int64      `gorm:"column:wallet_amount;type:bigint;not null;comment:钱包余额（单位：分），可以为负" json:"wallet_amount"`                                 // 钱包余额（单位：分），可以为负
	UpdatedAt    *time.Time `gorm:"column:updated_at;type:timestamp without time zone;not null;default:CURRENT_TIMESTAMP;comment:最后更新时间" json:"updated_at"` // 最后更新时间
}

// TableName UserBalance's table name
func (*UserBalance) TableName() string {
	return TableNameUserBalance
}
