// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameCommissionTransaction = "commission_transactions"

// CommissionTransaction mapped from table <commission_transactions>
type CommissionTransaction struct {
	ID              int32      `gorm:"column:id;type:integer;primaryKey;autoIncrement:true;comment:交易ID，自增主键" json:"id"`                                                                                             // 交易ID，自增主键
	UserID          int32      `gorm:"column:user_id;type:integer;not null;index:idx_commission_user_id,priority:1;comment:推荐人用户ID" json:"user_id"`                                                                  // 推荐人用户ID
	Amount          int64      `gorm:"column:amount;type:bigint;not null;comment:交易金额，正数为收入，负数为支出" json:"amount"`                                                                                                    // 交易金额，正数为收入，负数为支出
	Currency        string     `gorm:"column:currency;type:character varying(3);not null;comment:货币类型，ISO 4217 标准，如 CNY, USD" json:"currency"`                                                                       // 货币类型，ISO 4217 标准，如 CNY, USD
	TransactionType int16      `gorm:"column:transaction_type;type:smallint;not null;comment:交易类型，1返利，2提现，3退款" json:"transaction_type"`                                                                              // 交易类型，1返利，2提现，3退款
	ReferenceID     int32      `gorm:"column:reference_id;type:integer;not null;index:idx_commission_reference_id,priority:1;comment:相关订单ID，0 表示无关联订单" json:"reference_id"`                                          // 相关订单ID，0 表示无关联订单
	Description     string     `gorm:"column:description;type:character varying(100);not null;comment:交易说明文本，最大100字符" json:"description"`                                                                            // 交易说明文本，最大100字符
	UnlockTime      *time.Time `gorm:"column:unlock_time;type:timestamp without time zone;not null;index:idx_commission_unlock_time,priority:1;default:CURRENT_TIMESTAMP;comment:资金解锁时间，到账前资金冻结" json:"unlock_time"` // 资金解锁时间，到账前资金冻结
	CreatedAt       *time.Time `gorm:"column:created_at;type:timestamp without time zone;not null;default:CURRENT_TIMESTAMP;comment:记录创建时间" json:"created_at"`                                                       // 记录创建时间
	InvitedUserID   *int32     `gorm:"column:invited_user_id;type:integer;index:idx_commission_transactions_invited_user_id,priority:1;comment:被邀请的用户ID" json:"invited_user_id"`                                     // 被邀请的用户ID
}

// TableName CommissionTransaction's table name
func (*CommissionTransaction) TableName() string {
	return TableNameCommissionTransaction
}
