// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameLoginLog = "login_logs"

// LoginLog mapped from table <login_logs>
type LoginLog struct {
	ID         int32      `gorm:"column:id;type:integer;primaryKey;autoIncrement:true;comment:日志主键 ID" json:"id"`                                        // 日志主键 ID
	UserID     int32      `gorm:"column:user_id;type:integer;not null;index:idx_loginlog_user_id,priority:1;comment:用户 ID" json:"user_id"`               // 用户 ID
	TeamID     int32      `gorm:"column:team_id;type:integer;not null;index:idx_loginlog_team_id,priority:1;comment:所属团队 ID" json:"team_id"`             // 所属团队 ID
	LoginIP    string     `gorm:"column:login_ip;type:inet;not null;comment:用户登录的 IP 地址" json:"login_ip"`                                                // 用户登录的 IP 地址
	IPLocation string     `gorm:"column:ip_location;type:character varying(100);not null;comment:IP 地址解析的物理位置，最长 100 字符" json:"ip_location"`             // IP 地址解析的物理位置，最长 100 字符
	CreatedAt  *time.Time `gorm:"column:created_at;type:timestamp without time zone;not null;default:CURRENT_TIMESTAMP;comment:登录时间戳" json:"created_at"` // 登录时间戳
}

// TableName LoginLog's table name
func (*LoginLog) TableName() string {
	return TableNameLoginLog
}
