// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"fp-browser/internal/dao/model"
)

func newCommissionTransaction(db *gorm.DB, opts ...gen.DOOption) commissionTransaction {
	_commissionTransaction := commissionTransaction{}

	_commissionTransaction.commissionTransactionDo.UseDB(db, opts...)
	_commissionTransaction.commissionTransactionDo.UseModel(&model.CommissionTransaction{})

	tableName := _commissionTransaction.commissionTransactionDo.TableName()
	_commissionTransaction.ALL = field.NewAsterisk(tableName)
	_commissionTransaction.ID = field.NewInt32(tableName, "id")
	_commissionTransaction.UserID = field.NewInt32(tableName, "user_id")
	_commissionTransaction.Amount = field.NewInt64(tableName, "amount")
	_commissionTransaction.Currency = field.NewString(tableName, "currency")
	_commissionTransaction.TransactionType = field.NewInt16(tableName, "transaction_type")
	_commissionTransaction.ReferenceID = field.NewInt32(tableName, "reference_id")
	_commissionTransaction.Description = field.NewString(tableName, "description")
	_commissionTransaction.UnlockTime = field.NewTime(tableName, "unlock_time")
	_commissionTransaction.CreatedAt = field.NewTime(tableName, "created_at")
	_commissionTransaction.InvitedUserID = field.NewInt32(tableName, "invited_user_id")

	_commissionTransaction.fillFieldMap()

	return _commissionTransaction
}

type commissionTransaction struct {
	commissionTransactionDo

	ALL             field.Asterisk
	ID              field.Int32  // 交易ID，自增主键
	UserID          field.Int32  // 推荐人用户ID
	Amount          field.Int64  // 交易金额，正数为收入，负数为支出
	Currency        field.String // 货币类型，ISO 4217 标准，如 CNY, USD
	TransactionType field.Int16  // 交易类型，1返利，2提现，3退款
	ReferenceID     field.Int32  // 相关订单ID，0 表示无关联订单
	Description     field.String // 交易说明文本，最大100字符
	UnlockTime      field.Time   // 资金解锁时间，到账前资金冻结
	CreatedAt       field.Time   // 记录创建时间
	InvitedUserID   field.Int32  // 被邀请的用户ID

	fieldMap map[string]field.Expr
}

func (c commissionTransaction) Table(newTableName string) *commissionTransaction {
	c.commissionTransactionDo.UseTable(newTableName)
	return c.updateTableName(newTableName)
}

func (c commissionTransaction) As(alias string) *commissionTransaction {
	c.commissionTransactionDo.DO = *(c.commissionTransactionDo.As(alias).(*gen.DO))
	return c.updateTableName(alias)
}

func (c *commissionTransaction) updateTableName(table string) *commissionTransaction {
	c.ALL = field.NewAsterisk(table)
	c.ID = field.NewInt32(table, "id")
	c.UserID = field.NewInt32(table, "user_id")
	c.Amount = field.NewInt64(table, "amount")
	c.Currency = field.NewString(table, "currency")
	c.TransactionType = field.NewInt16(table, "transaction_type")
	c.ReferenceID = field.NewInt32(table, "reference_id")
	c.Description = field.NewString(table, "description")
	c.UnlockTime = field.NewTime(table, "unlock_time")
	c.CreatedAt = field.NewTime(table, "created_at")
	c.InvitedUserID = field.NewInt32(table, "invited_user_id")

	c.fillFieldMap()

	return c
}

func (c *commissionTransaction) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := c.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (c *commissionTransaction) fillFieldMap() {
	c.fieldMap = make(map[string]field.Expr, 10)
	c.fieldMap["id"] = c.ID
	c.fieldMap["user_id"] = c.UserID
	c.fieldMap["amount"] = c.Amount
	c.fieldMap["currency"] = c.Currency
	c.fieldMap["transaction_type"] = c.TransactionType
	c.fieldMap["reference_id"] = c.ReferenceID
	c.fieldMap["description"] = c.Description
	c.fieldMap["unlock_time"] = c.UnlockTime
	c.fieldMap["created_at"] = c.CreatedAt
	c.fieldMap["invited_user_id"] = c.InvitedUserID
}

func (c commissionTransaction) clone(db *gorm.DB) commissionTransaction {
	c.commissionTransactionDo.ReplaceConnPool(db.Statement.ConnPool)
	return c
}

func (c commissionTransaction) replaceDB(db *gorm.DB) commissionTransaction {
	c.commissionTransactionDo.ReplaceDB(db)
	return c
}

type commissionTransactionDo struct{ gen.DO }

type ICommissionTransactionDo interface {
	gen.SubQuery
	Debug() ICommissionTransactionDo
	WithContext(ctx context.Context) ICommissionTransactionDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() ICommissionTransactionDo
	WriteDB() ICommissionTransactionDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) ICommissionTransactionDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) ICommissionTransactionDo
	Not(conds ...gen.Condition) ICommissionTransactionDo
	Or(conds ...gen.Condition) ICommissionTransactionDo
	Select(conds ...field.Expr) ICommissionTransactionDo
	Where(conds ...gen.Condition) ICommissionTransactionDo
	Order(conds ...field.Expr) ICommissionTransactionDo
	Distinct(cols ...field.Expr) ICommissionTransactionDo
	Omit(cols ...field.Expr) ICommissionTransactionDo
	Join(table schema.Tabler, on ...field.Expr) ICommissionTransactionDo
	LeftJoin(table schema.Tabler, on ...field.Expr) ICommissionTransactionDo
	RightJoin(table schema.Tabler, on ...field.Expr) ICommissionTransactionDo
	Group(cols ...field.Expr) ICommissionTransactionDo
	Having(conds ...gen.Condition) ICommissionTransactionDo
	Limit(limit int) ICommissionTransactionDo
	Offset(offset int) ICommissionTransactionDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) ICommissionTransactionDo
	Unscoped() ICommissionTransactionDo
	Create(values ...*model.CommissionTransaction) error
	CreateInBatches(values []*model.CommissionTransaction, batchSize int) error
	Save(values ...*model.CommissionTransaction) error
	First() (*model.CommissionTransaction, error)
	Take() (*model.CommissionTransaction, error)
	Last() (*model.CommissionTransaction, error)
	Find() ([]*model.CommissionTransaction, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.CommissionTransaction, err error)
	FindInBatches(result *[]*model.CommissionTransaction, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.CommissionTransaction) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) ICommissionTransactionDo
	Assign(attrs ...field.AssignExpr) ICommissionTransactionDo
	Joins(fields ...field.RelationField) ICommissionTransactionDo
	Preload(fields ...field.RelationField) ICommissionTransactionDo
	FirstOrInit() (*model.CommissionTransaction, error)
	FirstOrCreate() (*model.CommissionTransaction, error)
	FindByPage(offset int, limit int) (result []*model.CommissionTransaction, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Rows() (*sql.Rows, error)
	Row() *sql.Row
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) ICommissionTransactionDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (c commissionTransactionDo) Debug() ICommissionTransactionDo {
	return c.withDO(c.DO.Debug())
}

func (c commissionTransactionDo) WithContext(ctx context.Context) ICommissionTransactionDo {
	return c.withDO(c.DO.WithContext(ctx))
}

func (c commissionTransactionDo) ReadDB() ICommissionTransactionDo {
	return c.Clauses(dbresolver.Read)
}

func (c commissionTransactionDo) WriteDB() ICommissionTransactionDo {
	return c.Clauses(dbresolver.Write)
}

func (c commissionTransactionDo) Session(config *gorm.Session) ICommissionTransactionDo {
	return c.withDO(c.DO.Session(config))
}

func (c commissionTransactionDo) Clauses(conds ...clause.Expression) ICommissionTransactionDo {
	return c.withDO(c.DO.Clauses(conds...))
}

func (c commissionTransactionDo) Returning(value interface{}, columns ...string) ICommissionTransactionDo {
	return c.withDO(c.DO.Returning(value, columns...))
}

func (c commissionTransactionDo) Not(conds ...gen.Condition) ICommissionTransactionDo {
	return c.withDO(c.DO.Not(conds...))
}

func (c commissionTransactionDo) Or(conds ...gen.Condition) ICommissionTransactionDo {
	return c.withDO(c.DO.Or(conds...))
}

func (c commissionTransactionDo) Select(conds ...field.Expr) ICommissionTransactionDo {
	return c.withDO(c.DO.Select(conds...))
}

func (c commissionTransactionDo) Where(conds ...gen.Condition) ICommissionTransactionDo {
	return c.withDO(c.DO.Where(conds...))
}

func (c commissionTransactionDo) Order(conds ...field.Expr) ICommissionTransactionDo {
	return c.withDO(c.DO.Order(conds...))
}

func (c commissionTransactionDo) Distinct(cols ...field.Expr) ICommissionTransactionDo {
	return c.withDO(c.DO.Distinct(cols...))
}

func (c commissionTransactionDo) Omit(cols ...field.Expr) ICommissionTransactionDo {
	return c.withDO(c.DO.Omit(cols...))
}

func (c commissionTransactionDo) Join(table schema.Tabler, on ...field.Expr) ICommissionTransactionDo {
	return c.withDO(c.DO.Join(table, on...))
}

func (c commissionTransactionDo) LeftJoin(table schema.Tabler, on ...field.Expr) ICommissionTransactionDo {
	return c.withDO(c.DO.LeftJoin(table, on...))
}

func (c commissionTransactionDo) RightJoin(table schema.Tabler, on ...field.Expr) ICommissionTransactionDo {
	return c.withDO(c.DO.RightJoin(table, on...))
}

func (c commissionTransactionDo) Group(cols ...field.Expr) ICommissionTransactionDo {
	return c.withDO(c.DO.Group(cols...))
}

func (c commissionTransactionDo) Having(conds ...gen.Condition) ICommissionTransactionDo {
	return c.withDO(c.DO.Having(conds...))
}

func (c commissionTransactionDo) Limit(limit int) ICommissionTransactionDo {
	return c.withDO(c.DO.Limit(limit))
}

func (c commissionTransactionDo) Offset(offset int) ICommissionTransactionDo {
	return c.withDO(c.DO.Offset(offset))
}

func (c commissionTransactionDo) Scopes(funcs ...func(gen.Dao) gen.Dao) ICommissionTransactionDo {
	return c.withDO(c.DO.Scopes(funcs...))
}

func (c commissionTransactionDo) Unscoped() ICommissionTransactionDo {
	return c.withDO(c.DO.Unscoped())
}

func (c commissionTransactionDo) Create(values ...*model.CommissionTransaction) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Create(values)
}

func (c commissionTransactionDo) CreateInBatches(values []*model.CommissionTransaction, batchSize int) error {
	return c.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (c commissionTransactionDo) Save(values ...*model.CommissionTransaction) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Save(values)
}

func (c commissionTransactionDo) First() (*model.CommissionTransaction, error) {
	if result, err := c.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.CommissionTransaction), nil
	}
}

func (c commissionTransactionDo) Take() (*model.CommissionTransaction, error) {
	if result, err := c.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.CommissionTransaction), nil
	}
}

func (c commissionTransactionDo) Last() (*model.CommissionTransaction, error) {
	if result, err := c.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.CommissionTransaction), nil
	}
}

func (c commissionTransactionDo) Find() ([]*model.CommissionTransaction, error) {
	result, err := c.DO.Find()
	return result.([]*model.CommissionTransaction), err
}

func (c commissionTransactionDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.CommissionTransaction, err error) {
	buf := make([]*model.CommissionTransaction, 0, batchSize)
	err = c.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (c commissionTransactionDo) FindInBatches(result *[]*model.CommissionTransaction, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return c.DO.FindInBatches(result, batchSize, fc)
}

func (c commissionTransactionDo) Attrs(attrs ...field.AssignExpr) ICommissionTransactionDo {
	return c.withDO(c.DO.Attrs(attrs...))
}

func (c commissionTransactionDo) Assign(attrs ...field.AssignExpr) ICommissionTransactionDo {
	return c.withDO(c.DO.Assign(attrs...))
}

func (c commissionTransactionDo) Joins(fields ...field.RelationField) ICommissionTransactionDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Joins(_f))
	}
	return &c
}

func (c commissionTransactionDo) Preload(fields ...field.RelationField) ICommissionTransactionDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Preload(_f))
	}
	return &c
}

func (c commissionTransactionDo) FirstOrInit() (*model.CommissionTransaction, error) {
	if result, err := c.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.CommissionTransaction), nil
	}
}

func (c commissionTransactionDo) FirstOrCreate() (*model.CommissionTransaction, error) {
	if result, err := c.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.CommissionTransaction), nil
	}
}

func (c commissionTransactionDo) FindByPage(offset int, limit int) (result []*model.CommissionTransaction, count int64, err error) {
	result, err = c.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = c.Offset(-1).Limit(-1).Count()
	return
}

func (c commissionTransactionDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = c.Count()
	if err != nil {
		return
	}

	err = c.Offset(offset).Limit(limit).Scan(result)
	return
}

func (c commissionTransactionDo) Scan(result interface{}) (err error) {
	return c.DO.Scan(result)
}

func (c commissionTransactionDo) Delete(models ...*model.CommissionTransaction) (result gen.ResultInfo, err error) {
	return c.DO.Delete(models)
}

func (c *commissionTransactionDo) withDO(do gen.Dao) *commissionTransactionDo {
	c.DO = *do.(*gen.DO)
	return c
}
