// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"

	"gorm.io/gen"

	"gorm.io/plugin/dbresolver"
)

var (
	Q                     = new(Query)
	Admin                 *admin
	CommissionTransaction *commissionTransaction
	Coupon                *coupon
	Environment           *environment
	Forward               *forward
	Group                 *group
	LoginLog              *loginLog
	Notice                *notice
	OperationLog          *operationLog
	Order                 *order
	Proxy                 *proxy
	Role                  *role
	SelfHostProxy         *selfHostProxy
	StaticProxyPricing    *staticProxyPricing
	Subscription          *subscription
	System                *system
	Team                  *team
	TeamIPWhitelist       *teamIPWhitelist
	User                  *user
	UserBalance           *userBalance
	UserSubscription      *userSubscription
	WalletTransaction     *walletTransaction
)

func SetDefault(db *gorm.DB, opts ...gen.DOOption) {
	*Q = *Use(db, opts...)
	Admin = &Q.Admin
	CommissionTransaction = &Q.CommissionTransaction
	Coupon = &Q.Coupon
	Environment = &Q.Environment
	Forward = &Q.Forward
	Group = &Q.Group
	LoginLog = &Q.LoginLog
	Notice = &Q.Notice
	OperationLog = &Q.OperationLog
	Order = &Q.Order
	Proxy = &Q.Proxy
	Role = &Q.Role
	SelfHostProxy = &Q.SelfHostProxy
	StaticProxyPricing = &Q.StaticProxyPricing
	Subscription = &Q.Subscription
	System = &Q.System
	Team = &Q.Team
	TeamIPWhitelist = &Q.TeamIPWhitelist
	User = &Q.User
	UserBalance = &Q.UserBalance
	UserSubscription = &Q.UserSubscription
	WalletTransaction = &Q.WalletTransaction
}

func Use(db *gorm.DB, opts ...gen.DOOption) *Query {
	return &Query{
		db:                    db,
		Admin:                 newAdmin(db, opts...),
		CommissionTransaction: newCommissionTransaction(db, opts...),
		Coupon:                newCoupon(db, opts...),
		Environment:           newEnvironment(db, opts...),
		Forward:               newForward(db, opts...),
		Group:                 newGroup(db, opts...),
		LoginLog:              newLoginLog(db, opts...),
		Notice:                newNotice(db, opts...),
		OperationLog:          newOperationLog(db, opts...),
		Order:                 newOrder(db, opts...),
		Proxy:                 newProxy(db, opts...),
		Role:                  newRole(db, opts...),
		SelfHostProxy:         newSelfHostProxy(db, opts...),
		StaticProxyPricing:    newStaticProxyPricing(db, opts...),
		Subscription:          newSubscription(db, opts...),
		System:                newSystem(db, opts...),
		Team:                  newTeam(db, opts...),
		TeamIPWhitelist:       newTeamIPWhitelist(db, opts...),
		User:                  newUser(db, opts...),
		UserBalance:           newUserBalance(db, opts...),
		UserSubscription:      newUserSubscription(db, opts...),
		WalletTransaction:     newWalletTransaction(db, opts...),
	}
}

type Query struct {
	db *gorm.DB

	Admin                 admin
	CommissionTransaction commissionTransaction
	Coupon                coupon
	Environment           environment
	Forward               forward
	Group                 group
	LoginLog              loginLog
	Notice                notice
	OperationLog          operationLog
	Order                 order
	Proxy                 proxy
	Role                  role
	SelfHostProxy         selfHostProxy
	StaticProxyPricing    staticProxyPricing
	Subscription          subscription
	System                system
	Team                  team
	TeamIPWhitelist       teamIPWhitelist
	User                  user
	UserBalance           userBalance
	UserSubscription      userSubscription
	WalletTransaction     walletTransaction
}

func (q *Query) Available() bool { return q.db != nil }

func (q *Query) clone(db *gorm.DB) *Query {
	return &Query{
		db:                    db,
		Admin:                 q.Admin.clone(db),
		CommissionTransaction: q.CommissionTransaction.clone(db),
		Coupon:                q.Coupon.clone(db),
		Environment:           q.Environment.clone(db),
		Forward:               q.Forward.clone(db),
		Group:                 q.Group.clone(db),
		LoginLog:              q.LoginLog.clone(db),
		Notice:                q.Notice.clone(db),
		OperationLog:          q.OperationLog.clone(db),
		Order:                 q.Order.clone(db),
		Proxy:                 q.Proxy.clone(db),
		Role:                  q.Role.clone(db),
		SelfHostProxy:         q.SelfHostProxy.clone(db),
		StaticProxyPricing:    q.StaticProxyPricing.clone(db),
		Subscription:          q.Subscription.clone(db),
		System:                q.System.clone(db),
		Team:                  q.Team.clone(db),
		TeamIPWhitelist:       q.TeamIPWhitelist.clone(db),
		User:                  q.User.clone(db),
		UserBalance:           q.UserBalance.clone(db),
		UserSubscription:      q.UserSubscription.clone(db),
		WalletTransaction:     q.WalletTransaction.clone(db),
	}
}

func (q *Query) ReadDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Read))
}

func (q *Query) WriteDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Write))
}

func (q *Query) ReplaceDB(db *gorm.DB) *Query {
	return &Query{
		db:                    db,
		Admin:                 q.Admin.replaceDB(db),
		CommissionTransaction: q.CommissionTransaction.replaceDB(db),
		Coupon:                q.Coupon.replaceDB(db),
		Environment:           q.Environment.replaceDB(db),
		Forward:               q.Forward.replaceDB(db),
		Group:                 q.Group.replaceDB(db),
		LoginLog:              q.LoginLog.replaceDB(db),
		Notice:                q.Notice.replaceDB(db),
		OperationLog:          q.OperationLog.replaceDB(db),
		Order:                 q.Order.replaceDB(db),
		Proxy:                 q.Proxy.replaceDB(db),
		Role:                  q.Role.replaceDB(db),
		SelfHostProxy:         q.SelfHostProxy.replaceDB(db),
		StaticProxyPricing:    q.StaticProxyPricing.replaceDB(db),
		Subscription:          q.Subscription.replaceDB(db),
		System:                q.System.replaceDB(db),
		Team:                  q.Team.replaceDB(db),
		TeamIPWhitelist:       q.TeamIPWhitelist.replaceDB(db),
		User:                  q.User.replaceDB(db),
		UserBalance:           q.UserBalance.replaceDB(db),
		UserSubscription:      q.UserSubscription.replaceDB(db),
		WalletTransaction:     q.WalletTransaction.replaceDB(db),
	}
}

type queryCtx struct {
	Admin                 IAdminDo
	CommissionTransaction ICommissionTransactionDo
	Coupon                ICouponDo
	Environment           IEnvironmentDo
	Forward               IForwardDo
	Group                 IGroupDo
	LoginLog              ILoginLogDo
	Notice                INoticeDo
	OperationLog          IOperationLogDo
	Order                 IOrderDo
	Proxy                 IProxyDo
	Role                  IRoleDo
	SelfHostProxy         ISelfHostProxyDo
	StaticProxyPricing    IStaticProxyPricingDo
	Subscription          ISubscriptionDo
	System                ISystemDo
	Team                  ITeamDo
	TeamIPWhitelist       ITeamIPWhitelistDo
	User                  IUserDo
	UserBalance           IUserBalanceDo
	UserSubscription      IUserSubscriptionDo
	WalletTransaction     IWalletTransactionDo
}

func (q *Query) WithContext(ctx context.Context) *queryCtx {
	return &queryCtx{
		Admin:                 q.Admin.WithContext(ctx),
		CommissionTransaction: q.CommissionTransaction.WithContext(ctx),
		Coupon:                q.Coupon.WithContext(ctx),
		Environment:           q.Environment.WithContext(ctx),
		Forward:               q.Forward.WithContext(ctx),
		Group:                 q.Group.WithContext(ctx),
		LoginLog:              q.LoginLog.WithContext(ctx),
		Notice:                q.Notice.WithContext(ctx),
		OperationLog:          q.OperationLog.WithContext(ctx),
		Order:                 q.Order.WithContext(ctx),
		Proxy:                 q.Proxy.WithContext(ctx),
		Role:                  q.Role.WithContext(ctx),
		SelfHostProxy:         q.SelfHostProxy.WithContext(ctx),
		StaticProxyPricing:    q.StaticProxyPricing.WithContext(ctx),
		Subscription:          q.Subscription.WithContext(ctx),
		System:                q.System.WithContext(ctx),
		Team:                  q.Team.WithContext(ctx),
		TeamIPWhitelist:       q.TeamIPWhitelist.WithContext(ctx),
		User:                  q.User.WithContext(ctx),
		UserBalance:           q.UserBalance.WithContext(ctx),
		UserSubscription:      q.UserSubscription.WithContext(ctx),
		WalletTransaction:     q.WalletTransaction.WithContext(ctx),
	}
}

func (q *Query) Transaction(fc func(tx *Query) error, opts ...*sql.TxOptions) error {
	return q.db.Transaction(func(tx *gorm.DB) error { return fc(q.clone(tx)) }, opts...)
}

func (q *Query) Begin(opts ...*sql.TxOptions) *QueryTx {
	tx := q.db.Begin(opts...)
	return &QueryTx{Query: q.clone(tx), Error: tx.Error}
}

type QueryTx struct {
	*Query
	Error error
}

func (q *QueryTx) Commit() error {
	return q.db.Commit().Error
}

func (q *QueryTx) Rollback() error {
	return q.db.Rollback().Error
}

func (q *QueryTx) SavePoint(name string) error {
	return q.db.SavePoint(name).Error
}

func (q *QueryTx) RollbackTo(name string) error {
	return q.db.RollbackTo(name).Error
}
