package alipay

import (
	"context"
	"fmt"

	"fp-browser/pkg/alipay"
)

// Service 支付宝支付服务
type Service struct {
	client *alipay.Client
}

// NewAlipayService 创建支付宝支付服务
func NewAlipayService(client *alipay.Client) *Service {
	return &Service{
		client: client,
	}
}

// CreatePayment 创建支付订单
func (s *Service) CreatePayment(ctx context.Context, paymentMethod string, orderID string, amount float64, subject, description, notifyURL, clientIP string) (payURL string, err error) {
	// 检查支付方式
	if paymentMethod != "alipay" {
		return "", fmt.Errorf("不支持的支付方式: %s", paymentMethod)
	}

	// 调用客户端的CreatePayment方法
	return s.client.CreatePayment(ctx, orderID, amount, subject, description, notifyURL)
}

// HandleCallback 处理支付回调通知
func (s *Service) HandleCallback(ctx context.Context, paymentMethod string, payload []byte) (string, error) {
	// 检查支付方式
	if paymentMethod != "alipay" {
		return "", fmt.Errorf("不支持的支付方式: %s", paymentMethod)
	}

	// 调用客户端的HandleCallback方法
	return s.client.HandleCallback(ctx, payload)
}

// Refund 申请退款
func (s *Service) Refund(ctx context.Context, paymentMethod string, orderID, tradeNo string, amount float64, reason string) (success bool, err error) {
	// 检查支付方式
	if paymentMethod != "alipay" {
		return false, fmt.Errorf("不支持的支付方式: %s", paymentMethod)
	}

	// 调用客户端的Refund方法
	return s.client.Refund(ctx, orderID, tradeNo, amount, reason)
}
