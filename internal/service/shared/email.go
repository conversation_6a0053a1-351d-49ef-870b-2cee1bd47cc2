package shared

import (
	"context"
	"fmt"
	"regexp"
	"strings"

	"fp-browser/pkg/smtp"
)

type EmailService interface {
	// 发送相关
	SendHTMLEmail(ctx context.Context, email, subject, htmlBody, textBody string) error
	SendBulkHTMLEmail(ctx context.Context, emails []string, subject, htmlBody, textBody string) error

	// 验证相关
	ValidateEmail(email string) error
	ValidateEmails(emails []string) error
}

type emailService struct {
	client *smtp.Client
}

func NewEmailService(client *smtp.Client) EmailService {
	return &emailService{
		client: client,
	}
}

// validateEmailFormat 验证邮箱格式
func (e *emailService) validateEmailFormat(email string) bool {
	// 邮箱格式正则表达式
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	return emailRegex.MatchString(email)
}

// sanitizeEmail 清理邮箱地址（去除空格等）
func (e *emailService) sanitizeEmail(email string) string {
	return strings.TrimSpace(strings.ToLower(email))
}

// sanitizeEmails 批量清理邮箱地址
func (e *emailService) sanitizeEmails(emails []string) []string {
	sanitized := make([]string, len(emails))
	for i, email := range emails {
		sanitized[i] = e.sanitizeEmail(email)
	}
	return sanitized
}

// 验证相关方法

func (e *emailService) ValidateEmail(email string) error {
	if email == "" {
		return fmt.Errorf("邮箱地址不能为空")
	}

	sanitized := e.sanitizeEmail(email)
	if !e.validateEmailFormat(sanitized) {
		return fmt.Errorf("邮箱格式无效: %s", email)
	}

	return nil
}

func (e *emailService) ValidateEmails(emails []string) error {
	if len(emails) == 0 {
		return fmt.Errorf("邮箱列表不能为空")
	}

	seen := make(map[string]bool)
	for i, email := range emails {
		if err := e.ValidateEmail(email); err != nil {
			return fmt.Errorf("第%d个邮箱验证失败: %w", i+1, err)
		}

		sanitized := e.sanitizeEmail(email)
		if seen[sanitized] {
			return fmt.Errorf("发现重复邮箱: %s", email)
		}
		seen[sanitized] = true
	}

	return nil
}

// 发送相关方法

func (e *emailService) SendHTMLEmail(ctx context.Context, email, subject, htmlBody, textBody string) error {
	// 验证邮箱
	if err := e.ValidateEmail(email); err != nil {
		return fmt.Errorf("邮箱验证失败: %w", err)
	}

	// 验证内容
	if subject == "" {
		return fmt.Errorf("邮件主题不能为空")
	}

	if htmlBody == "" && textBody == "" {
		return fmt.Errorf("邮件内容不能为空")
	}

	// 清理邮箱地址
	sanitizedEmail := e.sanitizeEmail(email)

	// 发送邮件
	if err := e.client.SendHTMLMail(sanitizedEmail, subject, htmlBody, textBody); err != nil {
		return fmt.Errorf("发送邮件失败: %w", err)
	}

	return nil
}

func (e *emailService) SendBulkHTMLEmail(ctx context.Context, emails []string, subject, htmlBody, textBody string) error {
	// 验证邮箱列表
	if err := e.ValidateEmails(emails); err != nil {
		return fmt.Errorf("邮箱列表验证失败: %w", err)
	}

	// 验证内容
	if subject == "" {
		return fmt.Errorf("邮件主题不能为空")
	}

	if htmlBody == "" && textBody == "" {
		return fmt.Errorf("邮件内容不能为空")
	}

	// 清理邮箱地址
	sanitizedEmails := e.sanitizeEmails(emails)

	// 批量发送邮件
	if err := e.client.SendBulkHTMLMail(sanitizedEmails, subject, htmlBody, textBody); err != nil {
		return fmt.Errorf("批量发送邮件失败: %w", err)
	}

	return nil
}
