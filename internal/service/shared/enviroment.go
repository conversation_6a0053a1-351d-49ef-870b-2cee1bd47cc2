package shared

import (
	"context"
	"fmt"
	"net/url"
	"strconv"

	"fp-browser/internal/repository"
	"fp-browser/pkg/oss"
)

type EnvironmentService interface {
	// 更新环境存储信息(处理OSS回调)
	UpdateEnvironmentStorage(ctx context.Context, authorization, path, queryString, body string) error
}

type environmentService struct {
	environmentRepo repository.EnvironmentRepository
}

func NewEnvironmentService(
	environmentRepo repository.EnvironmentRepository,
) EnvironmentService {
	return &environmentService{
		environmentRepo: environmentRepo,
	}
}

// UpdateEnvironmentStorage 更新环境存储信息(处理OSS回调)
func (s *environmentService) UpdateEnvironmentStorage(ctx context.Context, authorization, path, queryString, body string) error {
	// 1. 验证OSS回调签名
	if !oss.VerifyOSSCallback(authorization, path, queryString, body) {
		return fmt.Errorf("OSS回调签名验证失败")
	}

	// 2. 解析回调参数
	params, err := s.parseCallbackParams(body)
	if err != nil {
		return fmt.Errorf("解析回调参数失败: %v", err)
	}

	// 3. 验证必需参数
	envid, exists := params["envid"]
	if !exists || envid == "" {
		return fmt.Errorf("缺少envid参数")
	}

	object, exists := params["object"]
	if !exists || object == "" {
		return fmt.Errorf("缺少object参数")
	}

	sizeStr, exists := params["size"]
	if !exists || sizeStr == "" {
		return fmt.Errorf("缺少size参数")
	}

	// 4. 转换size为int32
	size64, err := strconv.ParseInt(sizeStr, 10, 32)
	if err != nil {
		return fmt.Errorf("size参数格式错误: %v", err)
	}
	size := int32(size64)

	// 5. 转换envid为int32
	envID64, err := strconv.ParseInt(envid, 10, 32)
	if err != nil {
		return fmt.Errorf("envid参数格式错误: %v", err)
	}
	envID := int32(envID64)

	// 6. 更新环境存储信息
	err = s.environmentRepo.UpdateEnvironmentStorageByID(ctx, envID, object, size)
	if err != nil {
		return fmt.Errorf("更新环境存储信息失败: %v", err)
	}

	return nil
}

// parseCallbackParams 解析回调参数
func (s *environmentService) parseCallbackParams(body string) (map[string]string, error) {
	values, err := url.ParseQuery(body)
	if err != nil {
		return nil, err
	}

	result := make(map[string]string)
	for key, vals := range values {
		if len(vals) > 0 {
			result[key] = vals[0]
		}
	}

	return result, nil
}
