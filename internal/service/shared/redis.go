package shared

import (
	"context"
	"errors"
	"time"

	"github.com/redis/go-redis/v9"
)

type RedisService interface {
	Get(ctx context.Context, key string) (string, error)
	Set(ctx context.Context, key, value string, expiration time.Duration) error
	Delete(ctx context.Context, key string) error
	IsNil(err error) bool

	// 新增：扫描匹配pattern的key
	Scan(ctx context.Context, pattern string) ([]string, error)
	// 新增：获取key的TTL
	TTL(ctx context.Context, key string) (time.Duration, error)
}

type redisService struct {
	client *redis.Client
}

func NewRedisService(client *redis.Client) RedisService {
	return &redisService{
		client: client,
	}
}

func (r *redisService) Get(ctx context.Context, key string) (string, error) {
	return r.client.Get(ctx, key).Result()
}

func (r *redisService) Set(ctx context.Context, key, value string, expiration time.Duration) error {
	return r.client.Set(ctx, key, value, expiration).Err()
}

func (r *redisService) Delete(ctx context.Context, key string) error {
	return r.client.Del(ctx, key).Err()
}

func (r *redisService) IsNil(err error) bool {
	return errors.Is(err, redis.Nil)
}

// Scan 扫描匹配pattern的key
func (r *redisService) Scan(ctx context.Context, pattern string) ([]string, error) {
	var keys []string
	var cursor uint64

	for {
		var scanKeys []string
		var err error

		scanKeys, cursor, err = r.client.Scan(ctx, cursor, pattern, 100).Result()
		if err != nil {
			return nil, err
		}

		keys = append(keys, scanKeys...)

		if cursor == 0 {
			break
		}
	}

	return keys, nil
}

// TTL 获取key的剩余生存时间
func (r *redisService) TTL(ctx context.Context, key string) (time.Duration, error) {
	return r.client.TTL(ctx, key).Result()
}
