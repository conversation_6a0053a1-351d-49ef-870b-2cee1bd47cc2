package shared

import (
	"context"
	"time"

	"fp-browser/pkg/oss"

	"github.com/google/uuid"
)

type OSSService interface {
	// 上传相关
	GenerateSimpleUploadURL(ctx context.Context, envid string, expirationDuration time.Duration) (*oss.PostPolicyResponse, error)
	GenerateObjectName(envid string) string

	// 下载相关
	DownloadObject(ctx context.Context, objectName string) (*oss.DownloadResult, error)
	CheckObjectExists(ctx context.Context, objectName string) (bool, error)
	GetObjectInfo(ctx context.Context, objectName string) (*oss.DownloadResult, error)

	// 删除相关
	DeleteObject(ctx context.Context, objectName string) (*oss.DeleteResult, error)
	DeleteObjectIfExists(ctx context.Context, objectName string) (*oss.DeleteResult, error)
}

type ossService struct {
	client *oss.Client
}

func NewOSSService(client *oss.Client) OSSService {
	return &ossService{
		client: client,
	}
}

// generateObjectNameFromEnvId 根据envid生成对应的objectName
func (o *ossService) generateObjectNameFromEnvId(envid string) string {
	// 使用"suiyu"生成自定义命名空间UUID
	// 先基于DNS命名空间和"suiyu"生成一个固定的命名空间UUID
	suiyuNamespace := uuid.NewSHA1(uuid.NameSpaceDNS, []byte("suiyu"))

	// 然后基于这个自定义命名空间和envid生成确定性的UUID
	objectUUID := uuid.NewSHA1(suiyuNamespace, []byte(envid))

	// 构建对象名：UUID + .tar.xz后缀
	return objectUUID.String() + ".tar.xz"
}

// 上传相关方法

func (o *ossService) GenerateSimpleUploadURL(ctx context.Context, envid string, expirationDuration time.Duration) (*oss.PostPolicyResponse, error) {
	// 根据envid生成objectName
	objectName := o.generateObjectNameFromEnvId(envid)

	return o.client.GenerateSimplePostPolicy(ctx, objectName, expirationDuration, envid)
}

func (o *ossService) GenerateObjectName(envid string) string {
	return o.generateObjectNameFromEnvId(envid)
}

// 下载相关方法

func (o *ossService) DownloadObject(ctx context.Context, objectName string) (*oss.DownloadResult, error) {
	return o.client.DownloadObject(ctx, objectName)
}

func (o *ossService) CheckObjectExists(ctx context.Context, objectName string) (bool, error) {
	return o.client.CheckObjectExists(ctx, objectName)
}

func (o *ossService) GetObjectInfo(ctx context.Context, objectName string) (*oss.DownloadResult, error) {
	return o.client.GetObjectInfo(ctx, objectName)
}

// 删除相关方法

func (o *ossService) DeleteObject(ctx context.Context, objectName string) (*oss.DeleteResult, error) {
	return o.client.DeleteObject(ctx, objectName)
}

func (o *ossService) DeleteObjectIfExists(ctx context.Context, objectName string) (*oss.DeleteResult, error) {
	return o.client.DeleteObjectIfExists(ctx, objectName)
}
