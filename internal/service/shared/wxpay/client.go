package wxpay

import (
	"github.com/spf13/viper"
)

// NewService 创建微信支付服务（用于依赖注入）
func NewService(conf *viper.Viper) *Service {
	appID := conf.GetString("payment.wxpay.app_id")
	mchID := conf.GetString("payment.wxpay.mch_id")
	apiKey := conf.GetString("payment.wxpay.api_key")
	notifyURL := conf.GetString("payment.wxpay.notify_url")
	returnURL := conf.GetString("payment.wxpay.return_url")
	isSandbox := conf.GetBool("payment.wxpay.is_sandbox")

	return NewWxpayService(
		appID,
		mchID,
		apiKey,
		notifyURL,
		returnURL,
		isSandbox,
	)
}
