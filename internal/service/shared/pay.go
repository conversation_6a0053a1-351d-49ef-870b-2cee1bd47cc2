package shared

import (
	"context"
)

// PaymentClient 支付客户端接口
type PaymentClient interface {
	// CreatePayment 为已有订单创建支付
	// 参数:
	// - paymentMethod: 支付方式(如 "alipay", "wechat")
	// - orderID: 订单ID
	// - amount: 订单金额
	// - subject: 商品标题
	// - description: 商品描述
	// - notifyURL: 回调通知URL
	// - clientIP: 客户端IP
	// 返回:
	// - payURL: 支付URL，用于生成二维码或跳转
	// - error: 错误信息
	CreatePayment(ctx context.Context, paymentMethod string, orderID string, amount float64, subject, description, notifyURL, clientIP string) (payURL string, err error)

	// HandleCallback 处理支付回调通知
	// 参数:
	// - paymentMethod: 支付方式(如 "alipay", "wechat")
	// - payload: 回调原始数据
	// 返回:
	// - orderID: 订单ID，用于后续的PostPayment处理
	// - error: 错误信息，如果为nil则表示验证成功
	HandleCallback(ctx context.Context, paymentMethod string, payload []byte) (orderID string, err error)

	// Refund 申请退款
	// 参数:
	// - paymentMethod: 支付方式(如 "alipay", "wechat")
	// - orderID: 原订单ID
	// - tradeNo: 第三方支付平台交易号
	// - amount: 退款金额
	// - reason: 退款原因
	// 返回:
	// - success: 退款是否成功
	// - error: 错误信息
	Refund(ctx context.Context, paymentMethod string, orderID, tradeNo string, amount float64, reason string) (success bool, err error)
}
