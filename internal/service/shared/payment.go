package shared

import (
	"context"
	"fmt"
	"fp-browser/internal/dao/model"
	"fp-browser/internal/repository"
	"fp-browser/internal/service/shared/alipay"
	"fp-browser/internal/service/shared/wxpay"
	"time"
)

// PaymentType 表示支付类型
type PaymentType string

const (
	PaymentTypeAlipay PaymentType = "alipay" // 支付宝
	PaymentTypeWechat PaymentType = "wechat" // 微信支付
)

// 订单状态常量
const (
	OrderStatusPending   int16 = 1 // 待支付
	OrderStatusPaid      int16 = 2 // 已支付
	OrderStatusFailed    int16 = 3 // 失败
	OrderStatusCancelled int16 = 4 // 取消
)

// 返利类型常量
const (
	CommissionTypeFirstTime int16 = 1 // 首次返利（需检查是否已有记录）
	CommissionTypeEveryTime int16 = 2 // 每次返利（无需检查）
)

// 交易类型常量
const (
	TransactionTypeCommission int16 = 1 // 返利
	TransactionTypeWithdraw   int16 = 2 // 提现
	TransactionTypeRefund     int16 = 3 // 退款
)

// 钱包交易类型常量
const (
	WalletTransactionTypeBalancePayment int16 = 4 // 余额支付扣减
)

// PaymentService 支付服务，聚合多种支付方式
type PaymentService struct {
	// 用map存储各种支付客户端，key为支付方式
	clients map[PaymentType]PaymentClient
	// 订单仓储
	orderRepo repository.OrderRepository
	// 用户仓储
	userRepo repository.UserRepository
	// 返利交易仓储
	commissionRepo repository.CommissionTransactionRepository
	// 钱包交易仓储
	walletRepo repository.WalletTransactionRepository
}

// NewPaymentService 创建支付服务
func NewPaymentService(
	alipayService *alipay.Service,
	wechatService *wxpay.Service,
	orderRepo repository.OrderRepository,
	userRepo repository.UserRepository,
	commissionRepo repository.CommissionTransactionRepository,
	walletRepo repository.WalletTransactionRepository,
) *PaymentService {
	service := &PaymentService{
		clients:        make(map[PaymentType]PaymentClient),
		orderRepo:      orderRepo,
		userRepo:       userRepo,
		commissionRepo: commissionRepo,
		walletRepo:     walletRepo,
	}

	// 注册支付宝
	service.clients[PaymentTypeAlipay] = alipayService
	// 注册微信支付
	service.clients[PaymentTypeWechat] = wechatService

	return service
}

// getClient 获取指定支付方式的客户端
func (s *PaymentService) getClient(paymentMethod string) (PaymentClient, error) {
	paymentType := PaymentType(paymentMethod)
	client, ok := s.clients[paymentType]
	if !ok {
		return nil, fmt.Errorf("不支持的支付方式: %s", paymentMethod)
	}
	return client, nil
}

// CreatePayment 创建支付订单
func (s *PaymentService) CreatePayment(ctx context.Context, paymentMethod string, orderID string, amount float64, subject, description, notifyURL, clientIP string) (payURL string, err error) {
	client, err := s.getClient(paymentMethod)
	if err != nil {
		return "", err
	}
	return client.CreatePayment(ctx, paymentMethod, orderID, amount, subject, description, notifyURL, clientIP)
}

// HandleCallback 处理支付回调
func (s *PaymentService) HandleCallback(ctx context.Context, paymentMethod string, payload []byte) error {
	client, err := s.getClient(paymentMethod)
	if err != nil {
		return err
	}

	// 调用具体支付方式的回调处理，获取订单号
	orderID, err := client.HandleCallback(ctx, paymentMethod, payload)
	if err != nil {
		return err
	}

	// 如果没有错误，说明验证通过，执行统一的PostPayment逻辑
	return s.PostPayment(ctx, orderID)
}

// PostPayment 统一的支付后处理逻辑
func (s *PaymentService) PostPayment(ctx context.Context, orderID string) error {
	// 1. 根据订单号获取订单信息
	// 注意：需要在 OrderRepository 中添加 GetOrderByOrderNumber 方法
	// GetOrderByOrderNumber(ctx context.Context, orderNumber string) (*model.Order, error)
	order, err := s.orderRepo.GetOrderByOrderNumber(ctx, orderID)
	if err != nil {
		return fmt.Errorf("获取订单信息失败: %w", err)
	}

	// 2. 检查订单状态，如果已经是已支付状态，直接返回
	if order.Status == OrderStatusPaid {
		return nil
	}

	// 3. 如果不是已支付状态，更新为已支付
	// 注意：这里使用现有的 UpdateOrderStatus 方法，需要传入 teamID
	_, err = s.orderRepo.UpdateOrderStatus(ctx, orderID, order.TeamID, OrderStatusPaid)
	if err != nil {
		return fmt.Errorf("更新订单状态失败: %w", err)
	}

	// 4. 处理余额扣减（如果使用了余额支付）
	if order.BalanceAmount > 0 {
		err = s.processBalanceDeduction(ctx, order)
		if err != nil {
			// 余额扣减失败，记录错误但不影响订单状态
			return fmt.Errorf("处理余额扣减失败: %w", err)
		}
	}

	// 5. 处理用户返利逻辑
	err = s.processCommission(ctx, order)
	if err != nil {
		// 返利失败不影响订单状态，只记录错误
		// TODO: 可以考虑发送告警或重试机制
		return fmt.Errorf("处理返利失败: %w", err)
	}

	// TODO: 实现其他支付后处理逻辑
	// 6. 发送通知等

	return nil
}

// processBalanceDeduction 处理余额扣减
func (s *PaymentService) processBalanceDeduction(ctx context.Context, order *model.Order) error {
	// 生成余额扣减描述
	description := fmt.Sprintf("余额支付 - 订单:%s", order.OrderNumber)

	// 创建余额扣减记录（负数表示扣减）
	err := s.walletRepo.CreateTransaction(
		ctx,
		order.UserID,                        // 用户ID
		-order.BalanceAmount,                // 扣减金额（负数）
		order.Currency,                      // 货币类型
		WalletTransactionTypeBalancePayment, // 交易类型：余额支付扣减
		description,                         // 交易描述
		order.ID,                            // 关联订单ID
	)

	if err != nil {
		return fmt.Errorf("创建余额扣减记录失败: %w", err)
	}

	return nil
}

// processCommission 处理返利逻辑
func (s *PaymentService) processCommission(ctx context.Context, order *model.Order) error {
	// 1. 根据订单的 UserID 查询用户信息
	user, err := s.userRepo.FindUserByID(ctx, order.UserID)
	if err != nil {
		return fmt.Errorf("查询下单用户失败: %w", err)
	}

	// 2. 检查用户是否有邀请人
	if user.InviteUserID == nil {
		// 没有邀请人，无需返利
		return nil
	}

	// 3. 查询邀请人信息
	inviteUser, err := s.userRepo.FindUserByID(ctx, *user.InviteUserID)
	if err != nil {
		return fmt.Errorf("查询邀请用户失败: %w", err)
	}

	// 4. 检查邀请人是否有返利配置
	if inviteUser.CommissionType == nil || inviteUser.CommissionRate == nil {
		// 邀请人没有返利配置，无需返利
		return nil
	}

	// 5. 根据返利类型处理
	switch *inviteUser.CommissionType {
	case CommissionTypeFirstTime:
		// 首次返利类型：需要检查是否已有记录
		exists, err := s.commissionRepo.CheckInvitedUserExists(ctx, order.UserID)
		if err != nil {
			return fmt.Errorf("检查被邀请用户是否存在返利记录失败: %w", err)
		}
		if exists {
			// 已有返利记录，不再返利
			return nil
		}
		// 没有记录，进行返利
		return s.createCommissionTransaction(ctx, inviteUser.ID, order, *inviteUser.CommissionRate)

	case CommissionTypeEveryTime:
		// 每次返利类型：直接返利，无需检查
		return s.createCommissionTransaction(ctx, inviteUser.ID, order, *inviteUser.CommissionRate)

	default:
		// 未知的返利类型
		return fmt.Errorf("未知的返利类型: %d", *inviteUser.CommissionType)
	}
}

// createCommissionTransaction 创建返利交易记录
func (s *PaymentService) createCommissionTransaction(ctx context.Context, inviteUserID int32, order *model.Order, commissionRate int16) error {
	// 计算返利金额：实付金额 * 返利比例 / 100
	commissionAmount := order.RealAmount * int64(commissionRate) / 100

	// 生成返利描述
	description := fmt.Sprintf("邀请返利 - 订单:%s", order.OrderNumber)

	// 创建返利交易记录
	err := s.commissionRepo.CreateTransaction(
		ctx,
		inviteUserID,                 // 返利给邀请人
		commissionAmount,             // 返利金额
		order.Currency,               // 货币类型
		TransactionTypeCommission,    // 交易类型：返利
		description,                  // 交易描述
		order.ID,                     // 关联订单ID
		time.Now().Add(72*time.Hour), // 72小时后解锁
	)

	if err != nil {
		return fmt.Errorf("创建返利交易记录失败: %w", err)
	}

	return nil
}

// Refund 申请退款
func (s *PaymentService) Refund(ctx context.Context, paymentMethod string, orderID, tradeNo string, amount float64, reason string) (success bool, err error) {
	client, err := s.getClient(paymentMethod)
	if err != nil {
		return false, err
	}
	return client.Refund(ctx, paymentMethod, orderID, tradeNo, amount, reason)
}
