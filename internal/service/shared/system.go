package shared

import (
	"context"
	"fp-browser/internal/dao/model"
	"fp-browser/internal/repository"
)

// SystemService 提供系统配置读取服务
type SystemService interface {
	// GetConfig 根据key获取单个配置项
	GetConfig(ctx context.Context, key string) (*model.System, error)
	// GetAllConfigs 获取所有系统配置项
	GetAllConfigs(ctx context.Context) ([]*model.System, error)
}

type systemService struct {
	systemRepo repository.SystemRepo
}

// NewSystemService 创建系统配置服务实例
func NewSystemService(repo repository.SystemRepo) SystemService {
	return &systemService{
		systemRepo: repo,
	}
}

// GetConfig 根据key获取单个配置项
func (s *systemService) GetConfig(ctx context.Context, key string) (*model.System, error) {
	return s.systemRepo.GetByKey(ctx, key)
}

// GetAllConfigs 获取所有系统配置项
func (s *systemService) GetAllConfigs(ctx context.Context) ([]*model.System, error) {
	return s.systemRepo.GetAll(ctx)
}
