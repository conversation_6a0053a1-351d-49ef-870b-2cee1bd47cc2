package shared

import (
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"fmt"
	"math/big"
	"strconv"
	"time"

	"github.com/mojocn/base64Captcha"
)

// CaptchaResponse 验证码生成响应
type CaptchaResponse struct {
	CaptchaImage string `json:"captcha_image"` // base64编码的验证码图片
	Answer       string `json:"answer"`        // 验证码答案
	Seed         string `json:"seed"`          // 随机种子
	Target       string `json:"target"`        // PoW难度目标
}

type CaptchaService interface {
	// 生成相关
	GenerateCaptcha() (*CaptchaResponse, error)

	// 验证相关
	VerifyPoW(seed, nonce, target string) bool
}

type captchaService struct {
	captchaDriver base64Captcha.Driver
}

func NewCaptchaService() CaptchaService {
	return &captchaService{
		captchaDriver: base64Captcha.DefaultDriverDigit,
	}
}

// generateTarget 生成随机target（PoW难度调整）
func (c *captchaService) generateTarget() (string, error) {
	target := new(big.Int)
	// 难度调整：前20bit为0，可以通过修改这里调整难度
	target.SetString("00000FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF", 16)
	return target.Text(16), nil
}

// generateSeed 生成带时间戳的随机seed
func (c *captchaService) generateSeed() (string, error) {
	timestamp := time.Now().Unix()
	randomBytes := make([]byte, 8)
	_, err := rand.Read(randomBytes)
	if err != nil {
		return "", fmt.Errorf("生成随机字节失败: %w", err)
	}
	return strconv.FormatInt(timestamp, 10) + "-" + base64.RawURLEncoding.EncodeToString(randomBytes), nil
}

// 生成相关方法

func (c *captchaService) GenerateCaptcha() (*CaptchaResponse, error) {
	// 生成验证码
	_, _, answer := c.captchaDriver.GenerateIdQuestionAnswer()
	item, err := c.captchaDriver.DrawCaptcha(answer)
	if err != nil {
		return nil, fmt.Errorf("生成验证码图片失败: %w", err)
	}

	// 编码为base64
	b64Img := item.EncodeB64string()

	// 生成seed
	seed, err := c.generateSeed()
	if err != nil {
		return nil, fmt.Errorf("生成seed失败: %w", err)
	}

	// 生成target
	target, err := c.generateTarget()
	if err != nil {
		return nil, fmt.Errorf("生成target失败: %w", err)
	}

	return &CaptchaResponse{
		CaptchaImage: b64Img,
		Answer:       answer,
		Seed:         seed,
		Target:       target,
	}, nil
}

// 验证相关方法

func (c *captchaService) VerifyPoW(seed, nonce, target string) bool {
	// 计算hash
	h := sha256.New()
	h.Write([]byte(seed + nonce))
	hash := h.Sum(nil)

	// 转换为big.Int进行比较
	hashInt := new(big.Int).SetBytes(hash)
	targetInt := new(big.Int)
	targetInt.SetString(target, 16)

	// hash值必须小于target值
	return hashInt.Cmp(targetInt) < 0
}
