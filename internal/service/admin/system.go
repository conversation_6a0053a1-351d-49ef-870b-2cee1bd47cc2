package admin

import (
	"context"
	"fp-browser/internal/dao/model"
	"fp-browser/internal/repository"
)

type SystemService interface {
	SaveConfig(ctx context.Context, key string, value string, valueType *string, description *string) error
	SaveConfigs(ctx context.Context, configs []*model.System) error
	GetConfig(ctx context.Context, key string) (*model.System, error)
	GetAllConfigs(ctx context.Context) ([]*model.System, error)
	DeleteConfig(ctx context.Context, key string) error
}

type systemService struct {
	systemRepo repository.SystemRepo
}

func NewSystemService(repo repository.SystemRepo) SystemService {
	return &systemService{
		systemRepo: repo,
	}
}

func (s *systemService) SaveConfig(ctx context.Context, key string, value string, valueType *string, description *string) error {
	system := &model.System{
		Key:         key,
		Value:       value,
		ValueType:   valueType,
		Description: description,
	}
	return s.systemRepo.UpsertOne(ctx, system)
}

func (s *systemService) SaveConfigs(ctx context.Context, configs []*model.System) error {
	if len(configs) == 0 {
		return nil
	}
	return s.systemRepo.UpsertMany(ctx, configs)
}

func (s *systemService) GetConfig(ctx context.Context, key string) (*model.System, error) {
	return s.systemRepo.GetByKey(ctx, key)
}

func (s *systemService) GetAllConfigs(ctx context.Context) ([]*model.System, error) {
	return s.systemRepo.GetAll(ctx)
}

func (s *systemService) DeleteConfig(ctx context.Context, key string) error {
	return s.systemRepo.DeleteByKey(ctx, key)
}
