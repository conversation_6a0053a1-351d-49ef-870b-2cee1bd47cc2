package user

import (
	"context"
	"time"

	"fp-browser/internal/dao/model"
	"fp-browser/internal/repository"
)

type OperationLogService interface {
	CreateLog(ctx context.Context, log *model.OperationLog) error
	GetLogs(
		ctx context.Context,
		teamID, userID, action, category int32,
		target string,
		limit, offset int,
		startTime, endTime *time.Time,
	) ([]*model.OperationLog, int64, error)
}

type operationLogService struct {
	operationLogRepo repository.OperationLogRepository
}

func NewOperationLogService(repo repository.OperationLogRepository) OperationLogService {
	return &operationLogService{
		operationLogRepo: repo,
	}
}

func (s *operationLogService) CreateLog(ctx context.Context, log *model.OperationLog) error {
	return s.operationLogRepo.CreateLog(ctx, log)
}

func (s *operationLogService) GetLogs(
	ctx context.Context,
	teamID, userID, action, category int32,
	target string,
	limit, offset int,
	startTime, endTime *time.Time,
) ([]*model.OperationLog, int64, error) {
	return s.operationLogRepo.GetLogs(ctx, teamID, userID, action, category, target, limit, offset, startTime, endTime)
}
