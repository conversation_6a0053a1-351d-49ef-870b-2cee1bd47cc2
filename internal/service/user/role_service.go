package user

import (
	"context"

	"fp-browser/internal/dao/model"
	"fp-browser/internal/repository"
)

type RoleService interface {
	CreateRole(ctx context.Context, name string, teamID int32, permissions string, secure *bool) error
	GetRoles(ctx context.Context, teamID, id int32) ([]*model.Role, error)
	GetRoleByID(ctx context.Context, roleID int32) (*model.Role, error)
	UpdateRole(ctx context.Context, id, teamID int32, name, permissions string, secure *bool) error
	DeleteRole(ctx context.Context, teamID, roleID int32) error
}

type roleService struct {
	roleRepo repository.RoleRepository
}

func NewRoleService(repo repository.RoleRepository) RoleService {
	return &roleService{
		roleRepo: repo,
	}
}

func (s *roleService) CreateRole(ctx context.Context, name string, teamID int32, permissions string, secure *bool) error {
	return s.roleRepo.CreateRole(ctx, name, teamID, permissions, secure)
}

func (s *roleService) GetRoles(ctx context.Context, teamID, id int32) ([]*model.Role, error) {
	return s.roleRepo.GetRoles(ctx, teamID, id)
}

func (s *roleService) GetRoleByID(ctx context.Context, roleID int32) (*model.Role, error) {
	return s.roleRepo.GetRoleByID(ctx, roleID)
}

func (s *roleService) UpdateRole(ctx context.Context, id, teamID int32, name, permissions string, secure *bool) error {
	return s.roleRepo.UpdateRole(ctx, id, teamID, name, permissions, secure)
}

func (s *roleService) DeleteRole(ctx context.Context, teamID, roleID int32) error {
	return s.roleRepo.DeleteRole(ctx, teamID, roleID)
}
