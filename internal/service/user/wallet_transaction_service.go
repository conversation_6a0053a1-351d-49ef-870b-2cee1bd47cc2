package user

import (
	"context"

	"fp-browser/internal/dao/model"
	"fp-browser/internal/repository"
)

type WalletService interface {
	CreateTransaction(ctx context.Context, userID int32, amount int64, currency string, transactionType int16, description string, referenceID int32) error
	GetTransactionsByUserID(ctx context.Context, userID int32) ([]*model.WalletTransaction, error)
	GetTotalBalance(ctx context.Context, userID int32) (int64, error)
}

type walletService struct {
	walletRepo repository.WalletTransactionRepository
}

func NewWalletService(repo repository.WalletTransactionRepository) WalletService {
	return &walletService{
		walletRepo: repo,
	}
}

func (s *walletService) CreateTransaction(ctx context.Context, userID int32, amount int64, currency string, transactionType int16, description string, referenceID int32) error {
	return s.walletRepo.CreateTransaction(ctx, userID, amount, currency, transactionType, description, referenceID)
}

func (s *walletService) GetTransactionsByUserID(ctx context.Context, userID int32) ([]*model.WalletTransaction, error) {
	return s.walletRepo.GetTransactionsByUserID(ctx, userID)
}

func (s *walletService) GetTotalBalance(ctx context.Context, userID int32) (int64, error) {
	return s.walletRepo.GetTotalBalance(ctx, userID)
}
