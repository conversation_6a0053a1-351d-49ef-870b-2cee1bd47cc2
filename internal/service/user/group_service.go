package user

import (
	"context"
	"fp-browser/internal/dao/model"
	"fp-browser/internal/repository"
	"fp-browser/internal/view_model"
)

type GroupService interface {
	CreateGroup(ctx context.Context, group *model.Group) error
	GetGroups(ctx context.Context, userID, teamID int32) ([]*view_model.GroupWithUser, error)
	UpdateGroup(ctx context.Context, group *model.Group, teamID int32) error
	DeleteGroup(ctx context.Context, id int32, teamID int32) error
}

type groupService struct {
	groupRepo repository.GroupRepository
}

func NewGroupService(repo repository.GroupRepository) GroupService {
	return &groupService{
		groupRepo: repo,
	}
}

func (s *groupService) CreateGroup(ctx context.Context, group *model.Group) error {
	return s.groupRepo.CreateGroup(ctx, group)
}

func (s *groupService) GetGroups(ctx context.Context, userID, teamID int32) ([]*view_model.GroupWithUser, error) {
	return s.groupRepo.GetGroups(ctx, userID, teamID)
}

func (s *groupService) UpdateGroup(ctx context.Context, group *model.Group, teamID int32) error {
	return s.groupRepo.UpdateGroup(ctx, group, teamID)
}

func (s *groupService) DeleteGroup(ctx context.Context, id int32, teamID int32) error {
	return s.groupRepo.DeleteGroup(ctx, id, teamID)
}
