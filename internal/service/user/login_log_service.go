package user

import (
	"context"
	"time"

	"fp-browser/internal/dao/model"
	"fp-browser/internal/repository"
	"fp-browser/internal/view_model"
)

type LoginLogService interface {
	CreateLog(ctx context.Context, userID, teamID int32, loginIP string, ipLocation string) error
	GetLogs(ctx context.Context, teamID, userID int32, limit, offset int, startTime, endTime *time.Time) ([]*model.LoginLog, int64, error)
	GetLogsWithUserName(ctx context.Context, teamID, userID int32, limit, offset int, startTime, endTime *time.Time) ([]*view_model.LoginLogWithUser, int64, error)
}

type loginLogService struct {
	loginLogRepo repository.LoginLogRepository
}

func NewLoginLogService(repo repository.LoginLogRepository) LoginLogService {
	return &loginLogService{
		loginLogRepo: repo,
	}
}

func (s *loginLogService) CreateLog(ctx context.Context, userID, teamID int32, loginIP string, ipLocation string) error {
	return s.loginLogRepo.CreateLog(ctx, userID, teamID, loginIP, ipLocation)
}

func (s *loginLogService) GetLogs(ctx context.Context, teamID, userID int32, limit, offset int, startTime, endTime *time.Time) ([]*model.LoginLog, int64, error) {
	return s.loginLogRepo.GetLogs(ctx, teamID, userID, limit, offset, startTime, endTime)
}

func (s *loginLogService) GetLogsWithUserName(ctx context.Context, teamID, userID int32, limit, offset int, startTime, endTime *time.Time) ([]*view_model.LoginLogWithUser, int64, error) {
	return s.loginLogRepo.GetLogsWithUserName(ctx, teamID, userID, limit, offset, startTime, endTime)
}
