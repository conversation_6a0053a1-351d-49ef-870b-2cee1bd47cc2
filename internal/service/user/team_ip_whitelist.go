package user

import (
	"context"
	"fmt"
	"net"
	"strings"

	v1 "fp-browser/api/v1/user"
	"fp-browser/internal/dao/model"
	"fp-browser/internal/repository"
)

type TeamIPWhitelistService interface {
	// 核心业务方法 - 直接接受请求参数
	CreateIPWhitelist(ctx context.Context, request v1.CreateTeamIPWhitelistRequest, teamID int32) error
	CreateIPWhitelists(ctx context.Context, request v1.CreateTeamIPWhitelistsRequest, teamID int32) error
	DeleteIPWhitelists(ctx context.Context, request v1.DeleteTeamIPWhitelistRequest, teamID int32) error
	GetIPWhitelists(ctx context.Context, teamID int32) ([]*model.TeamIPWhitelist, error)

	// 扩展功能
	CheckIPExists(ctx context.Context, teamID int32, ipAddress string) (bool, error)
	ValidateIPAccess(ctx context.Context, teamID int32, clientIP string) (bool, error)
}

type teamIPWhitelistService struct {
	teamIPWhitelistRepo repository.TeamIPWhitelistRepository
}

func NewTeamIPWhitelistService(repo repository.TeamIPWhitelistRepository) TeamIPWhitelistService {
	return &teamIPWhitelistService{
		teamIPWhitelistRepo: repo,
	}
}

// CreateIPWhitelist 创建单个IP白名单
func (s *teamIPWhitelistService) CreateIPWhitelist(ctx context.Context, request v1.CreateTeamIPWhitelistRequest, teamID int32) error {
	// 业务验证
	if err := s.validateIPRequest(request.IPAddress); err != nil {
		return err
	}

	// 检查IP是否已存在
	cleanIP := s.sanitizeIP(request.IPAddress)
	exists, err := s.teamIPWhitelistRepo.CheckIPExists(ctx, teamID, cleanIP)
	if err != nil {
		return fmt.Errorf("检查IP是否已存在失败: %w", err)
	}
	if exists {
		return fmt.Errorf("IP地址已存在于白名单中: %s", cleanIP)
	}

	// 转换请求数据为模型
	whitelist := s.requestToModel(cleanIP, teamID)

	// 调用repository创建
	return s.teamIPWhitelistRepo.Create(ctx, []*model.TeamIPWhitelist{whitelist})
}

// CreateIPWhitelists 批量创建IP白名单
func (s *teamIPWhitelistService) CreateIPWhitelists(ctx context.Context, request v1.CreateTeamIPWhitelistsRequest, teamID int32) error {
	if len(request.IPAddresses) == 0 {
		return fmt.Errorf("IP地址列表不能为空")
	}

	whitelists := make([]*model.TeamIPWhitelist, 0, len(request.IPAddresses))
	seen := make(map[string]bool)

	for i, ipReq := range request.IPAddresses {
		// 业务验证
		if err := s.validateIPRequest(ipReq.IPAddress); err != nil {
			return fmt.Errorf("第%d个IP地址无效: %v", i+1, err)
		}

		cleanIP := s.sanitizeIP(ipReq.IPAddress)

		// 检查本次请求中的重复
		if seen[cleanIP] {
			return fmt.Errorf("第%d个IP地址重复: %s", i+1, cleanIP)
		}
		seen[cleanIP] = true

		// 检查是否已存在
		exists, err := s.teamIPWhitelistRepo.CheckIPExists(ctx, teamID, cleanIP)
		if err != nil {
			return fmt.Errorf("第%d个IP地址检查失败: %v", i+1, err)
		}
		if exists {
			return fmt.Errorf("第%d个IP地址已存在于白名单中: %s", i+1, cleanIP)
		}

		whitelist := s.requestToModel(cleanIP, teamID)
		whitelists = append(whitelists, whitelist)
	}

	return s.teamIPWhitelistRepo.Create(ctx, whitelists)
}

// DeleteIPWhitelists 批量删除IP白名单
func (s *teamIPWhitelistService) DeleteIPWhitelists(ctx context.Context, request v1.DeleteTeamIPWhitelistRequest, teamID int32) error {
	// 业务验证
	if len(request.IDs) == 0 {
		return fmt.Errorf("IP白名单ID列表不能为空")
	}

	// 验证每个ID的有效性
	for i, id := range request.IDs {
		if id <= 0 {
			return fmt.Errorf("第%d个IP白名单ID无效: %d", i+1, id)
		}
	}

	return s.teamIPWhitelistRepo.Delete(ctx, request.IDs, teamID)
}

// GetIPWhitelists 获取团队IP白名单列表
func (s *teamIPWhitelistService) GetIPWhitelists(ctx context.Context, teamID int32) ([]*model.TeamIPWhitelist, error) {
	// 业务验证
	if teamID <= 0 {
		return nil, fmt.Errorf("团队ID无效: %d", teamID)
	}

	return s.teamIPWhitelistRepo.GetByTeamID(ctx, teamID)
}

// CheckIPExists 检查IP是否存在于白名单中
func (s *teamIPWhitelistService) CheckIPExists(ctx context.Context, teamID int32, ipAddress string) (bool, error) {
	// 业务验证
	if teamID <= 0 {
		return false, fmt.Errorf("团队ID无效: %d", teamID)
	}

	if err := s.validateIPRequest(ipAddress); err != nil {
		return false, fmt.Errorf("IP地址验证失败: %w", err)
	}

	cleanIP := s.sanitizeIP(ipAddress)
	return s.teamIPWhitelistRepo.CheckIPExists(ctx, teamID, cleanIP)
}

// ValidateIPAccess 验证客户端IP是否有访问权限
func (s *teamIPWhitelistService) ValidateIPAccess(ctx context.Context, teamID int32, clientIP string) (bool, error) {
	// 业务验证
	if teamID <= 0 {
		return false, fmt.Errorf("团队ID无效: %d", teamID)
	}

	if clientIP == "" {
		return false, fmt.Errorf("客户端IP不能为空")
	}

	// 清理和验证客户端IP
	cleanIP := s.sanitizeIP(clientIP)
	if err := s.validateIPRequest(cleanIP); err != nil {
		return false, fmt.Errorf("客户端IP地址无效: %w", err)
	}

	// 获取团队的IP白名单
	whitelists, err := s.teamIPWhitelistRepo.GetByTeamID(ctx, teamID)
	if err != nil {
		return false, fmt.Errorf("获取IP白名单失败: %w", err)
	}

	// 如果没有配置白名单，默认允许访问
	if len(whitelists) == 0 {
		return true, nil
	}

	// 检查客户端IP是否在白名单中
	for _, whitelist := range whitelists {
		if whitelist.IPAddress == cleanIP {
			return true, nil
		}

		// 支持CIDR格式的网段匹配
		if strings.Contains(whitelist.IPAddress, "/") {
			_, ipNet, err := net.ParseCIDR(whitelist.IPAddress)
			if err == nil {
				clientIPObj := net.ParseIP(cleanIP)
				if clientIPObj != nil && ipNet.Contains(clientIPObj) {
					return true, nil
				}
			}
		}
	}

	// IP不在白名单中
	return false, nil
}

// requestToModel 将请求数据转换为模型
func (s *teamIPWhitelistService) requestToModel(ipAddress string, teamID int32) *model.TeamIPWhitelist {
	return &model.TeamIPWhitelist{
		TeamID:    teamID,
		IPAddress: ipAddress,
	}
}

// validateIPRequest 验证IP请求数据
func (s *teamIPWhitelistService) validateIPRequest(ipAddress string) error {
	if ipAddress == "" {
		return fmt.Errorf("IP地址不能为空")
	}

	// 清理IP地址
	cleanIP := s.sanitizeIP(ipAddress)

	// 如果是CIDR格式，验证网段
	if strings.Contains(cleanIP, "/") {
		_, _, err := net.ParseCIDR(cleanIP)
		if err != nil {
			return fmt.Errorf("CIDR格式无效: %s", cleanIP)
		}
		return nil
	}

	// 验证单个IP地址格式
	ip := net.ParseIP(cleanIP)
	if ip == nil {
		return fmt.Errorf("IP地址格式无效: %s", cleanIP)
	}

	return nil
}

// sanitizeIP 清理IP地址（去除空格等）
func (s *teamIPWhitelistService) sanitizeIP(ipAddress string) string {
	return strings.TrimSpace(ipAddress)
}
