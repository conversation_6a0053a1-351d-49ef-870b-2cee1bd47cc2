package user

import (
	"context"

	"fp-browser/internal/dao/model"
	"fp-browser/internal/repository"
)

type UserSubscriptionService interface {
	GetSubscriptionByID(ctx context.Context, id int32) (*model.UserSubscription, error)
	GetActiveSubscriptionByTeam(ctx context.Context, teamID int32) (*model.UserSubscription, error)
	GetAllSubscriptionsByTeam(ctx context.Context, teamID int32) ([]*model.UserSubscription, error)
	CreateSubscription(ctx context.Context, subscription *model.UserSubscription) error
	UpdateSubscription(ctx context.Context, subscription *model.UserSubscription) error
}

type userSubscriptionService struct {
	subscriptionRepo repository.UserSubscriptionRepository
}

func NewUserSubscriptionService(repo repository.UserSubscriptionRepository) UserSubscriptionService {
	return &userSubscriptionService{
		subscriptionRepo: repo,
	}
}

func (s *userSubscriptionService) GetSubscriptionByID(ctx context.Context, id int32) (*model.UserSubscription, error) {
	return s.subscriptionRepo.FindByID(ctx, id)
}

func (s *userSubscriptionService) GetActiveSubscriptionByTeam(ctx context.Context, teamID int32) (*model.UserSubscription, error) {
	return s.subscriptionRepo.FindActiveByTeam(ctx, teamID)
}

func (s *userSubscriptionService) GetAllSubscriptionsByTeam(ctx context.Context, teamID int32) ([]*model.UserSubscription, error) {
	return s.subscriptionRepo.FindAllByTeam(ctx, teamID)
}

func (s *userSubscriptionService) CreateSubscription(ctx context.Context, subscription *model.UserSubscription) error {
	return s.subscriptionRepo.Create(ctx, subscription)
}

func (s *userSubscriptionService) UpdateSubscription(ctx context.Context, subscription *model.UserSubscription) error {
	return s.subscriptionRepo.Update(ctx, subscription)
}
