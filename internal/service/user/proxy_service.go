package user

import (
	"context"
	"time"

	"fp-browser/internal/dao/model"
	"fp-browser/internal/repository"
)

type ProxyService interface {
	Update(ctx context.Context, proxy *model.Proxy) error
	GetProxies(ctx context.Context, id, teamID, environmentID int32, name string, limit, offset int, expiresAt *time.Time, autoRenew *bool) ([]*model.Proxy, int64, error)
}

type proxyService struct {
	proxyRepo repository.ProxyRepository
}

func NewProxyService(repo repository.ProxyRepository) ProxyService {
	return &proxyService{
		proxyRepo: repo,
	}
}

func (s *proxyService) Update(ctx context.Context, proxy *model.Proxy) error {
	return s.proxyRepo.Update(ctx, proxy)
}

func (s *proxyService) GetProxies(ctx context.Context, id, teamID, environmentID int32, name string, limit, offset int, expiresAt *time.Time, autoRenew *bool) ([]*model.Proxy, int64, error) {
	return s.proxyRepo.GetProxies(ctx, id, teamID, environmentID, name, limit, offset, expiresAt, autoRenew)
}
