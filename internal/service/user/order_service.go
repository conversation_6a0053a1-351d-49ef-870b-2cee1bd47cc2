package user

import (
	"context"
	"time"

	"fp-browser/internal/dao/model"
	"fp-browser/internal/repository"
)

type OrderService interface {
	GetOrder(ctx context.Context, orderNumber string, userID, teamID int32) (*model.Order, error)
	GetOrders(ctx context.Context, userID, teamID int32, orderType, status, paymentMethod int16, limit, offset int, startTime, endTime *time.Time) ([]*model.Order, error)
	Create(ctx context.Context, order *model.Order) (*model.Order, error)
	UpdateOrderStatus(ctx context.Context, orderNumber string, teamID int32, status int16) (*model.Order, error)
}

type orderService struct {
	orderRepo repository.OrderRepository
}

func NewOrderService(repo repository.OrderRepository) OrderService {
	return &orderService{
		orderRepo: repo,
	}
}

func (s *orderService) GetOrder(ctx context.Context, orderNumber string, userID, teamID int32) (*model.Order, error) {
	return s.orderRepo.GetOrder(ctx, orderNumber, userID, teamID)
}

func (s *orderService) GetOrders(ctx context.Context, userID, teamID int32, orderType, status, paymentMethod int16, limit, offset int, startTime, endTime *time.Time) ([]*model.Order, error) {
	return s.orderRepo.GetOrders(ctx, userID, teamID, orderType, status, paymentMethod, limit, offset, startTime, endTime)
}

func (s *orderService) Create(ctx context.Context, order *model.Order) (*model.Order, error) {
	return s.orderRepo.Create(ctx, order)
}

func (s *orderService) UpdateOrderStatus(ctx context.Context, orderNumber string, teamID int32, status int16) (*model.Order, error) {
	return s.orderRepo.UpdateOrderStatus(ctx, orderNumber, teamID, status)
}
