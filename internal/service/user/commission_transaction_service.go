package user

import (
	"context"
	"time"

	"fp-browser/internal/dao/model"
	"fp-browser/internal/repository"
)

type CommissionService interface {
	CreateCommission(ctx context.Context, userID int32, amount int64, currency string, transactionType int16, description string, referenceID int32) error
	ListTransactions(ctx context.Context, userID int32, limit, offset int, startTime, endTime *time.Time) ([]*model.CommissionTransaction, error)
	GetWithdrawableTransactions(ctx context.Context, userID int32) ([]*model.CommissionTransaction, error)
	GetTotalCommission(ctx context.Context, userID int32) (int64, error)
	GetTotalWithdrawals(ctx context.Context, userID int32) (int64, error)
	GetWithdrawableAmount(ctx context.Context, userID int32) (int64, error)
}

type commissionService struct {
	commissionRepo repository.CommissionTransactionRepository
}

func NewCommissionService(repo repository.CommissionTransactionRepository) CommissionService {
	return &commissionService{
		commissionRepo: repo,
	}
}

func (s *commissionService) CreateCommission(ctx context.Context, userID int32, amount int64, currency string, transactionType int16, description string, referenceID int32) error {
	// unlockTime 只在收入类型（非提现） 且 referenceID 存在时生效
	var unlockTime time.Time
	if referenceID != 0 && transactionType != 2 && transactionType != 3 {
		unlockTime = time.Now().Add(72 * time.Hour)
	} else {
		unlockTime = time.Now()
	}
	return s.commissionRepo.CreateTransaction(ctx, userID, amount, currency, transactionType, description, referenceID, unlockTime)
}

func (s *commissionService) ListTransactions(ctx context.Context, userID int32, limit, offset int, startTime, endTime *time.Time) ([]*model.CommissionTransaction, error) {
	return s.commissionRepo.GetTransactionsByUserID(ctx, userID, limit, offset, startTime, endTime)
}

func (s *commissionService) GetWithdrawableTransactions(ctx context.Context, userID int32) ([]*model.CommissionTransaction, error) {
	return s.commissionRepo.GetWithdrawableTransactions(ctx, userID)
}

func (s *commissionService) GetTotalCommission(ctx context.Context, userID int32) (int64, error) {
	return s.commissionRepo.GetTotalCommission(ctx, userID)
}

func (s *commissionService) GetTotalWithdrawals(ctx context.Context, userID int32) (int64, error) {
	return s.commissionRepo.GetTotalWithdrawals(ctx, userID)
}

func (s *commissionService) GetWithdrawableAmount(ctx context.Context, userID int32) (int64, error) {
	return s.commissionRepo.GetAvailableWithdrawableAmount(ctx, userID)
}
