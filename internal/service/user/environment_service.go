package user

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	v1 "fp-browser/api/v1/user"
	"fp-browser/internal/dao/model"
	"fp-browser/internal/repository"
	"fp-browser/internal/service/shared"
	"fp-browser/internal/view_model"
)

type EnvironmentService interface {
	// 基本环境管理功能
	CreateEnvironment(ctx context.Context, req *v1.CreateEnvironmentRequest, userID, teamID int32) error
	GetEnvironments(ctx context.Context, req *v1.GetEnvironmentsRequest, teamID int32) ([]*view_model.EnvironmentListItem, int64, error)
	GetEnvironmentByID(ctx context.Context, envID, userID, teamID int32) (*view_model.EnvironmentWithProxy, error)
	UpdateEnvironment(ctx context.Context, req *v1.UpdateEnvironmentRequest, teamID int32) error
	DeleteEnvironment(ctx context.Context, req *v1.DeleteEnvironmentRequest, teamID int32) error
	RefreshEnvironmentOccupation(ctx context.Context, envID, userID int32) error
	ReleaseEnvironmentOccupation(ctx context.Context, envID, userID int32) error

	// 新增：更新环境代理
	UpdateEnvironmentProxy(ctx context.Context, req *v1.UpdateEnvironmentProxyRequest, teamID int32) error

	// 已删除环境管理功能
	GetDeletedEnvironments(ctx context.Context, limit, offset int, teamID int32) ([]*view_model.EnvironmentListItem, int64, error)
	RestoreEnvironment(ctx context.Context, req *v1.DeleteEnvironmentRequest, teamID int32) error
	PermanentDeleteEnvironment(ctx context.Context, req *v1.DeleteEnvironmentRequest, teamID int32) error

	// 环境文件功能
	GenerateEnvironmentUploadURL(ctx context.Context, req *v1.GenerateEnvironmentURLRequest, userID, teamID int32) (*v1.GenerateEnvironmentURLResponse, error)
	GenerateEnvironmentDownloadURL(ctx context.Context, req *v1.GenerateEnvironmentURLRequest, userID, teamID int32) (*v1.GenerateEnvironmentURLResponse, error)
	DeleteEnvironmentFile(ctx context.Context, req *v1.DeleteEnvironmentFileRequest, userID, teamID int32) error

	// 新增：获取环境存储信息
	GetEnvironmentStorageInfo(ctx context.Context, envID, userID, teamID int32) (*view_model.EnvironmentWithProxy, error)
}

type environmentService struct {
	environmentRepo repository.EnvironmentRepository
	userService     UserService
	redisService    shared.RedisService
	ossService      shared.OSSService
}

func NewEnvironmentService(
	environmentRepo repository.EnvironmentRepository,
	userService UserService,
	redisService shared.RedisService,
	ossService shared.OSSService,
) EnvironmentService {
	return &environmentService{
		environmentRepo: environmentRepo,
		userService:     userService,
		redisService:    redisService,
		ossService:      ossService,
	}
}

func (s *environmentService) CreateEnvironment(ctx context.Context, req *v1.CreateEnvironmentRequest, userID, teamID int32) error {
	// 1. 业务逻辑验证
	if req.Name == "" {
		return fmt.Errorf("环境名称不能为空")
	}
	if req.ProxyType != 1 && req.ProxyType != 2 {
		return fmt.Errorf("代理类型无效")
	}

	// 去除备注的空白字符
	var comment *string
	if strings.TrimSpace(req.Comment) != "" { // 去除空白字符后判断
		comment = &req.Comment
	}

	// 处理JSON字段，确保不为空字符串
	parameters := req.Parameters
	if strings.TrimSpace(parameters) == "" {
		parameters = "{}"
	}

	tag := req.Tag
	if strings.TrimSpace(tag) == "" {
		tag = "{}"
	}

	// 2. 数据模型构造
	env := &model.Environment{
		TeamID:     teamID,
		Name:       req.Name,
		UserID:     userID,
		GroupID:    req.GroupID,
		ProxyID:    req.ProxyID,
		ProxyType:  req.ProxyType,
		Platform:   req.Platform,
		Parameters: parameters,
		Storage:    req.Storage,
		Tag:        tag,
		Comment:    comment,
		Sort:       &req.Sort,
	}

	// 3. 调用Repository层创建
	return s.environmentRepo.CreateEnvironment(ctx, env)
}

func (s *environmentService) GetEnvironments(ctx context.Context, req *v1.GetEnvironmentsRequest, teamID int32) ([]*view_model.EnvironmentListItem, int64, error) {
	// 1. 参数验证和默认值处理
	limit := req.Limit
	if limit > 500 {
		limit = 500
	}
	if limit <= 0 {
		limit = 100
	}

	offset := req.Offset
	if offset < 0 {
		offset = 0
	}

	// 2. 调用Repository层查询
	return s.environmentRepo.GetEnvironments(ctx, teamID, req.Name, req.UserID, req.GroupID, limit, offset)
}

func (s *environmentService) GetEnvironmentByID(ctx context.Context, envID, userID, teamID int32) (*view_model.EnvironmentWithProxy, error) {
	// 1. Redis 占用检查逻辑
	redisKey := fmt.Sprintf("env:%d", envID)
	storedUserID, err := s.redisService.Get(ctx, redisKey)

	// 2. 检查是否被其他用户占用
	if err == nil && storedUserID != "" {
		storedUserIDInt, parseErr := strconv.Atoi(storedUserID)
		if parseErr == nil && int32(storedUserIDInt) != userID {
			// 环境已被其他用户占用，获取占用用户信息
			otherUser, userErr := s.userService.GetUser(ctx, int32(storedUserIDInt))
			if userErr == nil {
				return nil, fmt.Errorf("环境已被用户 %s 占用", otherUser.UserName)
			}
			return nil, fmt.Errorf("环境已被其他用户占用")
		}
	} else if err != nil && !s.redisService.IsNil(err) {
		// Redis查询出错但不是key不存在的错误，记录日志但不阻断流程
		// 这里可以记录日志，但继续执行
	}

	// 3. 获取环境信息
	environment, err := s.environmentRepo.GetEnvironmentByID(ctx, envID, teamID)
	if err != nil {
		return nil, fmt.Errorf("获取环境信息失败: %w", err)
	}

	// 4. 设置占用记录（60秒过期）
	if err := s.redisService.Set(ctx, redisKey, strconv.Itoa(int(userID)), 60*time.Second); err != nil {
		// Redis设置失败不阻断主流程，只记录日志
		// 这里可以记录日志
	}

	return environment, nil
}

func (s *environmentService) UpdateEnvironment(ctx context.Context, req *v1.UpdateEnvironmentRequest, teamID int32) error {
	// 1. 业务逻辑验证
	if len(req.Environments) == 0 {
		return fmt.Errorf("更新环境列表不能为空")
	}

	// 2. 构造待更新的Environment对象列表
	environments := make([]*model.Environment, 0, len(req.Environments))
	for _, envReq := range req.Environments {
		// 只有传了Name字段才验证
		if envReq.Name != nil && *envReq.Name == "" {
			return fmt.Errorf("环境名称不能为空")
		}

		// 只有传了ProxyType字段才验证代理类型
		if envReq.ProxyType != nil && *envReq.ProxyType != 0 && *envReq.ProxyType != 1 && *envReq.ProxyType != 2 {
			return fmt.Errorf("代理类型无效")
		}

		// 处理Parameters字段，只有传了才处理
		var parameters string
		if envReq.Parameters != nil {
			parameters = *envReq.Parameters
			if strings.TrimSpace(parameters) == "" {
				parameters = "{}"
			}
		}

		// 处理Tag字段，只有传了才处理
		var tag string
		if envReq.Tag != nil {
			tag = *envReq.Tag
			if strings.TrimSpace(tag) == "" {
				tag = "{}"
			}
		}

		// 构造Environment对象，只设置传递了的字段
		env := &model.Environment{
			ID: envReq.ID, // ID始终需要
		}

		// 根据指针是否为nil来决定是否设置字段
		if envReq.Name != nil {
			env.Name = *envReq.Name
		}
		if envReq.GroupID != nil {
			env.GroupID = *envReq.GroupID
		}
		if envReq.ProxyID != nil {
			env.ProxyID = *envReq.ProxyID
		}
		if envReq.ProxyType != nil {
			env.ProxyType = *envReq.ProxyType
		}
		if envReq.Platform != nil {
			env.Platform = *envReq.Platform
		}
		if envReq.Parameters != nil {
			env.Parameters = parameters
		}
		if envReq.Storage != nil {
			env.Storage = *envReq.Storage
		}
		if envReq.Tag != nil {
			env.Tag = tag
		}
		if envReq.Comment != nil {
			if strings.TrimSpace(*envReq.Comment) != "" {
				env.Comment = envReq.Comment
			} else {
				env.Comment = nil
			}
		}
		if envReq.Sort != nil {
			env.Sort = envReq.Sort
		}

		environments = append(environments, env)
	}

	// 3. 调用Repository层更新
	return s.environmentRepo.UpdateEnvironment(ctx, environments, teamID)
}

func (s *environmentService) DeleteEnvironment(ctx context.Context, req *v1.DeleteEnvironmentRequest, teamID int32) error {
	// 1. 业务逻辑验证
	if len(req.IDs) == 0 {
		return fmt.Errorf("删除环境ID列表不能为空")
	}

	// 2. 调用Repository层删除
	return s.environmentRepo.DeleteEnvironment(ctx, req.IDs, teamID)
}

func (s *environmentService) RefreshEnvironmentOccupation(ctx context.Context, envID, userID int32) error {
	// 1. 生成Redis键
	redisKey := fmt.Sprintf("env:%d", envID)

	// 2. 检查当前占用者
	storedValue, err := s.redisService.Get(ctx, redisKey)
	if err != nil && !s.redisService.IsNil(err) {
		return fmt.Errorf("获取环境占用信息失败: %w", err)
	}

	// 3. 验证权限：如果环境被占用，只有占用者才能刷新
	if storedValue != "" && storedValue != strconv.Itoa(int(userID)) {
		return fmt.Errorf("记录已被其他用户占用，无法刷新")
	}

	// 4. 刷新占用记录
	return s.redisService.Set(ctx, redisKey, strconv.Itoa(int(userID)), 60*time.Second)
}

func (s *environmentService) ReleaseEnvironmentOccupation(ctx context.Context, envID, userID int32) error {
	// 1. 生成Redis键
	redisKey := fmt.Sprintf("env:%d", envID)

	// 2. 检查当前占用者
	storedValue, err := s.redisService.Get(ctx, redisKey)
	if err != nil {
		if s.redisService.IsNil(err) {
			return nil // 记录不存在，视为释放成功
		}
		return fmt.Errorf("检查环境占用信息失败: %w", err)
	}

	// 3. 验证权限：只有占用者才能释放
	if storedValue != strconv.Itoa(int(userID)) {
		return fmt.Errorf("无权限删除该记录")
	}

	// 4. 释放占用记录
	return s.redisService.Delete(ctx, redisKey)
}

// ==================== 环境文件功能 ====================

// GenerateEnvironmentUploadURL 生成环境上传URL
func (s *environmentService) GenerateEnvironmentUploadURL(ctx context.Context, req *v1.GenerateEnvironmentURLRequest, userID, teamID int32) (*v1.GenerateEnvironmentURLResponse, error) {
	// 1. 参数验证
	if req.EnvironmentID <= 0 {
		return nil, fmt.Errorf("环境ID无效")
	}

	// 2. 获取环境存储信息（高性能，只查询必要字段）
	env, err := s.environmentRepo.GetEnvironmentStorageInfo(ctx, req.EnvironmentID, teamID)
	if err != nil {
		return nil, fmt.Errorf("获取环境信息失败: %w", err)
	}

	if env == nil {
		return nil, fmt.Errorf("环境不存在或无访问权限")
	}

	// 3. 检查用户Size容量
	if env.Size == nil || *env.Size <= 0 {
		return nil, fmt.Errorf("未开通文件共享")
	}

	// 4. 使用环境ID作为标识符
	envIdStr := strconv.Itoa(int(req.EnvironmentID))
	expireSeconds := int64(600) // 默认10分钟

	// 5. 调用OSS服务生成上传URL（直接上传模式）
	uploadResponse, err := s.ossService.GenerateSimpleUploadURL(
		ctx, envIdStr, time.Duration(expireSeconds)*time.Second)
	if err != nil {
		return nil, fmt.Errorf("生成上传URL失败: %w", err)
	}

	// 6. 如果是新文件，更新环境的Storage字段
	if env.Storage == "" {
		// 从OSS服务生成对象名称
		objectName := s.ossService.GenerateObjectName(envIdStr)
		envToUpdate := &model.Environment{
			ID:      req.EnvironmentID,
			Storage: objectName,
		}
		environments := []*model.Environment{envToUpdate}
		if err := s.environmentRepo.UpdateEnvironment(ctx, environments, teamID); err != nil {
			return nil, fmt.Errorf("更新环境存储信息失败: %w", err)
		}
	}

	// 7. 返回上传URL和过期时间给客户端
	// 客户端将使用该URL直接向OSS上传文件，无需通过我们的服务器
	return &v1.GenerateEnvironmentURLResponse{
		URL:      uploadResponse.URL,
		ExpireAt: time.Now().Add(time.Duration(expireSeconds) * time.Second).Unix(),
	}, nil
}

// GenerateEnvironmentDownloadURL 生成环境下载URL
func (s *environmentService) GenerateEnvironmentDownloadURL(ctx context.Context, req *v1.GenerateEnvironmentURLRequest, userID, teamID int32) (*v1.GenerateEnvironmentURLResponse, error) {
	// 1. 参数验证
	if req.EnvironmentID <= 0 {
		return nil, fmt.Errorf("环境ID无效")
	}

	// 2. 获取环境存储信息（高性能，只查询必要字段）
	env, err := s.environmentRepo.GetEnvironmentStorageInfo(ctx, req.EnvironmentID, teamID)
	if err != nil {
		return nil, fmt.Errorf("获取环境信息失败: %w", err)
	}

	if env == nil {
		return nil, fmt.Errorf("环境不存在或无访问权限")
	}

	// 3. 检查用户Size容量
	if env.Size == nil || *env.Size <= 0 {
		return nil, fmt.Errorf("未开通文件共享")
	}

	// 检查Storage字段是否为空
	if env.Storage == "" {
		return nil, fmt.Errorf("环境文件不存在")
	}

	// 4. 检查文件是否存在
	exists, err := s.ossService.CheckObjectExists(ctx, env.Storage)
	if err != nil {
		return nil, fmt.Errorf("检查环境文件失败: %w", err)
	}
	if !exists {
		return nil, fmt.Errorf("环境文件不存在或已被删除")
	}

	// 5. 生成token，以便验证下载请求
	expireSeconds := int64(600) // 默认10分钟
	expireAt := time.Now().Add(time.Duration(expireSeconds) * time.Second)

	// 生成下载令牌（实际项目中可能需要加密处理）
	downloadToken := fmt.Sprintf("env_%d_%d_%d", req.EnvironmentID, userID, expireAt.Unix())

	// 6. 构造指向我们自己服务器的下载URL，而不是直接返回OSS的URL
	// 这样我们的服务器可以作为中间层验证和转发请求
	downloadURL := fmt.Sprintf("/api/v1/environments/%d/download?token=%s",
		req.EnvironmentID, downloadToken)

	// 7. 将下载token存入Redis，后续下载时验证
	tokenKey := fmt.Sprintf("env_download:%d", req.EnvironmentID)
	if err := s.redisService.Set(ctx, tokenKey, downloadToken, time.Until(expireAt)); err != nil {
		return nil, fmt.Errorf("生成下载令牌失败: %w", err)
	}

	// 8. 返回结果
	return &v1.GenerateEnvironmentURLResponse{
		URL:      downloadURL,
		ExpireAt: expireAt.Unix(),
	}, nil
}

// DeleteEnvironmentFile 删除环境文件
func (s *environmentService) DeleteEnvironmentFile(ctx context.Context, req *v1.DeleteEnvironmentFileRequest, userID, teamID int32) error {
	// 1. 参数验证
	if req.EnvironmentID <= 0 {
		return fmt.Errorf("环境ID无效")
	}

	// 2. 轻量级验证环境权限
	exists, err := s.environmentRepo.ValidateEnvironmentAccess(ctx, req.EnvironmentID, teamID)
	if err != nil {
		return fmt.Errorf("验证环境权限失败: %w", err)
	}
	if !exists {
		return fmt.Errorf("环境不存在或无访问权限")
	}

	// 3. 获取环境存储信息（为了获取Storage路径）
	env, err := s.environmentRepo.GetEnvironmentStorageInfo(ctx, req.EnvironmentID, teamID)
	if err != nil {
		return fmt.Errorf("获取环境存储信息失败: %w", err)
	}

	// 如果环境有存储路径，从OSS中删除文件
	if env != nil && env.Storage != "" {
		// 调用OSS服务删除文件
		_, err := s.ossService.DeleteObjectIfExists(ctx, env.Storage)
		if err != nil {
			return fmt.Errorf("从OSS删除文件失败: %w", err)
		}
	}

	// 4. 从Environment模型中清除存储路径
	envModel := &model.Environment{
		ID:      req.EnvironmentID,
		Storage: "",
	}

	// 5. 更新数据库
	environments := []*model.Environment{envModel}
	return s.environmentRepo.UpdateEnvironment(ctx, environments, teamID)
}

// GetEnvironmentStorageInfo 获取环境存储信息
func (s *environmentService) GetEnvironmentStorageInfo(ctx context.Context, envID, userID, teamID int32) (*view_model.EnvironmentWithProxy, error) {
	// 1. 检查参数有效性
	if envID <= 0 {
		return nil, fmt.Errorf("环境ID无效")
	}

	// 2. Redis 占用检查逻辑
	redisKey := fmt.Sprintf("env:%d", envID)
	storedUserID, err := s.redisService.Get(ctx, redisKey)

	// 3. 检查是否被其他用户占用
	if err == nil && storedUserID != "" {
		storedUserIDInt, parseErr := strconv.Atoi(storedUserID)
		if parseErr == nil && int32(storedUserIDInt) != userID {
			// 环境已被其他用户占用，获取占用用户信息
			otherUser, userErr := s.userService.GetUser(ctx, int32(storedUserIDInt))
			if userErr == nil {
				return nil, fmt.Errorf("环境已被用户 %s 占用", otherUser.UserName)
			}
			return nil, fmt.Errorf("环境已被其他用户占用")
		}
	}

	// 4. 获取环境信息（包括存储信息）
	// 这里调用GetEnvironmentByID而不是GetEnvironmentStorageInfo，因为handler需要的是EnvironmentWithProxy
	environment, err := s.environmentRepo.GetEnvironmentByID(ctx, envID, teamID)
	if err != nil {
		return nil, fmt.Errorf("获取环境信息失败: %w", err)
	}

	if environment == nil {
		return nil, fmt.Errorf("环境不存在或无访问权限")
	}

	// 5. 如果有Storage字段，检查OSS文件是否存在
	if environment.Storage != "" {
		exists, checkErr := s.ossService.CheckObjectExists(ctx, environment.Storage)
		if checkErr != nil {
			// 记录错误但不阻断流程
			// 这里可以添加日志记录
		} else if !exists {
			// 文件不存在，但我们仍然返回存储信息
			// 如果需要标记文件状态，可以考虑扩展EnvironmentWithProxy结构体
			// 或者在前端根据需要再次查询文件状态
		}
		// 注意: 如果需要返回文件状态，需要扩展EnvironmentWithProxy结构体
	}

	// 6. 更新Redis占用信息
	if err := s.redisService.Set(ctx, redisKey, strconv.Itoa(int(userID)), 60*time.Second); err != nil {
		// Redis设置失败不阻断主流程，只记录日志
		// 这里可以添加日志记录
	}

	return environment, nil
}

// GetDeletedEnvironments 获取已删除的环境列表
func (s *environmentService) GetDeletedEnvironments(ctx context.Context, limit, offset int, teamID int32) ([]*view_model.EnvironmentListItem, int64, error) {
	// 1. 参数验证和默认值处理
	if limit > 500 {
		limit = 500
	}
	if limit <= 0 {
		limit = 100
	}

	if offset < 0 {
		offset = 0
	}

	// 2. 调用Repository层查询已删除的环境
	return s.environmentRepo.GetDeletedEnvironments(ctx, teamID, limit, offset)
}

// RestoreEnvironment 恢复已删除的环境
func (s *environmentService) RestoreEnvironment(ctx context.Context, req *v1.DeleteEnvironmentRequest, teamID int32) error {
	// 1. 业务逻辑验证
	if len(req.IDs) == 0 {
		return fmt.Errorf("恢复环境ID列表不能为空")
	}

	// 2. 调用Repository层恢复环境
	return s.environmentRepo.RestoreEnvironment(ctx, req.IDs, teamID)
}

// PermanentDeleteEnvironment 永久删除环境
func (s *environmentService) PermanentDeleteEnvironment(ctx context.Context, req *v1.DeleteEnvironmentRequest, teamID int32) error {
	// 1. 业务逻辑验证
	if len(req.IDs) == 0 {
		return fmt.Errorf("删除环境ID列表不能为空")
	}

	// 2. 获取要删除的环境存储信息，用于清理OSS文件
	environments := make([]*model.Environment, 0, len(req.IDs))
	for _, envID := range req.IDs {
		env, err := s.environmentRepo.GetEnvironmentStorageInfo(ctx, envID, teamID)
		if err != nil {
			// 记录错误但继续处理其他环境
			continue
		}
		if env != nil && env.Storage != "" {
			environments = append(environments, env)
		}
	}

	// 3. 先从OSS删除文件（在数据库删除之前）
	for _, env := range environments {
		if env.Storage != "" {
			_, err := s.ossService.DeleteObjectIfExists(ctx, env.Storage)
			if err != nil {
				// 记录错误但不阻断流程，继续删除数据库记录
				// 这里可以添加日志记录
			}
		}
	}

	// 4. 永久删除数据库记录
	return s.environmentRepo.PermanentDeleteEnvironment(ctx, req.IDs, teamID)
}

// UpdateEnvironmentProxy 批量更新环境代理ID
func (s *environmentService) UpdateEnvironmentProxy(ctx context.Context, req *v1.UpdateEnvironmentProxyRequest, teamID int32) error {
	// 1. 业务逻辑验证
	if len(req.IDs) == 0 {
		return fmt.Errorf("环境ID列表不能为空")
	}

	if req.ProxyID <= 0 {
		return fmt.Errorf("代理ID无效")
	}

	// 2. 调用Repository层更新代理ID
	return s.environmentRepo.UpdateEnvironmentProxyID(ctx, req.IDs, req.ProxyID, teamID)
}
