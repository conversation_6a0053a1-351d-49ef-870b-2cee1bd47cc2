package user

import (
	"context"
	"fmt"

	"fp-browser/internal/dao/model"
	"fp-browser/internal/repository"
)

type TeamService interface {
	UpdateTeam(ctx context.Context, id int32, name string) error
	GetTeamByID(ctx context.Context, id uint) (*model.Team, error)
	CreateTeam(ctx context.Context, name string, ownerID uint) (*model.Team, error)
	GetUserList(ctx context.Context, username string, teamID, roleID int32, limit, offset int) ([]*model.User, int64, error)
	AddUsersToTeam(ctx context.Context, user *model.User) error
	DeleteUsersFromTeam(ctx context.Context, teamID int32, userIDs []int32) error
	UpdateUsersInTeam(ctx context.Context, teamID int32, users []*model.User) error
}

type teamService struct {
	teamRepo         repository.TeamRepository
	userRepo         repository.UserRepository
	subscriptionRepo repository.UserSubscriptionRepository
}

func NewTeamService(teamRepo repository.TeamRepository, userRepo repository.UserRepository, subscriptionRepo repository.UserSubscriptionRepository) TeamService {
	return &teamService{
		teamRepo:         teamRepo,
		userRepo:         userRepo,
		subscriptionRepo: subscriptionRepo,
	}
}

func (s *teamService) UpdateTeam(ctx context.Context, id int32, name string) error {
	return s.teamRepo.Update(ctx, id, name)
}

func (s *teamService) GetTeamByID(ctx context.Context, id uint) (*model.Team, error) {
	return s.teamRepo.GetByID(ctx, int32(id))
}

func (s *teamService) CreateTeam(ctx context.Context, name string, ownerID uint) (*model.Team, error) {
	return s.teamRepo.Create(ctx, name, int32(ownerID))
}

func (s *teamService) GetUserList(ctx context.Context, username string, teamID, roleID int32, limit, offset int) ([]*model.User, int64, error) {
	return s.userRepo.GetUsers(ctx, username, teamID, roleID, limit, offset)
}

func (s *teamService) AddUsersToTeam(ctx context.Context, user *model.User) error {
	// 检查订阅人数限制
	if err := s.checkSubscriptionMemberLimit(ctx, user.TeamID, 1); err != nil {
		return err
	}

	_, err := s.userRepo.Create(ctx, user)
	return err
}

func (s *teamService) DeleteUsersFromTeam(ctx context.Context, teamID int32, userIDs []int32) error {
	return s.userRepo.Delete(ctx, userIDs, teamID)
}

func (s *teamService) UpdateUsersInTeam(ctx context.Context, teamID int32, users []*model.User) error {
	// 检查订阅人数限制 - 这里假设更新可能会增加用户，所以检查所有用户数量
	if err := s.checkSubscriptionMemberLimit(ctx, teamID, len(users)); err != nil {
		return err
	}

	return s.userRepo.Update(ctx, users, teamID)
}

// checkSubscriptionMemberLimit 检查订阅成员数量限制
func (s *teamService) checkSubscriptionMemberLimit(ctx context.Context, teamID int32, additionalUsers int) error {
	// 查询当前激活的订阅
	subscription, err := s.subscriptionRepo.FindActiveByTeam(ctx, teamID)
	if err != nil {
		return fmt.Errorf("failed to find active subscription for team %d: %w", teamID, err)
	}

	// 获取团队当前用户数量
	currentUserCount, err := s.getCurrentTeamUserCount(ctx, teamID)
	if err != nil {
		return fmt.Errorf("failed to get current user count for team %d: %w", teamID, err)
	}

	// 确定成员数量限制
	var memberLimit int16
	if subscription == nil {
		// 没有活跃订阅时，限制为1个成员
		memberLimit = 5
	} else {
		// 有活跃订阅时，使用订阅的成员限制
		memberLimit = subscription.MembersCount
	}

	// 检查是否超出成员限制
	totalUsersAfterOperation := currentUserCount + additionalUsers
	if totalUsersAfterOperation > int(memberLimit) {
		return fmt.Errorf("超出套餐限制,当前用户数=%d, 添加用户数=%d, 套餐用户数=%d",
			currentUserCount, additionalUsers, memberLimit)
	}

	return nil
}

// getCurrentTeamUserCount 获取团队当前用户数量
func (s *teamService) getCurrentTeamUserCount(ctx context.Context, teamID int32) (int, error) {
	// 使用GetUsers方法获取用户总数，设置一个很大的limit来获取所有用户
	_, total, err := s.userRepo.GetUsers(ctx, "", teamID, 0, 10000, 0)
	if err != nil {
		return 0, err
	}

	return int(total), nil
}
