package user

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"fp-browser/internal/dao/model"
	"fp-browser/internal/repository"
)

type SubscriptionService interface {
	GetAll(ctx context.Context) ([]*model.Subscription, error)
	Purchase(ctx context.Context, userID, teamID, subscriptionID, quantity int32) (*model.Order, error)
}

type OrderContentItem struct {
	GoodsName string `json:"goods_name"`
	Quantity  int32  `json:"quantity"`
	Price     string `json:"price"` // 以分为单位的价格字符串
	GoodsID   string `json:"goods_id"`
}

type subscriptionService struct {
	subscriptionRepo repository.SubscriptionRepository
	orderRepo        repository.OrderRepository
}

func NewSubscriptionService(subscriptionRepo repository.SubscriptionRepository, orderRepo repository.OrderRepository) SubscriptionService {
	return &subscriptionService{
		subscriptionRepo: subscriptionRepo,
		orderRepo:        orderRepo,
	}
}

func (s *subscriptionService) GetAll(ctx context.Context) ([]*model.Subscription, error) {
	return s.subscriptionRepo.GetAll(ctx)
}

func (s *subscriptionService) Purchase(ctx context.Context, userID, teamID, subscriptionID, quantity int32) (*model.Order, error) {
	// 1. 获取订阅套餐信息
	subscription, err := s.subscriptionRepo.GetByID(ctx, subscriptionID)
	if err != nil {
		return nil, fmt.Errorf("failed to get subscription: %w", err)
	}

	// 2. 计算订单金额（转换为分）
	unitPriceInCents := int64(subscription.Price * 100) // 将价格转换为分
	totalAmount := unitPriceInCents * int64(quantity)

	// 3. 生成订单号
	orderNumber := generateOrderNumber()

	// 4. 构建订单内容
	orderContent := []OrderContentItem{
		{
			GoodsName: extractSubscriptionName(subscription.Name), // 从JSONB中提取名称
			Quantity:  quantity,
			Price:     fmt.Sprintf("%d", unitPriceInCents),
			GoodsID:   fmt.Sprintf("%d", subscriptionID),
		},
	}

	orderContentJSON, err := json.Marshal(orderContent)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal order content: %w", err)
	}
	orderContentStr := string(orderContentJSON)

	// 5. 设置订单过期时间（30分钟后）
	expiresAt := time.Now().Add(30 * time.Minute)

	// 6. 创建订单
	order := &model.Order{
		OrderNumber:   orderNumber,
		UserID:        userID,
		TeamID:        teamID,
		Amount:        totalAmount,
		RealAmount:    totalAmount, // 初始时实际支付金额等于应支付金额
		Currency:      subscription.Currency,
		Status:        1, // 1待支付
		OrderType:     1, // 1订阅
		PaymentMethod: 0, // 0表示未选择支付方式，后续会更新
		OrderContent:  &orderContentStr,
		ExpiresAt:     &expiresAt,
	}

	// 7. 保存订单到数据库
	createdOrder, err := s.orderRepo.Create(ctx, order)
	if err != nil {
		return nil, fmt.Errorf("failed to create order: %w", err)
	}

	return createdOrder, nil
}

// generateOrderNumber 生成唯一订单号
func generateOrderNumber() string {
	return fmt.Sprintf("ORD%d", time.Now().UnixNano()/1000000) // 使用时间戳生成订单号
}

// extractSubscriptionName 从JSONB格式的名称中提取默认名称
func extractSubscriptionName(nameJSON string) string {
	var nameMap map[string]string
	if err := json.Unmarshal([]byte(nameJSON), &nameMap); err != nil {
		return "Subscription" // 默认名称
	}

	// 优先返回中文名称，然后英文，最后默认
	if name, ok := nameMap["zh"]; ok && name != "" {
		return name
	}
	if name, ok := nameMap["en"]; ok && name != "" {
		return name
	}
	for _, name := range nameMap {
		if name != "" {
			return name
		}
	}
	return "Subscription"
}
