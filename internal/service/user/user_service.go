package user

import (
	"context"
	"fmt"

	"fp-browser/internal/dao/model"
	"fp-browser/internal/repository"
	"fp-browser/internal/view_model"
	"golang.org/x/crypto/bcrypt"
)

type UserService interface {
	Authenticate(ctx context.Context, identifier, password string) (*view_model.User, error)
	Register(ctx context.Context, user *model.User) (*model.User, error)
	UpdateProfile(ctx context.Context, users []*model.User, teamID int32) error
	GetUser(ctx context.Context, id int32) (*view_model.User, error)
	GetUserByInviteCode(ctx context.Context, inviteCode string) (*model.User, error)
}

type userService struct {
	userRepo repository.UserRepository
}

func NewUserService(userRepo repository.UserRepository) UserService {
	return &userService{userRepo: userRepo}
}

// Authenticate 登录认证
func (s *userService) Authenticate(ctx context.Context, identifier, password string) (*view_model.User, error) {
	userVM, err := s.userRepo.FindByIdentifier(ctx, identifier)
	if err != nil {
		return nil, fmt.Errorf("user not found: %w", err)
	}

	// 比较密码
	if err := bcrypt.CompareHashAndPassword([]byte(userVM.Password), []byte(password)); err != nil {
		return nil, fmt.Errorf("invalid password: %w", err)
	}

	return userVM, nil
}

// Register 注册新用户
func (s *userService) Register(ctx context.Context, user *model.User) (*model.User, error) {
	return s.userRepo.Create(ctx, user)
}

// UpdateProfile 更新用户资料
func (s *userService) UpdateProfile(ctx context.Context, users []*model.User, teamID int32) error {
	return s.userRepo.Update(ctx, users, teamID)
}

// GetUser 根据 ID 查单个用户
func (s *userService) GetUser(ctx context.Context, id int32) (*view_model.User, error) {
	return s.userRepo.FindByID(ctx, id)
}

// GetUserByInviteCode 根据邀请码查询用户
func (s *userService) GetUserByInviteCode(ctx context.Context, inviteCode string) (*model.User, error) {
	return s.userRepo.GetUserByInviteCode(ctx, inviteCode)
}
