package user

import (
	"context"
	"fmt"

	v1 "fp-browser/api/v1/user"
	"fp-browser/internal/dao/model"
	"fp-browser/internal/repository"
)

type SelfHostProxyService interface {
	// 核心业务方法 - 直接接受请求参数
	CreateProxy(ctx context.Context, request v1.CreateSelfHostProxyRequest, teamID int32) error
	CreateProxies(ctx context.Context, request v1.CreateSelfHostProxiesRequest, teamID int32) error
	UpdateProxy(ctx context.Context, request v1.UpdateSelfHostProxyRequest, teamID int32) error
	UpdateProxies(ctx context.Context, request v1.UpdateSelfHostProxiesRequest, teamID int32) error
	DeleteProxies(ctx context.Context, request v1.DeleteSelfHostProxyRequest, teamID int32) error
	GetProxyByID(ctx context.Context, id, teamID int32) (*model.SelfHostProxy, error)
	GetProxies(ctx context.Context, request v1.GetSelfHostProxiesRequest, teamID int32) ([]*v1.SelfHostProxyItem, int64, error)
}

type selfHostProxyService struct {
	selfHostProxyRepo repository.SelfHostProxyRepository
}

func NewSelfHostProxyService(repo repository.SelfHostProxyRepository) SelfHostProxyService {
	return &selfHostProxyService{
		selfHostProxyRepo: repo,
	}
}

// CreateProxy 创建单个代理
func (s *selfHostProxyService) CreateProxy(ctx context.Context, request v1.CreateSelfHostProxyRequest, teamID int32) error {
	// 业务验证
	if err := s.validateProxyRequest(request.Name, request.Host, request.Port); err != nil {
		return err
	}

	// 转换请求数据为模型
	proxy := s.requestToModel(request.Name, request.Host, request.Username, request.Password,
		request.Type, request.Port, teamID, request.EnvironmentID)

	// 调用repository创建
	return s.selfHostProxyRepo.Create(ctx, []*model.SelfHostProxy{proxy})
}

// CreateProxies 批量创建代理
func (s *selfHostProxyService) CreateProxies(ctx context.Context, request v1.CreateSelfHostProxiesRequest, teamID int32) error {
	if len(request.Proxies) == 0 {
		return fmt.Errorf("代理列表不能为空")
	}

	proxies := make([]*model.SelfHostProxy, 0, len(request.Proxies))
	for i, proxyReq := range request.Proxies {
		// 业务验证
		if err := s.validateProxyRequest(proxyReq.Name, proxyReq.Host, proxyReq.Port); err != nil {
			return fmt.Errorf("第%d个代理数据无效: %v", i+1, err)
		}

		proxy := s.requestToModel(proxyReq.Name, proxyReq.Host, proxyReq.Username, proxyReq.Password,
			proxyReq.Type, proxyReq.Port, teamID, proxyReq.EnvironmentID)
		proxies = append(proxies, proxy)
	}

	return s.selfHostProxyRepo.Create(ctx, proxies)
}

// UpdateProxy 更新单个代理
func (s *selfHostProxyService) UpdateProxy(ctx context.Context, request v1.UpdateSelfHostProxyRequest, teamID int32) error {
	// 业务验证
	if request.ID <= 0 {
		return fmt.Errorf("代理ID无效")
	}
	if err := s.validateProxyRequest(request.Name, request.Host, request.Port); err != nil {
		return err
	}

	// 转换请求数据为模型
	proxy := s.requestToModel(request.Name, request.Host, request.Username, request.Password,
		request.Type, request.Port, teamID, request.EnvironmentID)
	proxy.ID = request.ID

	return s.selfHostProxyRepo.Update(ctx, []*model.SelfHostProxy{proxy})
}

// UpdateProxies 批量更新代理
func (s *selfHostProxyService) UpdateProxies(ctx context.Context, request v1.UpdateSelfHostProxiesRequest, teamID int32) error {
	if len(request.Proxies) == 0 {
		return fmt.Errorf("代理列表不能为空")
	}

	proxies := make([]*model.SelfHostProxy, 0, len(request.Proxies))
	for i, proxyReq := range request.Proxies {
		// 业务验证
		if proxyReq.ID <= 0 {
			return fmt.Errorf("第%d个代理ID无效", i+1)
		}
		if err := s.validateProxyRequest(proxyReq.Name, proxyReq.Host, proxyReq.Port); err != nil {
			return fmt.Errorf("第%d个代理数据无效: %v", i+1, err)
		}

		proxy := s.requestToModel(proxyReq.Name, proxyReq.Host, proxyReq.Username, proxyReq.Password,
			proxyReq.Type, proxyReq.Port, teamID, proxyReq.EnvironmentID)
		proxy.ID = proxyReq.ID
		proxies = append(proxies, proxy)
	}

	return s.selfHostProxyRepo.Update(ctx, proxies)
}

func (s *selfHostProxyService) DeleteProxies(ctx context.Context, request v1.DeleteSelfHostProxyRequest, teamID int32) error {
	// 业务验证
	if len(request.IDs) == 0 {
		return fmt.Errorf("代理ID列表不能为空")
	}

	// 验证每个ID的有效性
	for i, id := range request.IDs {
		if id <= 0 {
			return fmt.Errorf("第%d个代理ID无效: %d", i+1, id)
		}
	}

	return s.selfHostProxyRepo.Delete(ctx, request.IDs, teamID)
}

func (s *selfHostProxyService) GetProxyByID(ctx context.Context, id, teamID int32) (*model.SelfHostProxy, error) {
	// 业务验证
	if id <= 0 {
		return nil, fmt.Errorf("代理ID无效: %d", id)
	}
	if teamID < 0 {
		return nil, fmt.Errorf("团队ID无效: %d", teamID)
	}

	// 添加调试日志
	fmt.Printf("Service层开始查询代理: proxyID=%d, teamID=%d\n", id, teamID)

	proxy, err := s.selfHostProxyRepo.GetByID(ctx, id, teamID)
	if err != nil {
		fmt.Printf("Service层查询代理失败: proxyID=%d, teamID=%d, error=%v\n", id, teamID, err)
		return nil, err
	}

	if proxy == nil {
		fmt.Printf("Service层查询代理返回nil: proxyID=%d, teamID=%d\n", id, teamID)
		return nil, fmt.Errorf("代理不存在")
	}

	fmt.Printf("Service层查询代理成功: proxyID=%d, teamID=%d, proxy=%+v\n", id, teamID, proxy)
	return proxy, nil
}

func (s *selfHostProxyService) GetProxies(ctx context.Context, request v1.GetSelfHostProxiesRequest, teamID int32) ([]*v1.SelfHostProxyItem, int64, error) {
	// 业务验证（Handler层已经做了基础参数验证）
	if request.ID < 0 {
		return nil, 0, fmt.Errorf("代理ID筛选参数无效: %d", request.ID)
	}
	if request.EnvironmentID < -1 {
		return nil, 0, fmt.Errorf("环境ID筛选参数无效: %d", request.EnvironmentID)
	}

	// Handler层已经验证过limit和offset，这里直接使用
	return s.selfHostProxyRepo.GetSelfHostProxies(ctx, request.ID, teamID, request.EnvironmentID, request.ProxyType, request.Name, request.Limit, request.Offset)
}

// requestToModel 将请求数据转换为模型
func (s *selfHostProxyService) requestToModel(name, host, username, password string,
	proxyType int16, port int16, teamID int32, environmentID int32) *model.SelfHostProxy {
	proxy := &model.SelfHostProxy{
		Name:          &name,
		Type:          proxyType,
		Host:          &host,
		Port:          port,
		TeamID:        teamID,
		EnvironmentID: environmentID,
	}

	// 处理可选字段
	if username != "" {
		proxy.Username = &username
	}
	if password != "" {
		proxy.Password = &password
	}

	return proxy
}

// validateProxyRequest 验证代理请求数据
func (s *selfHostProxyService) validateProxyRequest(name, host string, port int16) error {
	if name == "" {
		return fmt.Errorf("代理名称不能为空")
	}
	if host == "" {
		return fmt.Errorf("代理主机地址不能为空")
	}
	if port <= 0 || int(port) > 65535 {
		return fmt.Errorf("端口号必须在1-65535之间")
	}
	return nil
}
