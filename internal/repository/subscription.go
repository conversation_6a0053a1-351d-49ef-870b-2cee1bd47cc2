package repository

import (
	"context"

	"fp-browser/internal/dao/model"
)

type SubscriptionRepository interface {
	Create(ctx context.Context, subscription *model.Subscription) error
	Update(ctx context.Context, subscription *model.Subscription) error
	Delete(ctx context.Context, id int32) error
	GetByID(ctx context.Context, id int32) (*model.Subscription, error)
	GetAll(ctx context.Context) ([]*model.Subscription, error)
}

type subscriptionRepository struct {
	*Repository
}

func NewSubscriptionRepository(r *Repository) SubscriptionRepository {
	return &subscriptionRepository{Repository: r}
}

func (r *subscriptionRepository) Create(ctx context.Context, subscription *model.Subscription) error {
	err := r.query.Subscription.WithContext(ctx).Create(subscription)
	if err != nil {
		r.logger.Error("创建订阅套餐失败",
			"name", subscription.Name,
			"storagesize", subscription.Storagesize,
			"membercount", subscription.Membercount,
			"price", subscription.Price,
			"currency", subscription.Currency,
			"error", err)
		return err
	}

	r.logger.Info("订阅套餐创建成功",
		"id", subscription.ID,
		"name", subscription.Name,
		"storagesize", subscription.Storagesize,
		"membercount", subscription.Membercount,
		"price", subscription.Price,
		"currency", subscription.Currency)
	return nil
}

func (r *subscriptionRepository) Update(ctx context.Context, subscription *model.Subscription) error {
	q := r.query.Subscription.WithContext(ctx)

	_, err := q.Where(r.query.Subscription.ID.Eq(subscription.ID)).Updates(subscription)
	if err != nil {
		r.logger.Error("更新订阅套餐失败",
			"id", subscription.ID,
			"name", subscription.Name,
			"storagesize", subscription.Storagesize,
			"membercount", subscription.Membercount,
			"price", subscription.Price,
			"currency", subscription.Currency,
			"error", err)
		return err
	}

	r.logger.Info("订阅套餐更新成功",
		"id", subscription.ID,
		"name", subscription.Name,
		"storagesize", subscription.Storagesize,
		"membercount", subscription.Membercount,
		"price", subscription.Price,
		"currency", subscription.Currency)
	return nil
}

func (r *subscriptionRepository) Delete(ctx context.Context, id int32) error {
	q := r.query.Subscription.WithContext(ctx)

	_, err := q.Where(r.query.Subscription.ID.Eq(id)).Delete()
	if err != nil {
		r.logger.Error("删除订阅套餐失败",
			"id", id,
			"error", err)
		return err
	}

	r.logger.Info("订阅套餐删除成功", "id", id)
	return nil
}

func (r *subscriptionRepository) GetByID(ctx context.Context, id int32) (*model.Subscription, error) {
	q := r.query.Subscription.WithContext(ctx)

	subscription, err := q.Where(r.query.Subscription.ID.Eq(id)).First()
	if err != nil {
		r.logger.Error("根据ID查询订阅套餐失败",
			"id", id,
			"error", err)
		return nil, err
	}

	r.logger.Debug("根据ID查询订阅套餐成功",
		"id", id,
		"name", subscription.Name,
		"storagesize", subscription.Storagesize,
		"membercount", subscription.Membercount,
		"price", subscription.Price,
		"currency", subscription.Currency)
	return subscription, nil
}

func (r *subscriptionRepository) GetAll(ctx context.Context) ([]*model.Subscription, error) {
	q := r.query.Subscription
	db := q.WithContext(ctx)

	results, err := db.Order(q.ID.Desc()).Find()
	if err != nil {
		r.logger.Error("查询所有订阅套餐失败", "error", err)
		return nil, err
	}

	r.logger.Debug("查询所有订阅套餐成功", "count", len(results))

	// Debug级别记录每个套餐的简要信息
	for _, s := range results {
		r.logger.Debug("订阅套餐详情",
			"id", s.ID,
			"name", s.Name,
			"storagesize", s.Storagesize,
			"membercount", s.Membercount,
			"price", s.Price,
			"currency", s.Currency)
	}

	return results, nil
}
