package repository

import (
	"context"
	"fp-browser/internal/dao/model"
)

type TeamIPWhitelistRepository interface {
	Create(ctx context.Context, whitelists []*model.TeamIPWhitelist) error
	GetByTeamID(ctx context.Context, teamID int32) ([]*model.TeamIPWhitelist, error)
	Delete(ctx context.Context, ids []int32, teamID int32) error
	CheckIPExists(ctx context.Context, teamID int32, ipAddress string) (bool, error)
}

type teamIPWhitelistRepository struct {
	*Repository
}

func NewTeamIPWhitelistRepository(r *Repository) TeamIPWhitelistRepository {
	return &teamIPWhitelistRepository{Repository: r}
}

func (r *teamIPWhitelistRepository) Create(ctx context.Context, whitelists []*model.TeamIPWhitelist) error {
	err := r.query.TeamIPWhitelist.WithContext(ctx).Create(whitelists...)
	if err != nil {
		r.logger.Error("批量创建团队IP白名单失败",
			"count", len(whitelists),
			"error", err)
		return err
	}

	r.logger.Info("批量创建团队IP白名单成功",
		"count", len(whitelists))

	// Debug级别记录每个白名单的详情
	for _, w := range whitelists {
		r.logger.Debug("团队IP白名单创建详情",
			"id", w.ID,
			"teamID", w.TeamID,
			"ipAddress", w.IPAddress)
	}

	return nil
}

func (r *teamIPWhitelistRepository) GetByTeamID(ctx context.Context, teamID int32) ([]*model.TeamIPWhitelist, error) {
	q := r.query.TeamIPWhitelist.WithContext(ctx)

	tx := q.Where(r.query.TeamIPWhitelist.TeamID.Eq(teamID))
	conditions := []string{"teamID"}

	whitelists, err := tx.Order(r.query.TeamIPWhitelist.CreatedAt.Desc()).Find()
	if err != nil {
		r.logger.Error("查询团队IP白名单失败",
			"teamID", teamID,
			"conditions", conditions,
			"error", err)
		return nil, err
	}

	r.logger.Debug("查询团队IP白名单成功",
		"teamID", teamID,
		"conditions", conditions,
		"count", len(whitelists))
	return whitelists, nil
}

func (r *teamIPWhitelistRepository) Delete(ctx context.Context, ids []int32, teamID int32) error {
	q := r.query.TeamIPWhitelist.WithContext(ctx)

	tx := q.Where(r.query.TeamIPWhitelist.ID.In(ids...))
	conditions := []string{"ids"}

	if teamID != 0 {
		tx = tx.Where(r.query.TeamIPWhitelist.TeamID.Eq(teamID))
		conditions = append(conditions, "teamID")
	}

	result, err := tx.Delete()
	if err != nil {
		r.logger.Error("批量删除团队IP白名单失败",
			"ids", ids,
			"teamID", teamID,
			"conditions", conditions,
			"count", len(ids),
			"error", err)
		return err
	}

	affectedRows := result.RowsAffected
	r.logger.Info("批量删除团队IP白名单成功",
		"ids", ids,
		"teamID", teamID,
		"conditions", conditions,
		"count", len(ids),
		"affectedRows", affectedRows)
	return nil
}

func (r *teamIPWhitelistRepository) CheckIPExists(ctx context.Context, teamID int32, ipAddress string) (bool, error) {
	q := r.query.TeamIPWhitelist.WithContext(ctx)

	tx := q.Where(r.query.TeamIPWhitelist.TeamID.Eq(teamID), r.query.TeamIPWhitelist.IPAddress.Eq(ipAddress))
	conditions := []string{"teamID", "ipAddress"}

	count, err := tx.Count()
	if err != nil {
		r.logger.Error("检查IP是否存在失败",
			"teamID", teamID,
			"ipAddress", ipAddress,
			"conditions", conditions,
			"error", err)
		return false, err
	}

	exists := count > 0
	r.logger.Debug("检查IP是否存在",
		"teamID", teamID,
		"ipAddress", ipAddress,
		"conditions", conditions,
		"exists", exists,
		"count", count)
	return exists, nil
}
