// internal/repository/environment_repository.go
package repository

import (
	"context"
	"database/sql"
	"fmt"
	"fp-browser/internal/dao/model"
	"fp-browser/internal/view_model"
)

type EnvironmentRepository interface {
	CreateEnvironment(ctx context.Context, env *model.Environment) error
	GetEnvironments(ctx context.Context, teamID int32, name string, userID, groupID int32, limit, offset int) ([]*view_model.EnvironmentListItem, int64, error)
	GetDeletedEnvironments(ctx context.Context, teamID int32, limit, offset int) ([]*view_model.EnvironmentListItem, int64, error)
	GetEnvironmentByID(ctx context.Context, id, teamID int32) (*view_model.EnvironmentWithProxy, error)
	UpdateEnvironment(ctx context.Context, envs []*model.Environment, teamID int32) error
	UpdateEnvironmentParameters(ctx context.Context, ids []int32, parameters string, teamID int32) error
	UpdateEnvironmentProxyID(ctx context.Context, ids []int32, proxyID int32, teamID int32) error
	UpdateEnvironmentGroupID(ctx context.Context, ids []int32, groupID int32, teamID int32) error
	DeleteEnvironment(ctx context.Context, ids []int32, teamID int32) error
	PermanentDeleteEnvironment(ctx context.Context, ids []int32, teamID int32) error
	RestoreEnvironment(ctx context.Context, ids []int32, teamID int32) error
	GetEnvironmentStorageInfo(ctx context.Context, id, teamID int32) (*model.Environment, error)
	ValidateEnvironmentAccess(ctx context.Context, id, teamID int32) (bool, error)
	UpdateEnvironmentStorageByID(ctx context.Context, id int32, storage string, size int32) error
	GetEnvironmentsSizeSum(ctx context.Context, teamID int32) (int64, error)
}

type environmentRepository struct {
	*Repository
}

func NewEnvironmentRepository(r *Repository) EnvironmentRepository {
	return &environmentRepository{Repository: r}
}

func (r *environmentRepository) CreateEnvironment(ctx context.Context, env *model.Environment) error {
	err := r.query.Environment.WithContext(ctx).Create(env)
	if err != nil {
		r.logger.Error("创建环境失败",
			"teamID", env.TeamID,
			"name", env.Name,
			"userID", env.UserID,
			"groupID", env.GroupID,
			"error", err)
		return err
	}

	r.logger.Info("环境创建成功",
		"id", env.ID,
		"teamID", env.TeamID,
		"name", env.Name,
		"userID", env.UserID,
		"groupID", env.GroupID)
	return nil
}

func (r *environmentRepository) GetEnvironments(ctx context.Context, teamID int32, name string, userID, groupID int32, limit, offset int) ([]*view_model.EnvironmentListItem, int64, error) {
	e := r.query.Environment
	g := r.query.Group
	shp := r.query.SelfHostProxy
	p := r.query.Proxy
	f := r.query.Forward

	// 查询条件
	stmt := e.WithContext(ctx).Where(e.TeamID.Eq(teamID)).Where(e.DeletedAt.IsNull())

	if name != "" {
		stmt = stmt.Where(e.Name.Like("%" + name + "%"))
	}
	if userID != 0 {
		stmt = stmt.Where(e.UserID.Eq(userID))
	}
	if groupID != 0 {
		stmt = stmt.Where(e.GroupID.Eq(groupID))
	}

	// 查总数
	var total int64
	if err := stmt.Select(e.ID.Count()).Scan(&total); err != nil {
		r.logger.Error("查询环境总数失败",
			"teamID", teamID,
			"name", name,
			"userID", userID,
			"groupID", groupID,
			"error", err)
		return nil, 0, err
	}

	if limit > 500 {
		r.logger.Warn("查询限制已调整为最大值", "requestedLimit", limit, "adjustedLimit", 500)
		limit = 500
	}

	// 查询 Environment + Group Name
	type environmentRaw struct {
		model.Environment
		GroupName string `gorm:"column:group_name"`
	}

	var envs []*environmentRaw

	err := stmt.
		Select(e.ALL, g.Name.As("group_name")).
		LeftJoin(g, g.ID.EqCol(e.GroupID)).
		Order(e.UpdatedAt.Desc()).
		Limit(limit).
		Offset(offset).
		Scan(&envs)

	if err != nil {
		r.logger.Error("查询环境列表失败",
			"teamID", teamID,
			"name", name,
			"userID", userID,
			"groupID", groupID,
			"limit", limit,
			"offset", offset,
			"error", err)
		return nil, 0, err
	}

	// 遍历构建 ViewModel
	var results []*view_model.EnvironmentListItem

	for _, env := range envs {
		proxyInfo := view_model.ProxyInfo{}

		if env.ProxyType == 0 || env.ProxyID == 0 {
			// proxy 信息为空，跳过查询
		} else if env.ProxyType == 2 {
			// 自有代理
			proxy, err := shp.WithContext(ctx).
				Where(shp.ID.Eq(env.ProxyID)).
				First()
			if err != nil {
				r.logger.Warn("查询自有代理失败，使用空代理信息",
					"envID", env.ID,
					"proxyID", env.ProxyID,
					"error", err)
				// 不中断查询，使用空的代理信息
				proxyInfo = view_model.ProxyInfo{
					Name:     "代理不存在",
					Type:     env.ProxyType,
					Address:  "",
					Port:     0,
					Username: "",
					Password: "",
				}
			} else {
				proxyInfo = view_model.ProxyInfo{
					Name:     derefString(proxy.Name),
					Type:     proxy.Type,
					Address:  derefString(proxy.Host),
					Port:     proxy.Port,
					Username: derefString(proxy.Username),
					Password: derefString(proxy.Password),
				}
			}
		} else {
			//平台代理
			proxy, err := p.WithContext(ctx).
				Where(p.ID.Eq(env.ProxyID)).
				First()
			if err != nil {
				r.logger.Warn("查询平台代理失败，使用空代理信息",
					"envID", env.ID,
					"proxyID", env.ProxyID,
					"error", err)
				// 不中断查询，使用空的代理信息
				proxyInfo = view_model.ProxyInfo{
					Name:     "代理不存在",
					Type:     env.ProxyType,
					Address:  "",
					Port:     0,
					Username: "",
					Password: "",
				}
			} else {
				// 直接默认 forwarding 都启用
				forward, err := f.WithContext(ctx).
					Where(f.ID.Eq(proxy.ForwardID)).
					First()
				if err != nil {
					r.logger.Warn("查询代理转发信息失败，使用空转发信息",
						"envID", env.ID,
						"proxyID", env.ProxyID,
						"forwardID", proxy.ForwardID,
						"error", err)
					// 不中断查询，使用代理信息但无转发信息
					proxyInfo = view_model.ProxyInfo{
						Name:     derefString(proxy.Name),
						Type:     3,
						Address:  "",
						Port:     0,
						Username: proxy.TuicUsername,
						Password: proxy.TuicUsername,
					}
				} else {
					proxyInfo = view_model.ProxyInfo{
						Name:     derefString(proxy.Name),
						Type:     3,
						Address:  forward.Domain,
						Port:     forward.Port,
						Username: proxy.TuicUsername,
						Password: proxy.TuicUsername,
					}
				}
			}
		}

		result := &view_model.EnvironmentListItem{
			ID:        env.ID,
			TeamID:    env.TeamID,
			Name:      env.Name,
			UserID:    env.UserID,
			GroupID:   env.GroupID,
			GroupName: env.GroupName,
			Proxy:     proxyInfo,
			ProxyType: env.ProxyType,
			Platform:  env.Platform,
			Storage:   env.Storage,
			Tag:       env.Tag,
			Sort:      env.Sort,
			Size:      env.Size,
			Comment:   env.Comment,
			CreatedAt: env.CreatedAt,
			UpdatedAt: env.UpdatedAt,
		}

		results = append(results, result)
	}

	r.logger.Debug("查询环境列表成功",
		"teamID", teamID,
		"total", total,
		"count", len(results),
		"name", name,
		"userID", userID,
		"groupID", groupID,
		"limit", limit,
		"offset", offset)

	return results, total, nil
}

func (r *environmentRepository) GetDeletedEnvironments(ctx context.Context, teamID int32, limit, offset int) ([]*view_model.EnvironmentListItem, int64, error) {
	e := r.query.Environment
	g := r.query.Group
	shp := r.query.SelfHostProxy
	p := r.query.Proxy
	f := r.query.Forward

	// 查询条件 - 使用Unscoped查找软删除的环境
	stmt := e.WithContext(ctx).Unscoped().Where(e.TeamID.Eq(teamID)).Where(e.DeletedAt.IsNotNull())

	// 查总数
	var total int64
	if err := stmt.Select(e.ID.Count()).Scan(&total); err != nil {
		r.logger.Error("查询已删除环境总数失败",
			"teamID", teamID,
			"error", err)
		return nil, 0, err
	}

	if limit > 500 {
		r.logger.Warn("查询限制已调整为最大值", "requestedLimit", limit, "adjustedLimit", 500)
		limit = 500
	}

	// 查询 Environment + Group Name
	type environmentRaw struct {
		model.Environment
		GroupName string `gorm:"column:group_name"`
	}

	var envs []*environmentRaw

	err := stmt.
		Select(e.ALL, g.Name.As("group_name")).
		LeftJoin(g, g.ID.EqCol(e.GroupID)).
		Order(e.ID.Asc()).
		Limit(limit).
		Offset(offset).
		Scan(&envs)

	if err != nil {
		r.logger.Error("查询已删除环境列表失败",
			"teamID", teamID,
			"limit", limit,
			"offset", offset,
			"error", err)
		return nil, 0, err
	}

	// 遍历构建 ViewModel
	var results []*view_model.EnvironmentListItem

	for _, env := range envs {
		proxyInfo := view_model.ProxyInfo{}

		if env.ProxyType == 0 || env.ProxyID == 0 {
			// proxy 信息为空，跳过查询
		} else if env.ProxyType == 2 {
			// 自有代理
			proxy, err := shp.WithContext(ctx).
				Where(shp.ID.Eq(env.ProxyID)).
				First()
			if err != nil {
				r.logger.Warn("查询已删除环境的自有代理失败，使用空代理信息",
					"envID", env.ID,
					"proxyID", env.ProxyID,
					"error", err)
				// 不中断查询，使用空的代理信息
				proxyInfo = view_model.ProxyInfo{
					Name:     "代理不存在",
					Type:     env.ProxyType,
					Address:  "",
					Port:     0,
					Username: "",
					Password: "",
				}
			} else {
				proxyInfo = view_model.ProxyInfo{
					Name:     derefString(proxy.Name),
					Type:     proxy.Type,
					Address:  derefString(proxy.Host),
					Port:     proxy.Port,
					Username: derefString(proxy.Username),
					Password: derefString(proxy.Password),
				}
			}
		} else {
			//平台代理
			proxy, err := p.WithContext(ctx).
				Where(p.ID.Eq(env.ProxyID)).
				First()
			if err != nil {
				r.logger.Warn("查询已删除环境的平台代理失败，使用空代理信息",
					"envID", env.ID,
					"proxyID", env.ProxyID,
					"error", err)
				// 不中断查询，使用空的代理信息
				proxyInfo = view_model.ProxyInfo{
					Name:     "代理不存在",
					Type:     env.ProxyType,
					Address:  "",
					Port:     0,
					Username: "",
					Password: "",
				}
			} else {
				// 直接默认 forwarding 都启用
				forward, err := f.WithContext(ctx).
					Where(f.ID.Eq(proxy.ForwardID)).
					First()
				if err != nil {
					r.logger.Warn("查询已删除环境的代理转发信息失败，使用空转发信息",
						"envID", env.ID,
						"proxyID", env.ProxyID,
						"forwardID", proxy.ForwardID,
						"error", err)
					// 不中断查询，使用代理信息但无转发信息
					proxyInfo = view_model.ProxyInfo{
						Name:     derefString(proxy.Name),
						Type:     3,
						Address:  "",
						Port:     0,
						Username: proxy.TuicUsername,
						Password: proxy.TuicUsername,
					}
				} else {
					proxyInfo = view_model.ProxyInfo{
						Name:     derefString(proxy.Name),
						Type:     3,
						Address:  forward.Domain,
						Port:     forward.Port,
						Username: proxy.TuicUsername,
						Password: proxy.TuicUsername,
					}
				}
			}
		}

		result := &view_model.EnvironmentListItem{
			ID:        env.ID,
			TeamID:    env.TeamID,
			Name:      env.Name,
			UserID:    env.UserID,
			GroupID:   env.GroupID,
			GroupName: env.GroupName,
			Proxy:     proxyInfo,
			ProxyType: env.ProxyType,
			Platform:  env.Platform,
			Storage:   env.Storage,
			Tag:       env.Tag,
			Sort:      env.Sort,
			Size:      env.Size,
			CreatedAt: env.CreatedAt,
			UpdatedAt: env.UpdatedAt,
		}

		results = append(results, result)
	}

	r.logger.Debug("查询已删除环境列表成功",
		"teamID", teamID,
		"total", total,
		"count", len(results),
		"limit", limit,
		"offset", offset)

	return results, total, nil
}

func (r *environmentRepository) GetEnvironmentByID(ctx context.Context, id, teamID int32) (*view_model.EnvironmentWithProxy, error) {
	e := r.query.Environment
	g := r.query.Group
	shp := r.query.SelfHostProxy
	p := r.query.Proxy
	f := r.query.Forward

	// 查询 Environment + Group Name
	type environmentRaw struct {
		model.Environment
		GroupName string `gorm:"column:group_name"`
	}

	envQuery := e.WithContext(ctx).
		Select(e.ALL, g.Name.As("group_name")).
		LeftJoin(g, g.ID.EqCol(e.GroupID)).
		Where(e.ID.Eq(id), e.TeamID.Eq(teamID))

	var env environmentRaw
	if err := envQuery.Scan(&env); err != nil {
		r.logger.Error("查询环境详情失败",
			"id", id,
			"teamID", teamID,
			"error", err)
		return nil, err
	}

	proxyInfo := view_model.ProxyInfo{}

	if env.ProxyType == 0 || env.ProxyID == 0 {
		// proxy信息为空，什么都不查，直接返回默认 proxyInfo（全是0和空字符串）
		r.logger.Debug("环境无代理配置", "envID", id, "proxyType", env.ProxyType, "proxyID", env.ProxyID)
	} else if env.ProxyType == 2 {
		// 自有代理 SelfHostProxy
		proxy, err := shp.WithContext(ctx).
			Where(shp.ID.Eq(env.ProxyID)).
			First()
		if err != nil {
			r.logger.Error("查询环境自有代理失败",
				"envID", id,
				"proxyID", env.ProxyID,
				"error", err)
			return nil, err
		}

		proxyInfo = view_model.ProxyInfo{
			Name:     derefString(proxy.Name),
			Type:     proxy.Type,
			Address:  derefString(proxy.Host),
			Port:     proxy.Port,
			Username: derefString(proxy.Username),
			Password: derefString(proxy.Password),
		}

		r.logger.Debug("加载环境自有代理成功",
			"envID", id,
			"proxyID", env.ProxyID,
			"proxyName", proxyInfo.Name)
	} else {
		//平台代理 Proxy（直接默认走 Forward，前提是你的业务保证 forwarding 都开启）
		proxy, err := p.WithContext(ctx).
			Where(p.ID.Eq(env.ProxyID)).
			First()
		if err != nil {
			r.logger.Error("查询环境平台代理失败",
				"envID", id,
				"proxyID", env.ProxyID,
				"error", err)
			return nil, err
		}

		// 查 Forward
		forward, err := f.WithContext(ctx).
			Where(f.ID.Eq(proxy.ForwardID)).
			First()
		if err != nil {
			r.logger.Error("查询环境代理转发信息失败",
				"envID", id,
				"proxyID", env.ProxyID,
				"forwardID", proxy.ForwardID,
				"error", err)
			return nil, err
		}

		proxyInfo = view_model.ProxyInfo{
			Name:     derefString(proxy.Name),
			Type:     3, // 固定是 3
			Address:  forward.Domain,
			Port:     forward.Port,
			Username: proxy.TuicUsername,
			Password: proxy.TuicUsername,
		}

		r.logger.Debug("加载环境平台代理成功",
			"envID", id,
			"proxyID", env.ProxyID,
			"forwardID", proxy.ForwardID,
			"proxyName", proxyInfo.Name)
	}

	result := &view_model.EnvironmentWithProxy{
		ID:         env.ID,
		TeamID:     env.TeamID,
		Name:       env.Name,
		UserID:     env.UserID,
		GroupID:    env.GroupID,
		GroupName:  env.GroupName,
		Proxy:      proxyInfo,
		ProxyType:  env.ProxyType,
		Platform:   env.Platform,
		Parameters: env.Parameters,
		Storage:    env.Storage,
		Tag:        env.Tag,
		Comment:    env.Comment,
	}

	r.logger.Debug("查询环境详情成功",
		"id", id,
		"teamID", teamID,
		"name", env.Name,
		"proxyType", env.ProxyType)

	return result, nil
}

func (r *environmentRepository) UpdateEnvironment(ctx context.Context, envs []*model.Environment, teamID int32) error {
	// 简化版本：直接循环更新，不使用事务
	q := r.query.Environment

	var updatedCount int
	for _, env := range envs {
		_, err := q.WithContext(ctx).
			Where(q.ID.Eq(env.ID), q.TeamID.Eq(teamID)).
			Updates(env)
		if err != nil {
			r.logger.Error("批量更新环境失败",
				"envID", env.ID,
				"teamID", teamID,
				"updatedCount", updatedCount,
				"totalCount", len(envs),
				"error", err)
			return err
		}
		updatedCount++
	}

	r.logger.Info("批量更新环境成功",
		"teamID", teamID,
		"updatedCount", updatedCount,
		"totalCount", len(envs))
	return nil
}

func (r *environmentRepository) UpdateEnvironmentParameters(ctx context.Context, ids []int32, parameters string, teamID int32) error {
	q := r.query.Environment
	_, err := q.WithContext(ctx).
		Where(q.ID.In(ids...), q.TeamID.Eq(teamID)).
		Update(q.Parameters, parameters)

	if err != nil {
		r.logger.Error("批量更新环境参数失败",
			"ids", ids,
			"teamID", teamID,
			"parametersLength", len(parameters),
			"error", err)
		return err
	}

	r.logger.Info("批量更新环境参数成功",
		"ids", ids,
		"teamID", teamID,
		"count", len(ids))
	return nil
}

func (r *environmentRepository) UpdateEnvironmentProxyID(ctx context.Context, ids []int32, proxyID int32, teamID int32) error {
	q := r.query.Environment
	_, err := q.WithContext(ctx).
		Where(q.ID.In(ids...), q.TeamID.Eq(teamID)).
		Update(q.ProxyID, proxyID)

	if err != nil {
		r.logger.Error("批量更新环境代理失败",
			"ids", ids,
			"teamID", teamID,
			"proxyID", proxyID,
			"error", err)
		return err
	}

	r.logger.Info("批量更新环境代理成功",
		"ids", ids,
		"teamID", teamID,
		"proxyID", proxyID,
		"count", len(ids))
	return nil
}

func (r *environmentRepository) UpdateEnvironmentGroupID(ctx context.Context, ids []int32, groupID int32, teamID int32) error {
	q := r.query.Environment
	_, err := q.WithContext(ctx).
		Where(q.ID.In(ids...), q.TeamID.Eq(teamID)).
		Update(q.GroupID, groupID)

	if err != nil {
		r.logger.Error("批量更新环境分组失败",
			"ids", ids,
			"teamID", teamID,
			"groupID", groupID,
			"error", err)
		return err
	}

	r.logger.Info("批量更新环境分组成功",
		"ids", ids,
		"teamID", teamID,
		"groupID", groupID,
		"count", len(ids))
	return nil
}

// DeleteEnvironment 软删除环境（标记删除时间）
func (r *environmentRepository) DeleteEnvironment(ctx context.Context, ids []int32, teamID int32) error {
	q := r.query.Environment

	// 软删除：GORM会自动设置 deleted_at 为当前时间
	result, err := q.WithContext(ctx).
		Where(q.ID.In(ids...), q.TeamID.Eq(teamID)).
		Delete()

	if err != nil {
		r.logger.Error("批量软删除环境失败",
			"ids", ids,
			"teamID", teamID,
			"error", err)
		return err
	}

	affectedRows := result.RowsAffected
	r.logger.Info("批量软删除环境成功",
		"ids", ids,
		"teamID", teamID,
		"requestCount", len(ids),
		"affectedRows", affectedRows)
	return nil
}

// PermanentDeleteEnvironment 永久删除环境（物理删除）
func (r *environmentRepository) PermanentDeleteEnvironment(ctx context.Context, ids []int32, teamID int32) error {
	q := r.query.Environment

	// 物理删除：真正从数据库中删除记录
	result, err := q.WithContext(ctx).Unscoped().
		Where(q.ID.In(ids...), q.TeamID.Eq(teamID)).
		Delete()

	if err != nil {
		r.logger.Error("批量永久删除环境失败",
			"ids", ids,
			"teamID", teamID,
			"error", err)
		return err
	}

	affectedRows := result.RowsAffected
	r.logger.Info("批量永久删除环境成功",
		"ids", ids,
		"teamID", teamID,
		"requestCount", len(ids),
		"affectedRows", affectedRows)
	return nil
}

// RestoreEnvironment 恢复已删除的环境
func (r *environmentRepository) RestoreEnvironment(ctx context.Context, ids []int32, teamID int32) error {
	q := r.query.Environment

	// 恢复：将 deleted_at 设置为 NULL，需要使用Unscoped访问软删除的记录
	_, err := q.WithContext(ctx).Unscoped().
		Where(q.ID.In(ids...), q.TeamID.Eq(teamID), q.DeletedAt.IsNotNull()).
		Update(q.DeletedAt, nil)

	if err != nil {
		r.logger.Error("批量恢复环境失败",
			"ids", ids,
			"teamID", teamID,
			"error", err)
		return err
	}

	r.logger.Info("批量恢复环境成功",
		"ids", ids,
		"teamID", teamID,
		"count", len(ids))
	return nil
}

func (r *environmentRepository) GetEnvironmentStorageInfo(ctx context.Context, id, teamID int32) (*model.Environment, error) {
	e := r.query.Environment

	env, err := e.WithContext(ctx).
		Where(e.ID.Eq(id), e.TeamID.Eq(teamID)).
		First()

	if err != nil {
		r.logger.Error("查询环境存储信息失败",
			"id", id,
			"teamID", teamID,
			"error", err)
		return nil, err
	}

	r.logger.Debug("查询环境存储信息成功",
		"id", id,
		"teamID", teamID,
		"storage", env.Storage,
		"size", env.Size)
	return env, nil
}

func (r *environmentRepository) ValidateEnvironmentAccess(ctx context.Context, id, teamID int32) (bool, error) {
	e := r.query.Environment

	count, err := e.WithContext(ctx).
		Where(e.ID.Eq(id), e.TeamID.Eq(teamID)).
		Count()

	if err != nil {
		r.logger.Error("验证环境访问权限失败",
			"id", id,
			"teamID", teamID,
			"error", err)
		return false, err
	}

	hasAccess := count > 0
	r.logger.Debug("验证环境访问权限",
		"id", id,
		"teamID", teamID,
		"hasAccess", hasAccess,
		"count", count)

	return hasAccess, nil
}

func (r *environmentRepository) GetEnvironmentsSizeSum(ctx context.Context, teamID int32) (int64, error) {
	e := r.query.Environment

	// 构建查询语句
	stmt := e.WithContext(ctx)

	// 如果teamID不为0，则添加team过滤条件
	if teamID != 0 {
		stmt = stmt.Where(e.TeamID.Eq(teamID))
	}

	// 计算size总和，使用sql.NullInt64处理可能的NULL值
	var totalSize sql.NullInt64
	err := stmt.Select(e.Size.Sum()).Scan(&totalSize)
	if err != nil {
		r.logger.Error("计算环境存储总量失败",
			"teamID", teamID,
			"error", err)
		return 0, err
	}

	// 如果查询结果为NULL（没有记录），返回0
	var result int64 = 0
	if totalSize.Valid {
		result = totalSize.Int64
	}

	r.logger.Debug("计算环境存储总量成功",
		"teamID", teamID,
		"totalSize", result)
	return result, nil
}

func (r *environmentRepository) UpdateEnvironmentStorageByID(ctx context.Context, id int32, storage string, size int32) error {
	q := r.query.Environment

	info, err := q.WithContext(ctx).
		Where(q.ID.Eq(id)).
		Updates(map[string]interface{}{
			"storage": storage,
			"size":    size,
		})

	if err != nil {
		r.logger.Error("更新环境存储信息失败",
			"id", id,
			"storage", storage,
			"size", size,
			"error", err)
		return err
	}

	if info.RowsAffected == 0 {
		r.logger.Warn("环境不存在，无法更新存储信息",
			"id", id,
			"storage", storage,
			"size", size)
		return fmt.Errorf("环境ID %d 不存在", id)
	}

	r.logger.Info("更新环境存储信息成功",
		"id", id,
		"storage", storage,
		"size", size,
		"rowsAffected", info.RowsAffected)

	return nil
}

func derefString(s *string) string {
	if s != nil {
		return *s
	}
	return ""
}
