package repository

import (
	"context"
	"fp-browser/internal/dao/model"
)

type UserBalanceRepository interface {
	Insert(ctx context.Context, balance *model.UserBalance) error
	FindByUserID(ctx context.Context, userID int32) (*model.UserBalance, error)
	FindByUserIDs(ctx context.Context, userIDs []int32) ([]*model.UserBalance, error)
	Delete(ctx context.Context, userID int32) error
}

type userBalanceRepository struct {
	*Repository
}

func NewUserBalanceRepository(r *Repository) UserBalanceRepository {
	return &userBalanceRepository{Repository: r}
}

func (r *userBalanceRepository) Insert(ctx context.Context, balance *model.UserBalance) error {
	err := r.query.UserBalance.WithContext(ctx).Create(balance)
	if err != nil {
		r.logger.Error("插入用户余额记录失败",
			"userID", balance.UserID,
			"error", err)
		return err
	}

	r.logger.Info("用户余额记录插入成功",
		"userID", balance.UserID)
	return nil
}

func (r *userBalanceRepository) FindByUserID(ctx context.Context, userID int32) (*model.UserBalance, error) {
	balance, err := r.query.UserBalance.WithContext(ctx).
		Where(r.query.UserBalance.UserID.Eq(userID)).
		First()

	if err != nil {
		r.logger.Error("根据用户ID查询余额失败",
			"userID", userID,
			"error", err)
		return nil, err
	}

	r.logger.Debug("根据用户ID查询余额成功",
		"userID", userID,
		"hasPositiveBalance", balance.WalletAmount > 0,
		"isNegativeBalance", balance.WalletAmount < 0)
	return balance, nil
}

func (r *userBalanceRepository) FindByUserIDs(ctx context.Context, userIDs []int32) ([]*model.UserBalance, error) {
	if len(userIDs) == 0 {
		r.logger.Debug("批量查询用户余额：空用户ID列表，跳过操作")
		return []*model.UserBalance{}, nil
	}

	balances, err := r.query.UserBalance.WithContext(ctx).
		Where(r.query.UserBalance.UserID.In(userIDs...)).
		Find()

	if err != nil {
		r.logger.Error("批量查询用户余额失败",
			"userIDs", userIDs,
			"userCount", len(userIDs),
			"error", err)
		return nil, err
	}

	r.logger.Debug("批量查询用户余额成功",
		"requestedUserCount", len(userIDs),
		"foundBalanceCount", len(balances))

	// Debug级别记录每个用户的余额状态（不记录具体金额）
	for _, balance := range balances {
		r.logger.Debug("用户余额状态",
			"userID", balance.UserID,
			"hasPositiveBalance", balance.WalletAmount > 0,
			"isNegativeBalance", balance.WalletAmount < 0)
	}

	return balances, nil
}

func (r *userBalanceRepository) Delete(ctx context.Context, userID int32) error {
	_, err := r.query.UserBalance.WithContext(ctx).
		Where(r.query.UserBalance.UserID.Eq(userID)).
		Delete()

	if err != nil {
		r.logger.Error("删除用户余额记录失败",
			"userID", userID,
			"error", err)
		return err
	}

	r.logger.Warn("用户余额记录删除成功",
		"userID", userID)
	return nil
}
