// internal/repository/redis.go
package repository

import (
	"context"
	"fmt"
	"time"

	"fp-browser/pkg/log"
	"github.com/redis/go-redis/v9"
	"github.com/spf13/viper"
)

// NewRedis 创建Redis客户端（使用viper配置）
func NewRedis(conf *viper.Viper, l *log.Logger) *redis.Client {
	rdb := redis.NewClient(&redis.Options{
		Addr:     conf.GetString("redis.addr"),
		Password: conf.GetString("redis.password"),
		DB:       conf.GetInt("redis.db"),
	})

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	_, err := rdb.Ping(ctx).Result()
	if err != nil {
		l.Error("Redis连接失败", "error", err)
		panic(fmt.Sprintf("redis error: %s", err.<PERSON>rror()))
	}

	l.Info("Redis连接成功")
	return rdb
}
