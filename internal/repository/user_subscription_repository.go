package repository

import (
	"context"
	"fp-browser/internal/dao/model"
	"time"
)

type UserSubscriptionRepository interface {
	FindByID(ctx context.Context, id int32) (*model.UserSubscription, error)
	FindActiveByTeam(ctx context.Context, teamID int32) (*model.UserSubscription, error)
	FindAllByTeam(ctx context.Context, teamID int32) ([]*model.UserSubscription, error)
	Create(ctx context.Context, subscription *model.UserSubscription) error
	Update(ctx context.Context, subscription *model.UserSubscription) error
}

type userSubscriptionRepository struct {
	*Repository
}

func NewUserSubscriptionRepository(r *Repository) UserSubscriptionRepository {
	return &userSubscriptionRepository{Repository: r}
}

// FindByID 根据 ID 获取订阅记录
func (r *userSubscriptionRepository) FindByID(ctx context.Context, id int32) (*model.UserSubscription, error) {
	subscription, err := r.query.UserSubscription.WithContext(ctx).
		Where(r.query.UserSubscription.ID.Eq(id)).
		First()

	if err != nil {
		r.logger.Error("根据ID查询用户订阅失败",
			"id", id,
			"error", err)
		return nil, err
	}

	r.logger.Debug("根据ID查询用户订阅成功",
		"id", id,
		"orderID", subscription.OrderID,
		"teamID", subscription.TeamID,
		"status", subscription.Status,
		"storageSize", subscription.StorageSize,
		"membersCount", subscription.MembersCount,
		"isActive", subscription.Status == 1,
		"isExpired", subscription.EndDate.Before(time.Now()))
	return subscription, nil
}

// FindActiveByTeam 查询指定团队当前激活的订阅
func (r *userSubscriptionRepository) FindActiveByTeam(ctx context.Context, teamID int32) (*model.UserSubscription, error) {
	now := time.Now()

	var subscriptions []*model.UserSubscription
	subscriptions, err := r.query.UserSubscription.WithContext(ctx).
		Where(
			r.query.UserSubscription.TeamID.Eq(teamID),
			r.query.UserSubscription.Status.Eq(1),
			r.query.UserSubscription.EndDate.Gt(now),
		).
		Order(r.query.UserSubscription.EndDate.Desc()).
		Limit(1).
		Find()

	if err != nil {
		r.logger.Error("查询团队活跃订阅失败",
			"teamID", teamID,
			"currentTime", now,
			"error", err)
		return nil, err
	}

	// 如果没有找到活跃订阅，返回nil，不视为错误
	if len(subscriptions) == 0 {
		r.logger.Debug("未找到团队活跃订阅",
			"teamID", teamID,
			"currentTime", now)
		return nil, nil
	}

	subscription := subscriptions[0]
	r.logger.Debug("查询团队活跃订阅成功",
		"teamID", teamID,
		"subscriptionID", subscription.ID,
		"orderID", subscription.OrderID,
		"status", subscription.Status,
		"startDate", subscription.StartDate,
		"endDate", subscription.EndDate,
		"storageSize", subscription.StorageSize,
		"membersCount", subscription.MembersCount,
		"remainingDays", int(subscription.EndDate.Sub(now).Hours()/24))

	return subscription, nil
}

// FindAllByTeam 获取全部订阅记录，根据teamID过滤，如果teamID为0则返回所有记录
func (r *userSubscriptionRepository) FindAllByTeam(ctx context.Context, teamID int32) ([]*model.UserSubscription, error) {
	stmt := r.query.UserSubscription.WithContext(ctx)

	var conditions []string
	// 如果teamID不为0，则添加team过滤条件
	if teamID != 0 {
		stmt = stmt.Where(r.query.UserSubscription.TeamID.Eq(teamID))
		conditions = append(conditions, "teamID")
	}

	subscriptions, err := stmt.Order(r.query.UserSubscription.CreatedAt.Desc()).Find()
	if err != nil {
		r.logger.Error("查询团队订阅列表失败",
			"teamID", teamID,
			"conditions", conditions,
			"error", err)
		return nil, err
	}

	// 统计订阅状态
	var activeCount, expiredCount, cancelledCount int
	now := time.Now()
	for _, sub := range subscriptions {
		switch sub.Status {
		case 1: // Active
			if sub.EndDate.After(now) {
				activeCount++
			} else {
				expiredCount++
			}
		case 2: // Expired
			expiredCount++
		case 3: // Cancelled
			cancelledCount++
		}
	}

	r.logger.Debug("查询团队订阅列表成功",
		"teamID", teamID,
		"conditions", conditions,
		"totalCount", len(subscriptions),
		"activeCount", activeCount,
		"expiredCount", expiredCount,
		"cancelledCount", cancelledCount,
		"currentTime", now)

	return subscriptions, nil
}

// Create 新建订阅
func (r *userSubscriptionRepository) Create(ctx context.Context, subscription *model.UserSubscription) error {
	err := r.query.UserSubscription.WithContext(ctx).Create(subscription)
	if err != nil {
		r.logger.Error("创建用户订阅失败",
			"orderID", subscription.OrderID,
			"teamID", subscription.TeamID,
			"storageSize", subscription.StorageSize,
			"membersCount", subscription.MembersCount,
			"status", subscription.Status,
			"startDate", subscription.StartDate,
			"endDate", subscription.EndDate,
			"error", err)
		return err
	}

	// 计算订阅时长
	duration := subscription.EndDate.Sub(subscription.StartDate)
	days := int(duration.Hours() / 24)

	r.logger.Info("用户订阅创建成功",
		"id", subscription.ID,
		"orderID", subscription.OrderID,
		"teamID", subscription.TeamID,
		"storageSize", subscription.StorageSize,
		"membersCount", subscription.MembersCount,
		"status", subscription.Status,
		"startDate", subscription.StartDate,
		"endDate", subscription.EndDate,
		"durationDays", days)
	return nil
}

// Update 全量更新订阅（按 ID 匹配）
func (r *userSubscriptionRepository) Update(ctx context.Context, subscription *model.UserSubscription) error {
	_, err := r.query.UserSubscription.WithContext(ctx).
		Where(r.query.UserSubscription.ID.Eq(subscription.ID)).
		Updates(subscription)

	if err != nil {
		r.logger.Error("更新用户订阅失败",
			"id", subscription.ID,
			"orderID", subscription.OrderID,
			"teamID", subscription.TeamID,
			"storageSize", subscription.StorageSize,
			"membersCount", subscription.MembersCount,
			"status", subscription.Status,
			"startDate", subscription.StartDate,
			"endDate", subscription.EndDate,
			"error", err)
		return err
	}

	// 计算订阅时长和剩余时间
	duration := subscription.EndDate.Sub(subscription.StartDate)
	days := int(duration.Hours() / 24)
	now := time.Now()
	var remainingDays int
	if subscription.EndDate.After(now) {
		remainingDays = int(subscription.EndDate.Sub(now).Hours() / 24)
	}

	r.logger.Info("用户订阅更新成功",
		"id", subscription.ID,
		"orderID", subscription.OrderID,
		"teamID", subscription.TeamID,
		"storageSize", subscription.StorageSize,
		"membersCount", subscription.MembersCount,
		"status", subscription.Status,
		"startDate", subscription.StartDate,
		"endDate", subscription.EndDate,
		"durationDays", days,
		"remainingDays", remainingDays,
		"isActive", subscription.Status == 1 && subscription.EndDate.After(now))
	return nil
}
