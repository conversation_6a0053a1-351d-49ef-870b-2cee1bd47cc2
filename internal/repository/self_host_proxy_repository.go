package repository

import (
	"context"

	v1 "fp-browser/api/v1/user"
	"fp-browser/internal/dao/model"
)

type SelfHostProxyRepository interface {
	Create(ctx context.Context, proxies []*model.SelfHostProxy) error
	Update(ctx context.Context, proxies []*model.SelfHostProxy) error
	Delete(ctx context.Context, ids []int32, teamID int32) error
	GetByID(ctx context.Context, id, teamID int32) (*model.SelfHostProxy, error)
	GetSelfHostProxies(ctx context.Context, id, teamID, environmentID int32, proxyType int16, name string, limit, offset int) ([]*v1.SelfHostProxyItem, int64, error)
}

type selfHostProxyRepository struct {
	*Repository
}

func NewSelfHostProxyRepository(r *Repository) SelfHostProxyRepository {
	return &selfHostProxyRepository{Repository: r}
}

func (r *selfHostProxyRepository) Create(ctx context.Context, proxies []*model.SelfHostProxy) error {
	err := r.query.SelfHostProxy.WithContext(ctx).Create(proxies...)
	if err != nil {
		r.logger.Error("批量创建自有代理失败",
			"count", len(proxies),
			"error", err)
		return err
	}

	r.logger.Info("批量创建自有代理成功",
		"count", len(proxies))

	// Debug级别记录每个代理的详情
	for _, p := range proxies {
		r.logger.Debug("自有代理创建详情",
			"id", p.ID,
			"name", p.Name,
			"teamID", p.TeamID,
			"environmentID", p.EnvironmentID,
			"type", p.Type,
			"host", p.Host,
			"port", p.Port)
	}

	return nil
}

func (r *selfHostProxyRepository) Update(ctx context.Context, proxies []*model.SelfHostProxy) error {
	q := r.query.SelfHostProxy.WithContext(ctx)

	var successCount int
	for _, p := range proxies {
		tx := q.Where(r.query.SelfHostProxy.ID.Eq(p.ID))

		var conditions []string = []string{"id"}
		if p.TeamID != 0 {
			tx = tx.Where(r.query.SelfHostProxy.TeamID.Eq(p.TeamID))
			conditions = append(conditions, "teamID")
		}

		_, err := tx.Updates(p)
		if err != nil {
			r.logger.Error("批量更新自有代理中的单个更新失败",
				"proxyID", p.ID,
				"name", p.Name,
				"teamID", p.TeamID,
				"environmentID", p.EnvironmentID,
				"type", p.Type,
				"conditions", conditions,
				"successCount", successCount,
				"totalCount", len(proxies),
				"error", err)
			return err
		}
		successCount++

		r.logger.Debug("自有代理更新成功",
			"proxyID", p.ID,
			"name", p.Name,
			"teamID", p.TeamID,
			"environmentID", p.EnvironmentID,
			"type", p.Type,
			"conditions", conditions)
	}

	r.logger.Info("批量更新自有代理成功",
		"count", len(proxies),
		"successCount", successCount)
	return nil
}

func (r *selfHostProxyRepository) Delete(ctx context.Context, ids []int32, teamID int32) error {
	q := r.query.SelfHostProxy.WithContext(ctx)
	e := r.query.Environment

	// 先查找并更新引用这些代理的环境记录
	// 将所有使用这些自有代理(proxy_type=2)的环境记录的proxy_id设置为0
	// 这样可以避免删除代理后，环境表中仍然引用已不存在的代理ID导致的错误
	_, err := e.WithContext(ctx).
		Where(e.ProxyID.In(ids...), e.ProxyType.Eq(int16(2))).
		Update(e.ProxyID, 0)

	if err != nil {
		r.logger.Error("更新引用自有代理的环境记录失败",
			"proxyIDs", ids,
			"error", err)
		return err
	}

	// 构建删除自有代理的查询条件
	tx := q.Where(r.query.SelfHostProxy.ID.In(ids...))
	var conditions []string = []string{"ids"}

	if teamID != 0 {
		tx = tx.Where(r.query.SelfHostProxy.TeamID.Eq(teamID))
		conditions = append(conditions, "teamID")
	}

	// 执行删除操作
	_, err = tx.Delete()
	if err != nil {
		r.logger.Error("批量删除自有代理失败",
			"ids", ids,
			"teamID", teamID,
			"conditions", conditions,
			"count", len(ids),
			"error", err)
		return err
	}

	r.logger.Info("批量删除自有代理成功",
		"ids", ids,
		"teamID", teamID,
		"conditions", conditions,
		"count", len(ids))
	return nil
}

func (r *selfHostProxyRepository) GetByID(ctx context.Context, id, teamID int32) (*model.SelfHostProxy, error) {
	q := r.query.SelfHostProxy.WithContext(ctx)

	tx := q.Where(r.query.SelfHostProxy.ID.Eq(id))
	var conditions []string = []string{"id"}

	if teamID != 0 {
		tx = tx.Where(r.query.SelfHostProxy.TeamID.Eq(teamID))
		conditions = append(conditions, "teamID")
	}

	proxy, err := tx.First()
	if err != nil {
		r.logger.Error("根据ID查询自有代理失败",
			"id", id,
			"teamID", teamID,
			"conditions", conditions,
			"error", err)
		return nil, err
	}

	r.logger.Debug("根据ID查询自有代理成功",
		"id", id,
		"teamID", teamID,
		"conditions", conditions,
		"proxyName", proxy.Name,
		"proxyType", proxy.Type,
		"proxyEnvironmentID", proxy.EnvironmentID)
	return proxy, nil
}

func (r *selfHostProxyRepository) GetSelfHostProxies(ctx context.Context, id, teamID, environmentID int32, proxyType int16, name string, limit, offset int) ([]*v1.SelfHostProxyItem, int64, error) {
	q := r.query.SelfHostProxy
	db := q.WithContext(ctx)

	var conditions []string

	// 构建基础查询，使用LEFT JOIN连接环境表和团队表
	baseQuery := q.WithContext(ctx).
		LeftJoin(r.query.Environment, q.EnvironmentID.EqCol(r.query.Environment.ID)).
		LeftJoin(r.query.Team, q.TeamID.EqCol(r.query.Team.ID))

	// 应用筛选条件
	if id != 0 {
		baseQuery = baseQuery.Where(q.ID.Eq(id))
		db = db.Where(q.ID.Eq(id))
		conditions = append(conditions, "id")
	}
	if teamID != 0 {
		baseQuery = baseQuery.Where(q.TeamID.Eq(teamID))
		db = db.Where(q.TeamID.Eq(teamID))
		conditions = append(conditions, "teamID")
	}
	if environmentID >= 0 { // 保持原有的特殊判断逻辑
		baseQuery = baseQuery.Where(q.EnvironmentID.Eq(environmentID))
		db = db.Where(q.EnvironmentID.Eq(environmentID))
		conditions = append(conditions, "environmentID")
	}
	if proxyType != 0 {
		baseQuery = baseQuery.Where(q.Type.Eq(proxyType))
		db = db.Where(q.Type.Eq(proxyType))
		conditions = append(conditions, "proxyType")
	}
	if name != "" {
		baseQuery = baseQuery.Where(q.Name.Like("%" + name + "%"))
		db = db.Where(q.Name.Like("%" + name + "%"))
		conditions = append(conditions, "name")
	}

	// 获取总数
	total, err := baseQuery.Count()
	if err != nil {
		r.logger.Error("查询自有代理总数失败",
			"id", id,
			"teamID", teamID,
			"environmentID", environmentID,
			"proxyType", proxyType,
			"name", name,
			"limit", limit,
			"offset", offset,
			"conditions", conditions,
			"error", err)
		return nil, 0, err
	}

	// 使用关联查询获取完整数据
	type Result struct {
		model.SelfHostProxy
		EnvironmentName *string `gorm:"column:environment_name"`
		TeamName        *string `gorm:"column:team_name"`
	}

	var joinResults []Result

	err = baseQuery.Select(
		q.ALL,
		r.query.Environment.Name.As("environment_name"),
		r.query.Team.Name.As("team_name"),
	).Order(q.ID.Desc()).Limit(limit).Offset(offset).Scan(&joinResults)

	if err != nil {
		r.logger.Error("分页查询自有代理失败",
			"id", id,
			"teamID", teamID,
			"environmentID", environmentID,
			"proxyType", proxyType,
			"name", name,
			"limit", limit,
			"offset", offset,
			"conditions", conditions,
			"total", total,
			"error", err)
		return nil, 0, err
	}

	r.logger.Debug("分页查询自有代理成功",
		"id", id,
		"teamID", teamID,
		"environmentID", environmentID,
		"proxyType", proxyType,
		"name", name,
		"limit", limit,
		"offset", offset,
		"conditions", conditions,
		"conditionCount", len(conditions),
		"total", total,
		"returnCount", len(joinResults))

	// 转换数据库模型为API模型
	proxyItems := make([]*v1.SelfHostProxyItem, len(joinResults))
	for i, result := range joinResults {
		// 创建SelfHostProxyItem，确保字段类型匹配
		item := &v1.SelfHostProxyItem{
			ID:        result.ID,
			ProxyType: result.Type,
			Port:      result.Port,
		}

		// 处理可能为nil的指针字段
		if result.Name != nil {
			item.Name = *result.Name
		}

		if result.Host != nil {
			item.Host = *result.Host
		}

		// 设置环境名称和团队名称
		if result.EnvironmentName != nil {
			item.EnvironmentName = *result.EnvironmentName
		}

		if result.TeamName != nil {
			item.TeamName = *result.TeamName
		}

		proxyItems[i] = item
	}

	return proxyItems, total, nil
}
