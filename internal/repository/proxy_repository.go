// internal/repository/proxy_repository.go
package repository

import (
	"context"
	"gorm.io/gorm"
	"time"

	"fp-browser/internal/dao/model"
)

type ProxyRepository interface {
	Create(ctx context.Context, proxy *model.Proxy) error
	Update(ctx context.Context, proxy *model.Proxy) error
	GetProxies(ctx context.Context, id, teamID, environmentID int32, name string, limit, offset int, expiresAt *time.Time, autoRenew *bool) ([]*model.Proxy, int64, error)
	FindAllByNodeID(ctx context.Context, nodeID int32) ([]*model.Proxy, error)
	BatchUpdateProxies(ctx context.Context, proxies []*model.Proxy) error
	Delete(ctx context.Context, id int32) error
}

type proxyRepository struct {
	*Repository
}

func NewProxyRepository(r *Repository) ProxyRepository {
	return &proxyRepository{Repository: r}
}

func (r *proxyRepository) Create(ctx context.Context, proxy *model.Proxy) error {
	err := r.query.Proxy.WithContext(ctx).Create(proxy)
	if err != nil {
		r.logger.Error("创建代理失败",
			"teamID", proxy.TeamID,
			"name", proxy.Name,
			"nodeID", proxy.NodeID,
			"environmentID", proxy.EnvironmentID,
			"trafficLimit", proxy.TrafficLimit,
			"autoRenew", proxy.AutoRenew,
			"error", err)
		return err
	}

	r.logger.Info("代理创建成功",
		"id", proxy.ID,
		"teamID", proxy.TeamID,
		"name", proxy.Name,
		"nodeID", proxy.NodeID,
		"environmentID", proxy.EnvironmentID,
		"trafficLimit", proxy.TrafficLimit,
		"autoRenew", proxy.AutoRenew)
	return nil
}

func (r *proxyRepository) Update(ctx context.Context, proxy *model.Proxy) error {
	_, err := r.query.Proxy.WithContext(ctx).
		Where(r.query.Proxy.ID.Eq(proxy.ID)).
		Updates(proxy)

	if err != nil {
		r.logger.Error("更新代理失败",
			"id", proxy.ID,
			"teamID", proxy.TeamID,
			"name", proxy.Name,
			"nodeID", proxy.NodeID,
			"environmentID", proxy.EnvironmentID,
			"trafficLimit", proxy.TrafficLimit,
			"autoRenew", proxy.AutoRenew,
			"error", err)
		return err
	}

	r.logger.Info("代理更新成功",
		"id", proxy.ID,
		"teamID", proxy.TeamID,
		"name", proxy.Name,
		"nodeID", proxy.NodeID,
		"environmentID", proxy.EnvironmentID,
		"trafficLimit", proxy.TrafficLimit,
		"autoRenew", proxy.AutoRenew)
	return nil
}

func (r *proxyRepository) GetProxies(ctx context.Context, id, teamID, environmentID int32, name string, limit, offset int, expiresAt *time.Time, autoRenew *bool) ([]*model.Proxy, int64, error) {
	q := r.query.Proxy
	db := q.WithContext(ctx)

	var conditions []string

	if id != 0 {
		db = db.Where(q.ID.Eq(id))
		conditions = append(conditions, "id")
	}
	if teamID != 0 {
		db = db.Where(q.TeamID.Eq(teamID))
		conditions = append(conditions, "teamID")
	}
	if environmentID != 0 {
		db = db.Where(q.EnvironmentID.Eq(environmentID))
		conditions = append(conditions, "environmentID")
	}
	if name != "" {
		db = db.Where(q.Name.Like("%" + name + "%"))
		conditions = append(conditions, "name")
	}
	if expiresAt != nil {
		db = db.Where(q.ExpiresAt.Lte(*expiresAt))
		conditions = append(conditions, "expiresAt")
	}
	if autoRenew != nil {
		db = db.Where(q.AutoRenew.Is(*autoRenew))
		conditions = append(conditions, "autoRenew")
	}

	originalLimit := limit
	if limit <= 0 || limit > 500 {
		limit = 500
		if originalLimit > 500 {
			r.logger.Warn("查询限制已调整为最大值",
				"requestedLimit", originalLimit,
				"adjustedLimit", 500)
		}
	}

	count, err := db.Count()
	if err != nil {
		r.logger.Error("查询代理总数失败",
			"id", id,
			"teamID", teamID,
			"environmentID", environmentID,
			"name", name,
			"limit", limit,
			"offset", offset,
			"originalLimit", originalLimit,
			"conditions", conditions,
			"expiresAt", expiresAt,
			"autoRenew", autoRenew,
			"error", err)
		return nil, 0, err
	}

	res, err := db.Limit(limit).Offset(offset).Order(q.CreatedAt.Desc()).Find()
	if err != nil {
		r.logger.Error("分页查询代理失败",
			"id", id,
			"teamID", teamID,
			"environmentID", environmentID,
			"name", name,
			"limit", limit,
			"offset", offset,
			"originalLimit", originalLimit,
			"conditions", conditions,
			"total", count,
			"expiresAt", expiresAt,
			"autoRenew", autoRenew,
			"error", err)
		return nil, 0, err
	}

	r.logger.Debug("分页查询代理成功",
		"id", id,
		"teamID", teamID,
		"environmentID", environmentID,
		"name", name,
		"limit", limit,
		"offset", offset,
		"originalLimit", originalLimit,
		"conditions", conditions,
		"conditionCount", len(conditions),
		"total", count,
		"returnCount", len(res),
		"expiresAt", expiresAt,
		"autoRenew", autoRenew)

	return res, count, nil
}

func (r *proxyRepository) FindAllByNodeID(ctx context.Context, nodeID int32) ([]*model.Proxy, error) {
	proxies, err := r.query.Proxy.WithContext(ctx).
		Where(r.query.Proxy.NodeID.Eq(nodeID)).
		Find()

	if err != nil {
		r.logger.Error("根据节点ID查询代理失败",
			"nodeID", nodeID,
			"error", err)
		return nil, err
	}

	r.logger.Debug("根据节点ID查询代理成功",
		"nodeID", nodeID,
		"count", len(proxies))
	return proxies, nil
}

func (r *proxyRepository) BatchUpdateProxies(ctx context.Context, proxies []*model.Proxy) error {
	db := r.query.Proxy.WithContext(ctx).UnderlyingDB() // *gorm.DB

	err := db.Transaction(func(tx *gorm.DB) error {
		for _, p := range proxies {
			if err := tx.Model(&model.Proxy{}).
				Where("id = ?", p.ID).
				Update("traffic_limit", p.TrafficLimit).Error; err != nil {
				r.logger.Error("批量更新代理中的单个更新失败",
					"proxyID", p.ID,
					"trafficLimit", p.TrafficLimit,
					"error", err)
				return err
			}
		}
		return nil
	})

	if err != nil {
		r.logger.Error("批量更新代理失败",
			"count", len(proxies),
			"error", err)
		return err
	}

	r.logger.Info("批量更新代理成功",
		"count", len(proxies))

	// 记录每个更新的代理详情（Debug级别）
	for _, p := range proxies {
		r.logger.Debug("代理流量限制更新",
			"proxyID", p.ID,
			"trafficLimit", p.TrafficLimit)
	}

	return nil
}

func (r *proxyRepository) Delete(ctx context.Context, id int32) error {
	_, err := r.query.Proxy.WithContext(ctx).
		Where(r.query.Proxy.ID.Eq(id)).
		Delete()

	if err != nil {
		r.logger.Error("删除代理失败",
			"id", id,
			"error", err)
		return err
	}

	r.logger.Info("代理删除成功", "id", id)
	return nil
}
