package repository

import (
	"context"

	"fp-browser/internal/dao/model"
)

type TeamRepository interface {
	Create(ctx context.Context, name string, ownerID int32) (*model.Team, error)
	GetByID(ctx context.Context, id int32) (*model.Team, error)
	GetByOwnerID(ctx context.Context, ownerID int32) (*model.Team, error)
	Update(ctx context.Context, id int32, name string) error
	Delete(ctx context.Context, id int32) error
}

type teamRepository struct {
	*Repository
}

func NewTeamRepository(r *Repository) TeamRepository {
	return &teamRepository{Repository: r}
}

func (r *teamRepository) Create(ctx context.Context, name string, ownerID int32) (*model.Team, error) {
	team := &model.Team{
		Name:    name,
		OwnerID: ownerID,
	}

	err := r.query.Team.WithContext(ctx).Create(team)
	if err != nil {
		r.logger.Error("创建团队失败",
			"name", name,
			"ownerID", ownerID,
			"error", err)
		return nil, err
	}

	r.logger.Info("团队创建成功",
		"id", team.ID,
		"name", name,
		"ownerID", ownerID)
	return team, nil
}

func (r *teamRepository) GetByID(ctx context.Context, id int32) (*model.Team, error) {
	team, err := r.query.Team.WithContext(ctx).Where(r.query.Team.ID.Eq(id)).First()
	if err != nil {
		r.logger.Error("根据ID查询团队失败",
			"id", id,
			"error", err)
		return nil, err
	}

	r.logger.Debug("根据ID查询团队成功",
		"id", id,
		"name", team.Name,
		"ownerID", team.OwnerID)
	return team, nil
}

func (r *teamRepository) GetByOwnerID(ctx context.Context, ownerID int32) (*model.Team, error) {
	team, err := r.query.Team.WithContext(ctx).Where(r.query.Team.OwnerID.Eq(ownerID)).First()
	if err != nil {
		r.logger.Error("根据所有者ID查询团队失败",
			"ownerID", ownerID,
			"error", err)
		return nil, err
	}

	r.logger.Debug("根据所有者ID查询团队成功",
		"ownerID", ownerID,
		"teamID", team.ID,
		"name", team.Name)
	return team, nil
}

func (r *teamRepository) Update(ctx context.Context, id int32, name string) error {
	_, err := r.query.Team.WithContext(ctx).
		Where(r.query.Team.ID.Eq(id)).
		UpdateSimple(r.query.Team.Name.Value(name))

	if err != nil {
		r.logger.Error("更新团队名称失败",
			"id", id,
			"newName", name,
			"error", err)
		return err
	}

	r.logger.Info("团队名称更新成功",
		"id", id,
		"newName", name)
	return nil
}

func (r *teamRepository) Delete(ctx context.Context, id int32) error {
	_, err := r.query.Team.WithContext(ctx).
		Where(r.query.Team.ID.Eq(id)).
		Delete()

	if err != nil {
		r.logger.Error("删除团队失败",
			"id", id,
			"error", err)
		return err
	}

	r.logger.Warn("团队删除成功",
		"id", id)
	return nil
}
