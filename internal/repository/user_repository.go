package repository

import (
	"context"
	"crypto/rand"
	"fmt"
	"regexp"
	"strings"

	"fp-browser/internal/dao/model"
	"fp-browser/internal/view_model"
	"golang.org/x/crypto/bcrypt"
)

type UserRepository interface {
	Create(ctx context.Context, user *model.User) (*model.User, error)
	Update(ctx context.Context, users []*model.User, teamID int32) error
	Delete(ctx context.Context, ids []int32, teamID int32) error
	GetUsers(ctx context.Context, username string, teamID, roleID int32, limit, offset int) ([]*model.User, int64, error)
	FindByID(ctx context.Context, id int32) (*view_model.User, error)
	FindUserByID(ctx context.Context, id int32) (*model.User, error)
	FindByIdentifier(ctx context.Context, identifier string) (*view_model.User, error)
	GetUserByInviteCode(ctx context.Context, inviteCode string) (*model.User, error)
}

type userRepository struct {
	*Repository
	envRepo EnvironmentRepository
}

func NewUserRepository(r *Repository, envRepo EnvironmentRepository) UserRepository {
	return &userRepository{
		Repository: r,
		envRepo:    envRepo,
	}
}

// generateInviteCode 生成8位字母和数字混合的邀请码
func (r *userRepository) generateInviteCode() (string, error) {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	const length = 8

	b := make([]byte, length)
	_, err := rand.Read(b)
	if err != nil {
		return "", fmt.Errorf("failed to generate random bytes: %w", err)
	}

	var result strings.Builder
	for i := 0; i < length; i++ {
		result.WriteByte(charset[int(b[i])%len(charset)])
	}

	return result.String(), nil
}

// generateUniqueInviteCode 生成唯一的邀请码，确保在数据库中不重复
func (r *userRepository) generateUniqueInviteCode(ctx context.Context) (string, error) {
	q := r.query.User.WithContext(ctx)
	maxAttempts := 10 // 最多尝试10次避免无限循环

	for attempt := 0; attempt < maxAttempts; attempt++ {
		inviteCode, err := r.generateInviteCode()
		if err != nil {
			return "", fmt.Errorf("failed to generate invite code: %w", err)
		}

		// 检查邀请码是否已存在
		count, err := q.Where(r.query.User.InviteCode.Eq(inviteCode)).Count()
		if err != nil {
			r.logger.Error("检查邀请码唯一性失败",
				"inviteCode", inviteCode,
				"attempt", attempt+1,
				"error", err)
			return "", fmt.Errorf("failed to check invite code uniqueness: %w", err)
		}

		if count == 0 {
			r.logger.Debug("生成唯一邀请码成功",
				"inviteCode", inviteCode,
				"attempt", attempt+1)
			return inviteCode, nil
		}

		r.logger.Debug("邀请码已存在，重新生成",
			"inviteCode", inviteCode,
			"attempt", attempt+1)
	}

	return "", fmt.Errorf("failed to generate unique invite code after %d attempts", maxAttempts)
}

func (r *userRepository) Create(ctx context.Context, user *model.User) (*model.User, error) {
	q := r.query.User.WithContext(ctx)

	// 检查用户名是否存在
	if user.UserName != "" {
		count, err := q.Where(r.query.User.UserName.Eq(user.UserName)).Count()
		if err != nil {
			r.logger.Error("检查用户名唯一性失败",
				"username", user.UserName,
				"error", err)
			return nil, fmt.Errorf("check username failed: %w", err)
		}
		if count > 0 {
			r.logger.Warn("用户名已存在",
				"username", user.UserName)
			return nil, fmt.Errorf("username already exists")
		}
	}

	// 检查邮箱是否存在
	if user.Email != "" {
		count, err := q.Where(r.query.User.Email.Eq(user.Email)).Count()
		if err != nil {
			r.logger.Error("检查邮箱唯一性失败",
				"email", user.Email,
				"error", err)
			return nil, fmt.Errorf("check email failed: %w", err)
		}
		if count > 0 {
			r.logger.Warn("邮箱已存在",
				"email", user.Email)
			return nil, fmt.Errorf("email already exists")
		}
	}

	// 检查手机号是否存在
	if user.Telephone != "" {
		count, err := q.Where(r.query.User.Telephone.Eq(user.Telephone)).Count()
		if err != nil {
			r.logger.Error("检查手机号唯一性失败",
				"telephone", user.Telephone,
				"error", err)
			return nil, fmt.Errorf("check telephone failed: %w", err)
		}
		if count > 0 {
			r.logger.Warn("手机号已存在",
				"telephone", user.Telephone)
			return nil, fmt.Errorf("telephone already exists")
		}
	}

	// 生成唯一邀请码
	if user.InviteCode == nil {
		inviteCode, err := r.generateUniqueInviteCode(ctx)
		if err != nil {
			r.logger.Error("生成邀请码失败",
				"username", user.UserName,
				"error", err)
			return nil, fmt.Errorf("failed to generate invite code: %w", err)
		}
		user.InviteCode = &inviteCode
	}

	// 密码加密
	var hasPassword bool
	if user.Password != "" {
		hash, err := bcrypt.GenerateFromPassword([]byte(user.Password), bcrypt.DefaultCost)
		if err != nil {
			r.logger.Error("密码加密失败",
				"username", user.UserName,
				"error", err)
			return nil, fmt.Errorf("password hashing failed: %w", err)
		}
		user.Password = string(hash)
		hasPassword = true
	}

	// 真正创建
	if err := q.Create(user); err != nil {
		r.logger.Error("创建用户失败",
			"username", user.UserName,
			"email", user.Email,
			"telephone", user.Telephone,
			"teamID", user.TeamID,
			"roleID", user.RoleID,
			"inviteCode", user.InviteCode,
			"hasPassword", hasPassword,
			"error", err)
		return nil, err
	}

	r.logger.Info("用户创建成功",
		"id", user.ID,
		"username", user.UserName,
		"email", user.Email,
		"telephone", user.Telephone,
		"teamID", user.TeamID,
		"roleID", user.RoleID,
		"inviteCode", user.InviteCode,
		"hasPassword", hasPassword)

	// 返回创建好的 user（ID 已经赋值）
	return user, nil
}

func (r *userRepository) Update(ctx context.Context, users []*model.User, teamID int32) error {
	q := r.query.User.WithContext(ctx)

	var successCount int
	for _, user := range users {
		// 在更新前检查邮箱唯一性（排除当前用户）
		if user.Email != "" {
			count, err := q.Where(r.query.User.Email.Eq(user.Email)).Where(r.query.User.ID.Neq(user.ID)).Count()
			if err != nil {
				r.logger.Error("检查邮箱唯一性失败",
					"userID", user.ID,
					"email", user.Email,
					"teamID", teamID,
					"successCount", successCount,
					"totalCount", len(users),
					"error", err)
				return fmt.Errorf("check email uniqueness failed: %w", err)
			}
			if count > 0 {
				r.logger.Warn("邮箱已存在",
					"userID", user.ID,
					"email", user.Email,
					"teamID", teamID)
				return fmt.Errorf("email already exists")
			}
		}

		// 在更新前检查手机号唯一性（排除当前用户）
		if user.Telephone != "" {
			count, err := q.Where(r.query.User.Telephone.Eq(user.Telephone)).Where(r.query.User.ID.Neq(user.ID)).Count()
			if err != nil {
				r.logger.Error("检查手机号唯一性失败",
					"userID", user.ID,
					"telephone", user.Telephone,
					"teamID", teamID,
					"successCount", successCount,
					"totalCount", len(users),
					"error", err)
				return fmt.Errorf("check telephone uniqueness failed: %w", err)
			}
			if count > 0 {
				r.logger.Warn("手机号已存在",
					"userID", user.ID,
					"telephone", user.Telephone,
					"teamID", teamID)
				return fmt.Errorf("telephone already exists")
			}
		}

		// 构建更新查询条件
		tx := q.Where(r.query.User.ID.Eq(user.ID))
		var conditions []string = []string{"userID"}
		if teamID != 0 {
			tx = tx.Where(r.query.User.TeamID.Eq(teamID))
			conditions = append(conditions, "teamID")
		}

		// 密码字段处理
		var hasPasswordUpdate bool
		if user.Password != "" {
			hash, err := bcrypt.GenerateFromPassword([]byte(user.Password), bcrypt.DefaultCost)
			if err != nil {
				r.logger.Error("批量更新用户中的密码加密失败",
					"userID", user.ID,
					"username", user.UserName,
					"teamID", teamID,
					"successCount", successCount,
					"totalCount", len(users),
					"error", err)
				return fmt.Errorf("password hashing failed: %w", err)
			}
			user.Password = string(hash)
			hasPasswordUpdate = true
		} else {
			tx = tx.Omit(r.query.User.Password) // 密码为空，不更新密码
		}

		// 执行更新
		_, err := tx.Updates(user)
		if err != nil {
			r.logger.Error("批量更新用户失败",
				"userID", user.ID,
				"username", user.UserName,
				"teamID", teamID,
				"conditions", conditions,
				"hasPasswordUpdate", hasPasswordUpdate,
				"successCount", successCount,
				"totalCount", len(users),
				"error", err)
			return err
		}

		successCount++
		r.logger.Debug("用户更新成功",
			"userID", user.ID,
			"username", user.UserName,
			"email", user.Email,
			"telephone", user.Telephone,
			"teamID", teamID,
			"roleID", user.RoleID,
			"conditions", conditions,
			"hasPasswordUpdate", hasPasswordUpdate)
	}

	r.logger.Info("批量更新用户成功",
		"teamID", teamID,
		"successCount", successCount,
		"totalCount", len(users))
	return nil
}

func (r *userRepository) Delete(ctx context.Context, ids []int32, teamID int32) error {
	q := r.query.User.WithContext(ctx)

	tx := q.Where(r.query.User.ID.In(ids...))
	var conditions []string = []string{"userIDs"}

	if teamID != 0 {
		tx = tx.Where(r.query.User.TeamID.Eq(teamID))
		conditions = append(conditions, "teamID")
	}

	_, err := tx.Delete()
	if err != nil {
		r.logger.Error("批量删除用户失败",
			"ids", ids,
			"teamID", teamID,
			"conditions", conditions,
			"count", len(ids),
			"error", err)
		return err
	}

	r.logger.Warn("批量删除用户成功",
		"ids", ids,
		"teamID", teamID,
		"conditions", conditions,
		"count", len(ids))
	return nil
}

func (r *userRepository) GetUsers(ctx context.Context, username string, teamID, roleID int32, limit, offset int) ([]*model.User, int64, error) {
	q := r.query.User.WithContext(ctx)

	var conditions []string
	if username != "" {
		q = q.Where(r.query.User.UserName.Like("%" + username + "%"))
		conditions = append(conditions, "username")
	}
	if teamID != 0 {
		q = q.Where(r.query.User.TeamID.Eq(teamID))
		conditions = append(conditions, "teamID")
	}
	if roleID != 0 {
		q = q.Where(r.query.User.RoleID.Eq(roleID))
		conditions = append(conditions, "roleID")
	}

	users, count, err := q.FindByPage(offset, limit)
	if err != nil {
		r.logger.Error("分页查询用户失败",
			"username", username,
			"teamID", teamID,
			"roleID", roleID,
			"limit", limit,
			"offset", offset,
			"conditions", conditions,
			"error", err)
		return nil, 0, err
	}

	r.logger.Debug("分页查询用户成功",
		"username", username,
		"teamID", teamID,
		"roleID", roleID,
		"limit", limit,
		"offset", offset,
		"conditions", conditions,
		"conditionCount", len(conditions),
		"total", count,
		"returnCount", len(users))

	return users, count, nil
}

func (r *userRepository) FindByID(ctx context.Context, id int32) (*view_model.User, error) {
	u := r.query.User
	t := r.query.Team
	ro := r.query.Role
	ub := r.query.UserBalance
	us := r.query.UserSubscription

	q := u.WithContext(ctx).
		Select(
			u.ALL,
			t.ALL,
			ro.ALL,
			ub.WalletAmount,
			us.ALL,
		).
		LeftJoin(t, t.ID.EqCol(u.TeamID)).
		LeftJoin(ro, ro.ID.EqCol(u.RoleID)).
		LeftJoin(ub, ub.UserID.EqCol(u.ID)).
		LeftJoin(us, us.TeamID.EqCol(u.TeamID)).
		Where(u.ID.Eq(id))

	type userWithJoinsRaw struct {
		model.User
		model.Team
		model.Role
		WalletAmount int64 `gorm:"column:wallet_amount"`
		model.UserSubscription
	}
	var raw userWithJoinsRaw

	err := q.Scan(&raw)
	if err != nil {
		r.logger.Error("根据ID查询用户详情失败",
			"id", id,
			"error", err)
		return nil, err
	}

	// 获取团队环境大小总和
	var envSizeSum int64
	if raw.Team.ID != 0 {
		envSizeSum, err = r.envRepo.GetEnvironmentsSizeSum(ctx, raw.Team.ID)
		if err != nil {
			r.logger.Warn("获取团队环境大小总和失败",
				"userID", id,
				"teamID", raw.Team.ID,
				"error", err)
			// 如果获取环境大小失败，记录错误但不中断，设置为0
			envSizeSum = 0
		}
	}

	result := &view_model.User{
		User: raw.User,
		Team: &model.Team{
			ID:        raw.Team.ID,
			Name:      raw.Team.Name,
			OwnerID:   raw.Team.OwnerID,
			CreatedAt: raw.Team.CreatedAt,
			UpdatedAt: raw.Team.UpdatedAt,
		},
		WalletAmount:       raw.WalletAmount,
		EnvironmentSizeSum: envSizeSum,
	}

	// 如果用户的RoleID为0或者查询不到Role记录，不设置Role，保持为nil
	if raw.User.RoleID != 0 && raw.Role.ID != 0 {
		result.Role = &model.Role{
			ID:          raw.Role.ID,
			Name:        raw.Role.Name,
			TeamID:      raw.Role.TeamID,
			Permissions: raw.Role.Permissions,
			CreatedAt:   raw.Role.CreatedAt,
			UpdatedAt:   raw.Role.UpdatedAt,
		}
	}

	if raw.UserSubscription.ID != 0 {
		result.Subscription = &model.UserSubscription{
			ID:           raw.UserSubscription.ID,
			OrderID:      raw.UserSubscription.OrderID,
			TeamID:       raw.UserSubscription.TeamID,
			StartDate:    raw.UserSubscription.StartDate,
			EndDate:      raw.UserSubscription.EndDate,
			StorageSize:  raw.UserSubscription.StorageSize,
			MembersCount: raw.UserSubscription.MembersCount,
			TotalPrice:   raw.UserSubscription.TotalPrice,
			Status:       raw.UserSubscription.Status,
			CreatedAt:    raw.UserSubscription.CreatedAt,
			UpdatedAt:    raw.UserSubscription.UpdatedAt,
		}
	}

	r.logger.Debug("根据ID查询用户详情成功",
		"id", id,
		"username", raw.User.UserName,
		"teamID", raw.Team.ID,
		"teamName", raw.Team.Name,
		"roleID", raw.User.RoleID,
		"roleName", func() string {
			if result.Role != nil {
				return result.Role.Name
			}
			return "无角色"
		}(),
		"hasWalletAmount", raw.WalletAmount != 0,
		"hasSubscription", raw.UserSubscription.ID != 0,
		"envSizeSum", envSizeSum)

	return result, nil
}

// FindUserByID 根据ID查询用户基本信息，返回 *model.User
func (r *userRepository) FindUserByID(ctx context.Context, id int32) (*model.User, error) {
	q := r.query.User.WithContext(ctx)

	user, err := q.Where(r.query.User.ID.Eq(id)).First()
	if err != nil {
		r.logger.Error("根据ID查询用户基本信息失败",
			"id", id,
			"error", err)
		return nil, err
	}

	r.logger.Debug("根据ID查询用户基本信息成功",
		"id", id,
		"username", user.UserName,
		"teamID", user.TeamID,
		"roleID", user.RoleID)

	return user, nil
}

func (r *userRepository) FindByIdentifier(ctx context.Context, identifier string) (*view_model.User, error) {
	u := r.query.User
	t := r.query.Team
	ro := r.query.Role
	ub := r.query.UserBalance
	us := r.query.UserSubscription

	q := u.WithContext(ctx).
		Select(
			u.ALL,
			t.ALL,
			ro.ALL,
			ub.WalletAmount,
			us.ALL,
		).
		LeftJoin(t, t.ID.EqCol(u.TeamID)).
		LeftJoin(ro, ro.ID.EqCol(u.RoleID)).
		LeftJoin(ub, ub.UserID.EqCol(u.ID)).
		LeftJoin(us, us.TeamID.EqCol(u.TeamID))

	var identifierType string
	switch {
	case isEmail(identifier):
		q = q.Where(u.Email.Eq(identifier))
		identifierType = "email"
	case isPhoneNumber(identifier):
		q = q.Where(u.Telephone.Eq(identifier))
		identifierType = "phone"
	default:
		q = q.Where(u.UserName.Eq(identifier))
		identifierType = "username"
	}

	type userWithJoinsRaw struct {
		model.User
		model.Team
		model.Role
		WalletAmount int64 `gorm:"column:wallet_amount"`
		model.UserSubscription
	}
	var raw userWithJoinsRaw

	err := q.Scan(&raw)
	if err != nil {
		r.logger.Error("根据标识符查询用户详情失败",
			"identifier", identifier,
			"identifierType", identifierType,
			"error", err)
		return nil, err
	}

	// 获取团队环境大小总和
	var envSizeSum int64
	if raw.Team.ID != 0 {
		envSizeSum, err = r.envRepo.GetEnvironmentsSizeSum(ctx, raw.Team.ID)
		if err != nil {
			r.logger.Warn("获取团队环境大小总和失败",
				"identifier", identifier,
				"identifierType", identifierType,
				"userID", raw.User.ID,
				"teamID", raw.Team.ID,
				"error", err)
			// 如果获取环境大小失败，记录错误但不中断，设置为0
			envSizeSum = 0
		}
	}

	result := &view_model.User{
		User: raw.User,
		Team: &model.Team{
			ID:        raw.Team.ID,
			Name:      raw.Team.Name,
			OwnerID:   raw.Team.OwnerID,
			CreatedAt: raw.Team.CreatedAt,
			UpdatedAt: raw.Team.UpdatedAt,
		},
		WalletAmount:       raw.WalletAmount,
		EnvironmentSizeSum: envSizeSum,
	}

	// 如果用户的RoleID为0或者查询不到Role记录，不设置Role，保持为nil
	if raw.User.RoleID != 0 && raw.Role.ID != 0 {
		result.Role = &model.Role{
			ID:          raw.Role.ID,
			Name:        raw.Role.Name,
			TeamID:      raw.Role.TeamID,
			Permissions: raw.Role.Permissions,
			CreatedAt:   raw.Role.CreatedAt,
			UpdatedAt:   raw.Role.UpdatedAt,
		}
	}

	if raw.UserSubscription.ID != 0 {
		result.Subscription = &model.UserSubscription{
			ID:           raw.UserSubscription.ID,
			OrderID:      raw.UserSubscription.OrderID,
			TeamID:       raw.UserSubscription.TeamID,
			StartDate:    raw.UserSubscription.StartDate,
			EndDate:      raw.UserSubscription.EndDate,
			StorageSize:  raw.UserSubscription.StorageSize,
			MembersCount: raw.UserSubscription.MembersCount,
			TotalPrice:   raw.UserSubscription.TotalPrice,
			Status:       raw.UserSubscription.Status,
			CreatedAt:    raw.UserSubscription.CreatedAt,
			UpdatedAt:    raw.UserSubscription.UpdatedAt,
		}
	}

	r.logger.Debug("根据标识符查询用户详情成功",
		"identifier", identifier,
		"identifierType", identifierType,
		"userID", raw.User.ID,
		"username", raw.User.UserName,
		"teamID", raw.Team.ID,
		"teamName", raw.Team.Name,
		"roleID", raw.User.RoleID,
		"roleName", func() string {
			if result.Role != nil {
				return result.Role.Name
			}
			return "无角色"
		}(),
		"hasWalletAmount", raw.WalletAmount != 0,
		"hasSubscription", raw.UserSubscription.ID != 0,
		"envSizeSum", envSizeSum)

	return result, nil
}

// GetUserByInviteCode 根据邀请码查询用户
func (r *userRepository) GetUserByInviteCode(ctx context.Context, inviteCode string) (*model.User, error) {
	q := r.query.User.WithContext(ctx)

	user, err := q.Where(r.query.User.InviteCode.Eq(inviteCode)).First()
	if err != nil {
		r.logger.Error("根据邀请码查询用户失败",
			"inviteCode", inviteCode,
			"error", err)
		return nil, err
	}

	r.logger.Debug("根据邀请码查询用户成功",
		"inviteCode", inviteCode,
		"userID", user.ID,
		"username", user.UserName)

	return user, nil
}

func isEmail(s string) bool {
	return regexp.MustCompile(`^[a-zA-Z0-9._%%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`).MatchString(s)
}

func isPhoneNumber(s string) bool {
	return regexp.MustCompile(`^[0-9]{10,15}$`).MatchString(s)
}
