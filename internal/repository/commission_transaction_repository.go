// internal/repository/commission_transaction_repository.go
package repository

import (
	"context"
	"errors"
	"time"

	"fp-browser/internal/dao/model"
	"gorm.io/gorm"
)

type CommissionTransactionRepository interface {
	CreateTransaction(ctx context.Context, userID int32, amount int64, currency string, transactionType int16, description string, referenceID int32, unlockTime time.Time) error
	GetTransactionsByUserID(ctx context.Context, userID int32, limit, offset int, startTime, endTime *time.Time) ([]*model.CommissionTransaction, error)
	GetWithdrawableTransactions(ctx context.Context, userID int32) ([]*model.CommissionTransaction, error)
	GetTotalCommission(ctx context.Context, userID int32) (int64, error)
	GetTotalWithdrawals(ctx context.Context, userID int32) (int64, error)
	GetAvailableWithdrawableAmount(ctx context.Context, userID int32) (int64, error)
	CheckInvitedUserExists(ctx context.Context, invitedUserID int32) (bool, error)
}

type commissionTransactionRepository struct {
	*Repository
}

func NewCommissionTransactionRepository(r *Repository) CommissionTransactionRepository {
	return &commissionTransactionRepository{Repository: r}
}

func (r *commissionTransactionRepository) CreateTransaction(ctx context.Context, userID int32, amount int64, currency string, transactionType int16, description string, referenceID int32, unlockTime time.Time) error {
	tx := r.query.CommissionTransaction
	originalAmount := amount

	// 提现和退款类型转为负数
	if transactionType == 2 || transactionType == 3 {
		amount = -amount
	}

	// 有引用ID且非退款类型时，设置72小时解锁时间
	if referenceID != 0 && transactionType != 3 {
		unlockTime = time.Now().Add(72 * time.Hour)
	}

	now := time.Now()
	err := tx.WithContext(ctx).Create(&model.CommissionTransaction{
		UserID:          userID,
		Amount:          amount,
		Currency:        currency,
		TransactionType: transactionType,
		ReferenceID:     referenceID,
		Description:     description,
		UnlockTime:      &unlockTime,
		CreatedAt:       &now,
	})

	if err != nil {
		r.logger.Error("创建佣金交易失败",
			"userID", userID,
			"originalAmount", originalAmount,
			"finalAmount", amount,
			"currency", currency,
			"transactionType", transactionType,
			"referenceID", referenceID,
			"description", description,
			"error", err)
		return err
	}

	r.logger.Info("佣金交易创建成功",
		"userID", userID,
		"amount", amount,
		"currency", currency,
		"transactionType", transactionType,
		"referenceID", referenceID,
		"description", description,
		"unlockTime", unlockTime)
	return nil
}

func (r *commissionTransactionRepository) GetTransactionsByUserID(ctx context.Context, userID int32, limit, offset int, startTime, endTime *time.Time) ([]*model.CommissionTransaction, error) {
	q := r.query.CommissionTransaction
	qtx := q.WithContext(ctx).Where(q.UserID.Eq(userID))

	if startTime != nil {
		qtx = qtx.Where(q.CreatedAt.Gte(*startTime))
	}
	if endTime != nil {
		qtx = qtx.Where(q.CreatedAt.Lte(*endTime))
	}

	// 限制查询数量，防止大量数据查询
	if limit > 500 {
		limit = 500
		r.logger.Warn("查询限制已调整为最大值", "requestedLimit", limit, "adjustedLimit", 500)
	}

	transactions, err := qtx.Order(q.CreatedAt.Desc()).Limit(limit).Offset(offset).Find()
	if err != nil {
		r.logger.Error("获取用户佣金交易记录失败",
			"userID", userID,
			"limit", limit,
			"offset", offset,
			"startTime", startTime,
			"endTime", endTime,
			"error", err)
		return nil, err
	}

	r.logger.Debug("获取用户佣金交易记录成功",
		"userID", userID,
		"count", len(transactions),
		"limit", limit,
		"offset", offset)
	return transactions, nil
}

func (r *commissionTransactionRepository) GetWithdrawableTransactions(ctx context.Context, userID int32) ([]*model.CommissionTransaction, error) {
	now := time.Now()
	q := r.query.CommissionTransaction
	transactions, err := q.WithContext(ctx).
		Where(
			q.UserID.Eq(userID),
			q.UnlockTime.Lte(now),
		).
		Find()

	if err != nil {
		r.logger.Error("获取可提现佣金交易失败",
			"userID", userID,
			"currentTime", now,
			"error", err)
		return nil, err
	}

	r.logger.Debug("获取可提现佣金交易成功",
		"userID", userID,
		"count", len(transactions),
		"currentTime", now)
	return transactions, nil
}

func (r *commissionTransactionRepository) GetTotalCommission(ctx context.Context, userID int32) (int64, error) {
	var result int64
	q := r.query.CommissionTransaction
	err := q.WithContext(ctx).
		Where(
			q.UserID.Eq(userID),
			q.TransactionType.Neq(2), // 排除提现类型
		).
		Select(q.Amount.Sum()).
		Scan(&result)

	if err != nil {
		r.logger.Error("获取用户总佣金失败",
			"userID", userID,
			"error", err)
		return 0, err
	}

	r.logger.Debug("获取用户总佣金成功",
		"userID", userID,
		"totalCommission", result)
	return result, nil
}

func (r *commissionTransactionRepository) GetTotalWithdrawals(ctx context.Context, userID int32) (int64, error) {
	var result int64
	q := r.query.CommissionTransaction
	err := q.WithContext(ctx).
		Where(
			q.UserID.Eq(userID),
			q.TransactionType.Eq(2), // 只查询提现类型
		).
		Select(q.Amount.Sum()).
		Scan(&result)

	if err != nil {
		r.logger.Error("获取用户总提现金额失败",
			"userID", userID,
			"error", err)
		return 0, err
	}

	r.logger.Debug("获取用户总提现金额成功",
		"userID", userID,
		"totalWithdrawals", result)
	return result, nil
}

func (r *commissionTransactionRepository) GetAvailableWithdrawableAmount(ctx context.Context, userID int32) (int64, error) {
	var result int64
	now := time.Now()
	q := r.query.CommissionTransaction
	err := q.WithContext(ctx).
		Where(
			q.UserID.Eq(userID),
			q.UnlockTime.Lte(now), // 只查询已解锁的
		).
		Select(q.Amount.Sum()).
		Scan(&result)

	if err != nil {
		r.logger.Error("获取用户可提现金额失败",
			"userID", userID,
			"currentTime", now,
			"error", err)
		return 0, err
	}

	r.logger.Debug("获取用户可提现金额成功",
		"userID", userID,
		"availableAmount", result,
		"currentTime", now)
	return result, nil
}

func (r *commissionTransactionRepository) CheckInvitedUserExists(ctx context.Context, invitedUserID int32) (bool, error) {
	var transaction *model.CommissionTransaction
	q := r.query.CommissionTransaction
	transaction, err := q.WithContext(ctx).
		Where(q.InvitedUserID.Eq(invitedUserID)).
		First()

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			r.logger.Debug("邀请用户记录不存在",
				"invitedUserID", invitedUserID)
			return false, nil
		}
		r.logger.Error("检查邀请用户是否存在失败",
			"invitedUserID", invitedUserID,
			"error", err)
		return false, err
	}

	r.logger.Debug("邀请用户记录存在",
		"invitedUserID", invitedUserID,
		"transactionID", transaction.ID)

	return true, nil
}
