package repository

import (
	"context"
	"fp-browser/internal/dao/model"
	"time"
)

type ForwardRepository interface {
	CreateForward(ctx context.Context, f *model.Forward) error
	GetForwardByID(ctx context.Context, id int32) (*model.Forward, error)
	GetAllForwards(ctx context.Context) ([]*model.Forward, error)
	UpdateForward(ctx context.Context, f *model.Forward) error
	DeleteForward(ctx context.Context, id int32) error
}

type forwardRepository struct {
	*Repository
}

func NewForwardRepository(r *Repository) ForwardRepository {
	return &forwardRepository{Repository: r}
}

func (r *forwardRepository) CreateForward(ctx context.Context, f *model.Forward) error {
	now := time.Now()
	f.CreatedAt = &now
	f.UpdatedAt = &now

	err := r.query.Forward.WithContext(ctx).Create(f)
	if err != nil {
		r.logger.Error("创建转发配置失败",
			"name", f.Name,
			"domain", f.Domain,
			"port", f.Port,
			"error", err)
		return err
	}

	r.logger.Info("转发配置创建成功",
		"id", f.ID,
		"name", f.Name,
		"domain", f.Domain,
		"port", f.Port)
	return nil
}

func (r *forwardRepository) GetForwardByID(ctx context.Context, id int32) (*model.Forward, error) {
	forward, err := r.query.Forward.WithContext(ctx).Where(r.query.Forward.ID.Eq(id)).First()
	if err != nil {
		r.logger.Error("根据ID查询转发配置失败",
			"id", id,
			"error", err)
		return nil, err
	}

	r.logger.Debug("根据ID查询转发配置成功",
		"id", id,
		"name", forward.Name,
		"domain", forward.Domain,
		"port", forward.Port)
	return forward, nil
}

func (r *forwardRepository) GetAllForwards(ctx context.Context) ([]*model.Forward, error) {
	forwards, err := r.query.Forward.WithContext(ctx).Order(r.query.Forward.ID.Desc()).Find()
	if err != nil {
		r.logger.Error("查询所有转发配置失败", "error", err)
		return nil, err
	}

	r.logger.Debug("查询所有转发配置成功", "count", len(forwards))
	return forwards, nil
}

func (r *forwardRepository) UpdateForward(ctx context.Context, f *model.Forward) error {
	f.UpdatedAt = ptrTime(time.Now())

	_, err := r.query.Forward.WithContext(ctx).
		Where(r.query.Forward.ID.Eq(f.ID)).
		Updates(f)

	if err != nil {
		r.logger.Error("更新转发配置失败",
			"id", f.ID,
			"name", f.Name,
			"domain", f.Domain,
			"port", f.Port,
			"error", err)
		return err
	}

	r.logger.Info("转发配置更新成功",
		"id", f.ID,
		"name", f.Name,
		"domain", f.Domain,
		"port", f.Port)
	return nil
}

func (r *forwardRepository) DeleteForward(ctx context.Context, id int32) error {
	_, err := r.query.Forward.WithContext(ctx).Where(r.query.Forward.ID.Eq(id)).Delete()
	if err != nil {
		r.logger.Error("删除转发配置失败",
			"id", id,
			"error", err)
		return err
	}

	r.logger.Info("转发配置删除成功", "id", id)
	return nil
}

// 辅助函数：创建时间指针
func ptrTime(t time.Time) *time.Time {
	return &t
}
