package repository

import (
	"context"
	"time"

	"fp-browser/internal/dao/model"
	"gorm.io/gen"
)

type OperationLogRepository interface {
	CreateLog(ctx context.Context, log *model.OperationLog) error
	GetLogs(
		ctx context.Context,
		teamID, userID, action, category int32,
		target string,
		limit, offset int,
		startTime, endTime *time.Time,
	) ([]*model.OperationLog, int64, error)
}

type operationLogRepository struct {
	*Repository
}

func NewOperationLogRepository(r *Repository) OperationLogRepository {
	return &operationLogRepository{Repository: r}
}

// CreateLog 写入操作日志
func (r *operationLogRepository) CreateLog(ctx context.Context, log *model.OperationLog) error {
	now := time.Now()
	log.CreatedAt = &now

	err := r.query.OperationLog.WithContext(ctx).Create(log)
	if err != nil {
		r.logger.Error("创建操作日志失败",
			"teamID", log.TeamID,
			"userID", log.UserID,
			"action", log.Action,
			"category", log.Category,
			"target", log.Target,
			"error", err)
		return err
	}

	r.logger.Info("操作日志创建成功",
		"id", log.ID,
		"teamID", log.TeamID,
		"userID", log.UserID,
		"action", log.Action,
		"category", log.Category,
		"target", log.Target)
	return nil
}

// GetLogs 多条件组合查询操作日志
func (r *operationLogRepository) GetLogs(
	ctx context.Context,
	teamID, userID, action, category int32,
	target string,
	limit, offset int,
	startTime, endTime *time.Time,
) ([]*model.OperationLog, int64, error) {
	log := r.query.OperationLog
	dao := log.WithContext(ctx)

	conds := make([]gen.Condition, 0)
	var conditions []string

	if teamID != 0 {
		conds = append(conds, log.TeamID.Eq(teamID))
		conditions = append(conditions, "teamID")
	}
	if userID != 0 {
		conds = append(conds, log.UserID.Eq(userID))
		conditions = append(conditions, "userID")
	}
	if action != 0 {
		conds = append(conds, log.Action.Eq(action))
		conditions = append(conditions, "action")
	}
	if category != 0 {
		conds = append(conds, log.Category.Eq(category))
		conditions = append(conditions, "category")
	}
	if target != "" {
		conds = append(conds, log.Target.Eq(target))
		conditions = append(conditions, "target")
	}
	if startTime != nil {
		conds = append(conds, log.CreatedAt.Gte(*startTime))
		conditions = append(conditions, "startTime")
	}
	if endTime != nil {
		conds = append(conds, log.CreatedAt.Lte(*endTime))
		conditions = append(conditions, "endTime")
	}

	query := dao.Where(conds...)

	// 获取总数
	count, err := query.Count()
	if err != nil {
		r.logger.Error("查询操作日志总数失败",
			"teamID", teamID,
			"userID", userID,
			"action", action,
			"category", category,
			"target", target,
			"limit", limit,
			"offset", offset,
			"conditions", conditions,
			"startTime", startTime,
			"endTime", endTime,
			"error", err)
		return nil, 0, err
	}

	// 限制分页
	originalLimit := limit
	if limit > 500 {
		limit = 500
		r.logger.Warn("查询限制已调整为最大值",
			"requestedLimit", originalLimit,
			"adjustedLimit", 500)
	}

	logs, err := query.Order(log.CreatedAt.Desc()).Limit(limit).Offset(offset).Find()
	if err != nil {
		r.logger.Error("查询操作日志失败",
			"teamID", teamID,
			"userID", userID,
			"action", action,
			"category", category,
			"target", target,
			"limit", limit,
			"offset", offset,
			"conditions", conditions,
			"total", count,
			"startTime", startTime,
			"endTime", endTime,
			"error", err)
		return nil, 0, err
	}

	r.logger.Debug("查询操作日志成功",
		"teamID", teamID,
		"userID", userID,
		"action", action,
		"category", category,
		"target", target,
		"limit", limit,
		"offset", offset,
		"originalLimit", originalLimit,
		"conditions", conditions,
		"total", count,
		"returnCount", len(logs),
		"conditionCount", len(conds),
		"startTime", startTime,
		"endTime", endTime)

	return logs, count, nil
}
