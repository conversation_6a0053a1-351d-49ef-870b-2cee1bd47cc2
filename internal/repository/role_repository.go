package repository

import (
	"context"
	"fp-browser/internal/dao/model"
)

type RoleRepository interface {
	CreateRole(ctx context.Context, name string, teamID int32, permissions string, secure *bool) error
	GetRoles(ctx context.Context, teamID, id int32) ([]*model.Role, error)
	GetRoleByID(ctx context.Context, roleID int32) (*model.Role, error)
	UpdateRole(ctx context.Context, id, teamID int32, name, permissions string, secure *bool) error
	DeleteRole(ctx context.Context, teamID, roleID int32) error
}

type roleRepository struct {
	*Repository
}

func NewRoleRepository(r *Repository) RoleRepository {
	return &roleRepository{Repository: r}
}

// Repository Layer
func (r *roleRepository) CreateRole(ctx context.Context, name string, teamID int32, permissions string, secure *bool) error {
	err := r.query.Role.WithContext(ctx).Create(&model.Role{
		Name:        name,
		TeamID:      teamID,
		Permissions: permissions,
		Secure:      secure,
	})

	if err != nil {
		r.logger.Error("创建角色失败",
			"name", name,
			"teamID", teamID,
			"permissionsLength", len(permissions),
			"secure", secure,
			"error", err)
		return err
	}

	r.logger.Info("角色创建成功",
		"name", name,
		"teamID", teamID,
		"permissionsLength", len(permissions),
		"secure", secure)
	return nil
}

func (r *roleRepository) GetRoles(ctx context.Context, teamID, id int32) ([]*model.Role, error) {
	q := r.query.Role
	db := q.WithContext(ctx)

	var conditions []string

	if teamID != 0 {
		db = db.Where(q.TeamID.Eq(teamID))
		conditions = append(conditions, "teamID")
	}
	if id != 0 {
		db = db.Where(q.ID.Eq(id))
		conditions = append(conditions, "id")
	}

	roles, err := db.Find()
	if err != nil {
		r.logger.Error("查询角色列表失败",
			"teamID", teamID,
			"id", id,
			"conditions", conditions,
			"error", err)
		return nil, err
	}

	r.logger.Debug("查询角色列表成功",
		"teamID", teamID,
		"id", id,
		"conditions", conditions,
		"count", len(roles))
	return roles, nil
}

func (r *roleRepository) GetRoleByID(ctx context.Context, roleID int32) (*model.Role, error) {
	role, err := r.query.Role.WithContext(ctx).Where(r.query.Role.ID.Eq(roleID)).First()
	if err != nil {
		r.logger.Error("根据ID查询角色失败",
			"roleID", roleID,
			"error", err)
		return nil, err
	}

	r.logger.Debug("根据ID查询角色成功",
		"roleID", roleID,
		"name", role.Name,
		"teamID", role.TeamID,
		"permissionsLength", len(role.Permissions))
	return role, nil
}

func (r *roleRepository) UpdateRole(ctx context.Context, id, teamID int32, name, permissions string, secure *bool) error {
	_, err := r.query.Role.WithContext(ctx).
		Where(r.query.Role.ID.Eq(id), r.query.Role.TeamID.Eq(teamID)).
		UpdateSimple(
			r.query.Role.Name.Value(name),
			r.query.Role.Permissions.Value(permissions),
			r.query.Role.Secure.Value(*secure),
		)

	if err != nil {
		r.logger.Error("更新角色失败",
			"id", id,
			"teamID", teamID,
			"name", name,
			"permissionsLength", len(permissions),
			"secure", secure,
			"error", err)
		return err
	}

	r.logger.Info("角色更新成功",
		"id", id,
		"teamID", teamID,
		"name", name,
		"permissionsLength", len(permissions),
		"secure", secure)
	return nil
}

func (r *roleRepository) DeleteRole(ctx context.Context, teamID, roleID int32) error {
	db := r.query.Role.WithContext(ctx).Where(r.query.Role.ID.Eq(roleID))

	var conditions []string = []string{"roleID"}

	if teamID != 0 {
		db = db.Where(r.query.Role.TeamID.Eq(teamID))
		conditions = append(conditions, "teamID")
	}

	_, err := db.Delete()
	if err != nil {
		r.logger.Error("删除角色失败",
			"roleID", roleID,
			"teamID", teamID,
			"conditions", conditions,
			"error", err)
		return err
	}

	r.logger.Info("角色删除成功",
		"roleID", roleID,
		"teamID", teamID,
		"conditions", conditions)
	return nil
}
