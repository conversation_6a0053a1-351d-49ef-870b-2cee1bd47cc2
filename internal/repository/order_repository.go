package repository

import (
	"context"
	"time"

	"fp-browser/internal/dao/model"
	"gorm.io/gen"
)

type OrderRepository interface {
	GetOrder(ctx context.Context, orderNumber string, userID int32, teamID int32) (*model.Order, error)
	GetOrderByOrderNumber(ctx context.Context, orderNumber string) (*model.Order, error)
	GetOrders(ctx context.Context, userID, teamID int32, orderType, status, paymentMethod int16, limit, offset int, startTime, endTime *time.Time) ([]*model.Order, error)
	Create(ctx context.Context, order *model.Order) (*model.Order, error)
	Update(ctx context.Context, order *model.Order) error
	UpdateOrderStatus(ctx context.Context, orderNumber string, teamID int32, status int16) (*model.Order, error)
	UpdateOrderURL(ctx context.Context, orderNumber string, teamID int32, url string) (*model.Order, error)
}

type orderRepository struct {
	*Repository
}

func NewOrderRepository(r *Repository) OrderRepository {
	return &orderRepository{Repository: r}
}

// GetOrder 根据订单号 / 用户ID / 团队ID查找
func (r *orderRepository) GetOrder(ctx context.Context, orderNumber string, userID int32, teamID int32) (*model.Order, error) {
	q := r.query.Order.WithContext(ctx)
	conds := make([]gen.Condition, 0)
	var conditions []string

	if orderNumber != "" {
		conds = append(conds, r.query.Order.OrderNumber.Eq(orderNumber))
		conditions = append(conditions, "orderNumber")
	}
	if userID != 0 {
		conds = append(conds, r.query.Order.UserID.Eq(userID))
		conditions = append(conditions, "userID")
	}
	if teamID != 0 {
		conds = append(conds, r.query.Order.TeamID.Eq(teamID))
		conditions = append(conditions, "teamID")
	}

	order, err := q.Where(conds...).First()
	if err != nil {
		r.logger.Error("查询订单失败",
			"orderNumber", orderNumber,
			"userID", userID,
			"teamID", teamID,
			"conditions", conditions,
			"error", err)
		return nil, err
	}

	r.logger.Debug("查询订单成功",
		"orderNumber", orderNumber,
		"userID", userID,
		"teamID", teamID,
		"conditions", conditions,
		"foundOrderID", order.ID,
		"foundOrderNumber", order.OrderNumber)
	return order, nil
}

// GetOrderByOrderNumber 根据订单号查找订单（用于支付回调等场景）
func (r *orderRepository) GetOrderByOrderNumber(ctx context.Context, orderNumber string) (*model.Order, error) {
	q := r.query.Order.WithContext(ctx)

	order, err := q.Where(r.query.Order.OrderNumber.Eq(orderNumber)).First()
	if err != nil {
		r.logger.Error("根据订单号查询订单失败",
			"orderNumber", orderNumber,
			"error", err)
		return nil, err
	}

	r.logger.Debug("根据订单号查询订单成功",
		"orderNumber", orderNumber,
		"orderID", order.ID,
		"userID", order.UserID,
		"teamID", order.TeamID,
		"status", order.Status,
		"amount", order.Amount,
		"realAmount", order.RealAmount)
	return order, nil
}

// GetOrders 分页查询订单
func (r *orderRepository) GetOrders(ctx context.Context, userID, teamID int32, orderType, status, paymentMethod int16, limit, offset int, startTime, endTime *time.Time) ([]*model.Order, error) {
	q := r.query.Order.WithContext(ctx)
	conds := make([]gen.Condition, 0)
	var conditions []string

	if userID != 0 {
		conds = append(conds, r.query.Order.UserID.Eq(userID))
		conditions = append(conditions, "userID")
	}
	if teamID != 0 {
		conds = append(conds, r.query.Order.TeamID.Eq(teamID))
		conditions = append(conditions, "teamID")
	}
	if orderType != 0 {
		conds = append(conds, r.query.Order.OrderType.Eq(orderType))
		conditions = append(conditions, "orderType")
	}
	if status != 0 {
		conds = append(conds, r.query.Order.Status.Eq(status))
		conditions = append(conditions, "status")
	}
	if paymentMethod != 0 {
		conds = append(conds, r.query.Order.PaymentMethod.Eq(paymentMethod))
		conditions = append(conditions, "paymentMethod")
	}
	if startTime != nil {
		conds = append(conds, r.query.Order.UpdatedAt.Gte(*startTime))
		conditions = append(conditions, "startTime")
	}
	if endTime != nil {
		conds = append(conds, r.query.Order.UpdatedAt.Lte(*endTime))
		conditions = append(conditions, "endTime")
	}

	originalLimit := limit
	if limit > 500 {
		limit = 500
		r.logger.Warn("查询限制已调整为最大值",
			"requestedLimit", originalLimit,
			"adjustedLimit", 500)
	}

	orders, err := q.Where(conds...).Order(r.query.Order.UpdatedAt.Desc()).Limit(limit).Offset(offset).Find()
	if err != nil {
		r.logger.Error("分页查询订单失败",
			"userID", userID,
			"teamID", teamID,
			"orderType", orderType,
			"status", status,
			"paymentMethod", paymentMethod,
			"limit", limit,
			"offset", offset,
			"originalLimit", originalLimit,
			"conditions", conditions,
			"startTime", startTime,
			"endTime", endTime,
			"error", err)
		return nil, err
	}

	r.logger.Debug("分页查询订单成功",
		"userID", userID,
		"teamID", teamID,
		"orderType", orderType,
		"status", status,
		"paymentMethod", paymentMethod,
		"limit", limit,
		"offset", offset,
		"originalLimit", originalLimit,
		"conditions", conditions,
		"conditionCount", len(conds),
		"returnCount", len(orders),
		"startTime", startTime,
		"endTime", endTime)

	return orders, nil
}

// Create 创建订单
func (r *orderRepository) Create(ctx context.Context, order *model.Order) (*model.Order, error) {
	err := r.query.Order.WithContext(ctx).Create(order)
	if err != nil {
		r.logger.Error("创建订单失败",
			"orderNumber", order.OrderNumber,
			"userID", order.UserID,
			"teamID", order.TeamID,
			"amount", order.Amount,
			"realAmount", order.RealAmount,
			"currency", order.Currency,
			"orderType", order.OrderType,
			"paymentMethod", order.PaymentMethod,
			"status", order.Status,
			"error", err)
		return nil, err
	}

	r.logger.Info("订单创建成功",
		"id", order.ID,
		"orderNumber", order.OrderNumber,
		"userID", order.UserID,
		"teamID", order.TeamID,
		"amount", order.Amount,
		"realAmount", order.RealAmount,
		"currency", order.Currency,
		"orderType", order.OrderType,
		"paymentMethod", order.PaymentMethod,
		"status", order.Status)
	return order, nil
}

// Update 更新订单
func (r *orderRepository) Update(ctx context.Context, order *model.Order) error {
	_, err := r.query.Order.WithContext(ctx).
		Where(r.query.Order.OrderNumber.Eq(order.OrderNumber)).
		Updates(order)

	if err != nil {
		r.logger.Error("更新订单失败",
			"orderNumber", order.OrderNumber,
			"userID", order.UserID,
			"teamID", order.TeamID,
			"amount", order.Amount,
			"realAmount", order.RealAmount,
			"status", order.Status,
			"paymentMethod", order.PaymentMethod,
			"error", err)
		return err
	}

	r.logger.Info("订单更新成功",
		"orderNumber", order.OrderNumber,
		"userID", order.UserID,
		"teamID", order.TeamID,
		"amount", order.Amount,
		"realAmount", order.RealAmount,
		"status", order.Status,
		"paymentMethod", order.PaymentMethod)
	return nil
}

// UpdateOrderStatus 根据 orderNumber 更新状态，如果 teamID > 0，增加 teamID 过滤
func (r *orderRepository) UpdateOrderStatus(ctx context.Context, orderNumber string, teamID int32, status int16) (*model.Order, error) {
	q := r.query.Order.WithContext(ctx)

	conds := []gen.Condition{
		r.query.Order.OrderNumber.Eq(orderNumber),
	}
	var conditions []string = []string{"orderNumber"}

	if teamID != 0 {
		conds = append(conds, r.query.Order.TeamID.Eq(teamID))
		conditions = append(conditions, "teamID")
	}

	_, err := q.Where(conds...).Update(r.query.Order.Status, status)
	if err != nil {
		r.logger.Error("更新订单状态失败",
			"orderNumber", orderNumber,
			"teamID", teamID,
			"status", status,
			"conditions", conditions,
			"error", err)
		return nil, err
	}

	// 查询更新后的订单
	order, err := q.Where(conds...).First()
	if err != nil {
		r.logger.Error("更新订单状态后查询订单失败",
			"orderNumber", orderNumber,
			"teamID", teamID,
			"status", status,
			"conditions", conditions,
			"error", err)
		return nil, err
	}

	r.logger.Info("订单状态更新成功",
		"orderNumber", orderNumber,
		"teamID", teamID,
		"oldStatus", "unknown",
		"newStatus", status,
		"conditions", conditions,
		"orderID", order.ID)
	return order, nil
}

// UpdateOrderURL 根据 orderNumber 更新支付链接，如果 teamID > 0，增加 teamID 过滤
func (r *orderRepository) UpdateOrderURL(ctx context.Context, orderNumber string, teamID int32, url string) (*model.Order, error) {
	q := r.query.Order.WithContext(ctx)

	conds := []gen.Condition{
		r.query.Order.OrderNumber.Eq(orderNumber),
	}
	var conditions []string = []string{"orderNumber"}

	if teamID != 0 {
		conds = append(conds, r.query.Order.TeamID.Eq(teamID))
		conditions = append(conditions, "teamID")
	}

	_, err := q.Where(conds...).Update(r.query.Order.URL, &url)
	if err != nil {
		r.logger.Error("更新订单支付链接失败",
			"orderNumber", orderNumber,
			"teamID", teamID,
			"url", url,
			"conditions", conditions,
			"error", err)
		return nil, err
	}

	// 查询更新后的订单
	order, err := q.Where(conds...).First()
	if err != nil {
		r.logger.Error("更新订单支付链接后查询订单失败",
			"orderNumber", orderNumber,
			"teamID", teamID,
			"url", url,
			"conditions", conditions,
			"error", err)
		return nil, err
	}

	r.logger.Info("订单支付链接更新成功",
		"orderNumber", orderNumber,
		"teamID", teamID,
		"url", url,
		"conditions", conditions,
		"orderID", order.ID)
	return order, nil
}
