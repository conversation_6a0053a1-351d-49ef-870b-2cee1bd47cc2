package repository

import (
	"context"
	"fp-browser/internal/dao/model"
	"fp-browser/internal/dao/query"
	"gorm.io/gorm/clause"
)

type SystemRepo interface {
	UpsertOne(ctx context.Context, sys *model.System) error
	UpsertMany(ctx context.Context, systems []*model.System) error
	GetByKey(ctx context.Context, key string) (*model.System, error)
	GetAll(ctx context.Context) ([]*model.System, error)
	DeleteByKey(ctx context.Context, key string) error
}

type systemRepo struct {
	*Repository
}

func NewSystemRepo(r *Repository) SystemRepo {
	return &systemRepo{Repository: r}
}

// UpsertOne 插入或更新单个配置项
func (r *systemRepo) UpsertOne(ctx context.Context, sys *model.System) error {
	err := r.query.Transaction(func(tx *query.Query) error {
		return tx.System.WithContext(ctx).
			Clauses(clause.OnConflict{
				Columns:   []clause.Column{{Name: "key"}},
				UpdateAll: true,
			}).Create(sys)
	})

	if err != nil {
		r.logger.Error("单个系统配置Upsert失败",
			"key", sys.Key,
			"valueLength", len(sys.Value),
			"error", err)
		return err
	}

	r.logger.Info("单个系统配置Upsert成功",
		"key", sys.Key,
		"valueLength", len(sys.Value))
	return nil
}

// UpsertMany 插入或更新多个配置项
func (r *systemRepo) UpsertMany(ctx context.Context, systems []*model.System) error {
	if len(systems) == 0 {
		r.logger.Debug("批量Upsert系统配置：空列表，跳过操作")
		return nil
	}

	err := r.query.Transaction(func(tx *query.Query) error {
		return tx.System.WithContext(ctx).
			Clauses(clause.OnConflict{
				Columns:   []clause.Column{{Name: "key"}},
				UpdateAll: true,
			}).CreateInBatches(systems, 100)
	})

	if err != nil {
		r.logger.Error("批量系统配置Upsert失败",
			"count", len(systems),
			"batchSize", 100,
			"error", err)
		return err
	}

	r.logger.Info("批量系统配置Upsert成功",
		"count", len(systems),
		"batchSize", 100)

	// Debug级别记录每个配置项的Key（不记录敏感的Value）
	for _, s := range systems {
		r.logger.Debug("系统配置Upsert详情",
			"key", s.Key,
			"valueLength", len(s.Value))
	}

	return nil
}

// GetByKey 根据 key 获取配置项
func (r *systemRepo) GetByKey(ctx context.Context, key string) (*model.System, error) {
	system, err := r.query.System.WithContext(ctx).Where(r.query.System.Key.Eq(key)).First()
	if err != nil {
		r.logger.Error("根据Key查询系统配置失败",
			"key", key,
			"error", err)
		return nil, err
	}

	r.logger.Debug("根据Key查询系统配置成功",
		"key", key,
		"valueLength", len(system.Value))
	return system, nil
}

// GetAll 获取所有配置项
func (r *systemRepo) GetAll(ctx context.Context) ([]*model.System, error) {
	systems, err := r.query.System.WithContext(ctx).Find()
	if err != nil {
		r.logger.Error("查询所有系统配置失败", "error", err)
		return nil, err
	}

	r.logger.Debug("查询所有系统配置成功", "count", len(systems))

	// Debug级别记录每个配置项的Key统计
	keyList := make([]string, len(systems))
	for i, s := range systems {
		keyList[i] = s.Key
	}
	r.logger.Debug("系统配置Key列表", "keys", keyList)

	return systems, nil
}

// DeleteByKey 删除单个配置项
func (r *systemRepo) DeleteByKey(ctx context.Context, key string) error {
	_, err := r.query.System.WithContext(ctx).Where(r.query.System.Key.Eq(key)).Delete()
	if err != nil {
		r.logger.Error("删除系统配置失败",
			"key", key,
			"error", err)
		return err
	}

	r.logger.Info("系统配置删除成功", "key", key)
	return nil
}
