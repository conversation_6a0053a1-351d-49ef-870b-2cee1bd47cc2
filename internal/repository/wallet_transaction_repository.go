package repository

import (
	"context"
	"fp-browser/internal/dao/model"
	"fp-browser/internal/dao/query"
)

// WalletTransactionRepository defines the interface for wallet transactions
type WalletTransactionRepository interface {
	CreateTransaction(ctx context.Context, userID int32, amount int64, currency string, transactionType int16, description string, referenceID int32) error
	GetTransactionsByUserID(ctx context.Context, userID int32) ([]*model.WalletTransaction, error)
	GetTotalBalance(ctx context.Context, userID int32) (int64, error)
}

type walletTransactionRepository struct {
	*Repository
}

// NewWalletTransactionRepository creates a new repository instance
func NewWalletTransactionRepository(r *Repository) WalletTransactionRepository {
	return &walletTransactionRepository{Repository: r}
}

// CreateTransaction creates a new wallet transaction record
func (r *walletTransactionRepository) CreateTransaction(ctx context.Context, userID int32, amount int64, currency string, transactionType int16, description string, referenceID int32) error {
	err := r.query.Transaction(func(tx *query.Query) error {
		// 插入交易记录
		if err := tx.WalletTransaction.WithContext(ctx).Create(&model.WalletTransaction{
			UserID:          userID,
			Amount:          amount,
			Currency:        currency,
			TransactionType: transactionType,
			ReferenceID:     referenceID,
			Description:     description,
		}); err != nil {
			r.logger.Error("创建钱包交易记录失败",
				"userID", userID,
				"currency", currency,
				"transactionType", transactionType,
				"referenceID", referenceID,
				"description", description,
				"isPositive", amount > 0,
				"isNegative", amount < 0,
				"error", err)
			return err
		}

		// 更新余额
		_, err := tx.UserBalance.WithContext(ctx).
			Where(tx.UserBalance.UserID.Eq(userID)).
			UpdateSimple(tx.UserBalance.WalletAmount.Add(amount))

		if err != nil {
			r.logger.Error("更新用户余额失败",
				"userID", userID,
				"currency", currency,
				"transactionType", transactionType,
				"referenceID", referenceID,
				"isPositive", amount > 0,
				"isNegative", amount < 0,
				"error", err)
			return err
		}

		return nil
	})

	if err != nil {
		r.logger.Error("钱包交易事务失败",
			"userID", userID,
			"currency", currency,
			"transactionType", transactionType,
			"referenceID", referenceID,
			"description", description,
			"isPositive", amount > 0,
			"isNegative", amount < 0,
			"error", err)
		return err
	}

	r.logger.Info("钱包交易创建成功",
		"userID", userID,
		"currency", currency,
		"transactionType", transactionType,
		"referenceID", referenceID,
		"description", description,
		"isPositive", amount > 0,
		"isNegative", amount < 0)
	return nil
}

// GetTransactionsByUserID retrieves wallet transactions by user ID
func (r *walletTransactionRepository) GetTransactionsByUserID(ctx context.Context, userID int32) ([]*model.WalletTransaction, error) {
	transactions, err := r.query.WalletTransaction.WithContext(ctx).
		Where(r.query.WalletTransaction.UserID.Eq(userID)).
		Order(r.query.WalletTransaction.CreatedAt.Desc()).
		Find()

	if err != nil {
		r.logger.Error("获取用户钱包交易记录失败",
			"userID", userID,
			"error", err)
		return nil, err
	}

	// 统计交易类型分布（不记录具体金额）
	var positiveCount, negativeCount, zeroCount int
	var currencyMap = make(map[string]int)
	var typeMap = make(map[int16]int)

	for _, tx := range transactions {
		if tx.Amount > 0 {
			positiveCount++
		} else if tx.Amount < 0 {
			negativeCount++
		} else {
			zeroCount++
		}
		currencyMap[tx.Currency]++
		typeMap[tx.TransactionType]++
	}

	r.logger.Debug("获取用户钱包交易记录成功",
		"userID", userID,
		"totalCount", len(transactions),
		"positiveCount", positiveCount,
		"negativeCount", negativeCount,
		"zeroCount", zeroCount,
		"currencyTypes", len(currencyMap),
		"transactionTypes", len(typeMap))

	return transactions, nil
}

// GetTotalBalance calculates the user's total wallet balance
func (r *walletTransactionRepository) GetTotalBalance(ctx context.Context, userID int32) (int64, error) {
	var total int64
	err := r.query.WalletTransaction.WithContext(ctx).
		Select(r.query.WalletTransaction.Amount.Sum().As("total")).
		Where(r.query.WalletTransaction.UserID.Eq(userID)).
		Scan(&total)

	if err != nil {
		r.logger.Error("计算用户钱包总余额失败",
			"userID", userID,
			"error", err)
		return 0, err
	}

	r.logger.Debug("计算用户钱包总余额成功",
		"userID", userID,
		"hasPositiveBalance", total > 0,
		"hasNegativeBalance", total < 0,
		"isZeroBalance", total == 0)

	return total, nil
}
