package repository

import (
	"context"
	"fmt"
	"fp-browser/internal/dao/model"
	"fp-browser/internal/view_model"
	"strings"
	"time"
)

type GroupRepository interface {
	CreateGroup(ctx context.Context, group *model.Group) error
	GetGroups(ctx context.Context, userID, teamID int32) ([]*view_model.GroupWithUser, error)
	UpdateGroup(ctx context.Context, group *model.Group, teamID int32) error
	DeleteGroup(ctx context.Context, id int32, teamID int32) error
	CheckGroupNameExists(ctx context.Context, name string, teamID int32) (bool, error)
	CheckGroupNameExistsExcludeID(ctx context.Context, name string, teamID int32, excludeID int32) (bool, error)
}

type groupRepository struct {
	*Repository
}

func NewGroupRepository(r *Repository) GroupRepository {
	return &groupRepository{Repository: r}
}

// CheckGroupNameExists 检查分组名称在指定团队中是否已存在
func (r *groupRepository) CheckGroupNameExists(ctx context.Context, name string, teamID int32) (bool, error) {
	count, err := r.query.Group.WithContext(ctx).
		Where(
			r.query.Group.Name.Eq(name),
			r.query.Group.TeamID.Eq(teamID),
		).
		Count()

	if err != nil {
		r.logger.Error("检查分组名称是否存在失败",
			"name", name,
			"teamID", teamID,
			"error", err)
		return false, err
	}

	return count > 0, nil
}

// CheckGroupNameExistsExcludeID 检查分组名称在指定团队中是否已存在（排除指定ID）
func (r *groupRepository) CheckGroupNameExistsExcludeID(ctx context.Context, name string, teamID int32, excludeID int32) (bool, error) {
	count, err := r.query.Group.WithContext(ctx).
		Where(
			r.query.Group.Name.Eq(name),
			r.query.Group.TeamID.Eq(teamID),
			r.query.Group.ID.Neq(excludeID),
		).
		Count()

	if err != nil {
		r.logger.Error("检查分组名称是否存在失败",
			"name", name,
			"teamID", teamID,
			"excludeID", excludeID,
			"error", err)
		return false, err
	}

	return count > 0, nil
}

func (r *groupRepository) CreateGroup(ctx context.Context, group *model.Group) error {
	// 基本验证
	if strings.TrimSpace(group.Name) == "" {
		return fmt.Errorf("分组名称不能为空")
	}

	// 检查分组名称是否已存在
	exists, err := r.CheckGroupNameExists(ctx, group.Name, group.TeamID)
	if err != nil {
		return err
	}
	if exists {
		return fmt.Errorf("分组名称 '%s' 在当前团队中已存在", group.Name)
	}

	now := time.Now()
	group.CreatedAt = &now
	group.UpdatedAt = &now

	err = r.query.Group.WithContext(ctx).Create(group)
	if err != nil {
		r.logger.Error("创建分组失败",
			"name", group.Name,
			"userID", group.UserID,
			"teamID", group.TeamID,
			"error", err)
		return err
	}

	r.logger.Info("分组创建成功",
		"id", group.ID,
		"name", group.Name,
		"userID", group.UserID,
		"teamID", group.TeamID)
	return nil
}

func (r *groupRepository) GetGroups(ctx context.Context, userID, teamID int32) ([]*view_model.GroupWithUser, error) {
	g := r.query.Group
	u := r.query.User

	type groupRaw struct {
		model.Group
		UserName string `gorm:"column:user_name"`
	}

	stmt := g.WithContext(ctx).
		Select(
			g.ALL,
			u.UserName.As("user_name"),
		).
		LeftJoin(u, g.UserID.EqCol(u.ID))

	// 构建查询条件
	var conditions []string
	if userID != 0 {
		stmt = stmt.Where(g.UserID.Eq(userID))
		conditions = append(conditions, "userID")
	}
	if teamID != 0 {
		stmt = stmt.Where(g.TeamID.Eq(teamID))
		conditions = append(conditions, "teamID")
	}

	var raws []*groupRaw

	err := stmt.
		Order(g.CreatedAt.Desc()).
		Scan(&raws)

	if err != nil {
		r.logger.Error("查询分组列表失败",
			"userID", userID,
			"teamID", teamID,
			"conditions", conditions,
			"error", err)
		return nil, err
	}

	var results []*view_model.GroupWithUser
	for _, raw := range raws {
		results = append(results, &view_model.GroupWithUser{
			ID:        raw.ID,
			Name:      raw.Name,
			UserName:  raw.UserName,
			TeamID:    raw.TeamID,
			CreatedAt: raw.CreatedAt,
			UpdatedAt: raw.UpdatedAt,
		})
	}

	r.logger.Debug("查询分组列表成功",
		"userID", userID,
		"teamID", teamID,
		"conditions", conditions,
		"count", len(results))

	return results, nil
}

func (r *groupRepository) UpdateGroup(ctx context.Context, group *model.Group, teamID int32) error {
	// 基本验证
	if strings.TrimSpace(group.Name) == "" {
		return fmt.Errorf("分组名称不能为空")
	}

	// 先查询现有分组信息
	existingGroup, err := r.query.Group.WithContext(ctx).
		Where(
			r.query.Group.ID.Eq(group.ID),
			r.query.Group.TeamID.Eq(teamID),
		).
		First()

	if err != nil {
		r.logger.Error("查询现有分组失败",
			"id", group.ID,
			"teamID", teamID,
			"error", err)
		return fmt.Errorf("分组不存在或无权限访问")
	}

	// 只有在分组名称发生改变时才检查重复性
	if existingGroup.Name != group.Name {
		exists, err := r.CheckGroupNameExistsExcludeID(ctx, group.Name, teamID, group.ID)
		if err != nil {
			return err
		}
		if exists {
			return fmt.Errorf("分组名称 '%s' 在当前团队中已存在", group.Name)
		}
	}

	group.UpdatedAt = ptrTime(time.Now())

	_, err = r.query.Group.WithContext(ctx).
		Where(
			r.query.Group.ID.Eq(group.ID),
			r.query.Group.TeamID.Eq(teamID), // 防越权：必须是同一团队
		).
		Updates(group)

	if err != nil {
		r.logger.Error("更新分组失败",
			"id", group.ID,
			"name", group.Name,
			"teamID", teamID,
			"userID", group.UserID,
			"error", err)
		return err
	}

	r.logger.Info("分组更新成功",
		"id", group.ID,
		"name", group.Name,
		"teamID", teamID,
		"userID", group.UserID)
	return nil
}

func (r *groupRepository) DeleteGroup(ctx context.Context, id int32, teamID int32) error {
	_, err := r.query.Group.WithContext(ctx).
		Where(
			r.query.Group.ID.Eq(id),
			r.query.Group.TeamID.Eq(teamID), // 防越权：必须是同一团队
		).
		Delete()

	if err != nil {
		r.logger.Error("删除分组失败",
			"id", id,
			"teamID", teamID,
			"error", err)
		return err
	}

	r.logger.Info("分组删除成功",
		"id", id,
		"teamID", teamID)
	return nil
}
