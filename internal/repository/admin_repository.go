package repository

import (
	"context"
	"errors"
	"gorm.io/gen"

	"fp-browser/internal/dao/model"
	"fp-browser/internal/dao/query"
	"fp-browser/internal/utils/validate"

	"golang.org/x/crypto/bcrypt"
)

type AdminRepo interface {
	AddAdmin(ctx context.Context, admin *model.Admin) (*model.Admin, error)
	FindByIdentifier(ctx context.Context, identifier string) (*model.Admin, error)
	FindByID(ctx context.Context, id int32) (*model.Admin, error)
	DeleteByID(ctx context.Context, id int32) error
}

type adminRepo struct {
	*Repository
}

func NewAdminRepo(r *Repository) AdminRepo {
	return &adminRepo{Repository: r}
}

// ---- private helper ----
func rowExists(do query.IAdminDo, conds ...gen.Condition) (bool, error) {
	cnt, err := do.Where(conds...).Count()
	return cnt > 0, err
}

// ------------------------

func (r *adminRepo) AddAdmin(ctx context.Context, admin *model.Admin) (*model.Admin, error) {
	if admin.UserName == "" {
		r.logger.Error("添加管理员失败：用户名为空")
		return nil, errors.New("username must be provided")
	}

	hashed, err := bcrypt.GenerateFromPassword([]byte(admin.Password), bcrypt.DefaultCost)
	if err != nil {
		r.logger.Error("密码加密失败", "error", err)
		return nil, err
	}
	admin.Password = string(hashed)

	err = r.query.Transaction(func(tx *query.Query) error {
		a := tx.Admin.WithContext(ctx)

		// 邮箱唯一
		if admin.Email != nil {
			if ok, err := rowExists(a, tx.Admin.Email.Eq(*admin.Email)); err != nil {
				r.logger.Error("检查邮箱唯一性失败", "email", *admin.Email, "error", err)
				return err
			} else if ok {
				r.logger.Warn("邮箱已存在", "email", *admin.Email)
				return errors.New("email already in use")
			}
		}
		// 电话唯一
		if admin.Telephone != nil {
			if ok, err := rowExists(a, tx.Admin.Telephone.Eq(*admin.Telephone)); err != nil {
				r.logger.Error("检查电话唯一性失败", "telephone", *admin.Telephone, "error", err)
				return err
			} else if ok {
				r.logger.Warn("电话号码已存在", "telephone", *admin.Telephone)
				return errors.New("telephone already in use")
			}
		}
		// 用户名唯一
		if ok, err := rowExists(a, tx.Admin.UserName.Eq(admin.UserName)); err != nil {
			r.logger.Error("检查用户名唯一性失败", "username", admin.UserName, "error", err)
			return err
		} else if ok {
			r.logger.Warn("用户名已存在", "username", admin.UserName)
			return errors.New("username already in use")
		}

		// 插入
		return a.Create(admin)
	})

	if err != nil {
		r.logger.Error("添加管理员失败", "username", admin.UserName, "error", err)
		return nil, err
	}

	r.logger.Info("管理员添加成功", "username", admin.UserName, "id", admin.ID)
	return admin, nil
}

func (r *adminRepo) FindByIdentifier(ctx context.Context, idf string) (*model.Admin, error) {
	a := r.query.Admin
	cond := a.UserName.Eq(idf)

	var identifierType string
	switch {
	case validate.IsEmail(idf):
		cond = a.Email.Eq(idf)
		identifierType = "email"
	case validate.IsPhoneNumber(idf):
		cond = a.Telephone.Eq(idf)
		identifierType = "phone"
	default:
		identifierType = "username"
	}

	admin, err := a.WithContext(ctx).Where(cond).First()
	if err != nil {
		r.logger.Error("根据标识符查找管理员失败",
			"identifier", idf,
			"type", identifierType,
			"error", err)
		return nil, err
	}

	r.logger.Debug("根据标识符查找管理员成功",
		"identifier", idf,
		"type", identifierType,
		"admin_id", admin.ID)
	return admin, nil
}

func (r *adminRepo) FindByID(ctx context.Context, id int32) (*model.Admin, error) {
	admin, err := r.query.Admin.WithContext(ctx).Where(r.query.Admin.ID.Eq(id)).First()
	if err != nil {
		r.logger.Error("根据ID查找管理员失败", "id", id, "error", err)
		return nil, err
	}

	r.logger.Debug("根据ID查找管理员成功", "id", id, "username", admin.UserName)
	return admin, nil
}

func (r *adminRepo) DeleteByID(ctx context.Context, id int32) error {
	_, err := r.query.Admin.WithContext(ctx).Where(r.query.Admin.ID.Eq(id)).Delete()
	if err != nil {
		r.logger.Error("删除管理员失败", "id", id, "error", err)
		return err
	}

	r.logger.Info("管理员删除成功", "id", id)
	return nil
}
