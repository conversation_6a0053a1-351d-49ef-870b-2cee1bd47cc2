package repository

import (
	"context"
	"time"

	"gorm.io/gen"

	"fp-browser/internal/dao/model"
	"fp-browser/internal/view_model"
)

type LoginLogRepository interface {
	CreateLog(ctx context.Context, userID, teamID int32, loginIP string, ipLocation string) error
	GetLogs(ctx context.Context, teamID, userID int32, limit, offset int, startTime, endTime *time.Time) ([]*model.LoginLog, int64, error)
	GetLogsWithUserName(ctx context.Context, teamID, userID int32, limit, offset int, startTime, endTime *time.Time) ([]*view_model.LoginLogWithUser, int64, error)
}

type loginLogRepository struct {
	*Repository
}

func NewLoginLogRepository(r *Repository) LoginLogRepository {
	return &loginLogRepository{Repository: r}
}

// CreateLog 记录新的登录日志
func (r *loginLogRepository) CreateLog(ctx context.Context, userID, teamID int32, loginIP string, ipLocation string) error {
	now := time.Now()

	err := r.query.LoginLog.WithContext(ctx).Create(&model.LoginLog{
		UserID:     userID,
		TeamID:     teamID,
		LoginIP:    loginIP,
		IPLocation: ipLocation,
		CreatedAt:  &now,
	})

	if err != nil {
		r.logger.Error("创建登录日志失败",
			"userID", userID,
			"teamID", teamID,
			"loginIP", loginIP,
			"ipLocation", ipLocation,
			"error", err)
		return err
	}

	r.logger.Info("登录日志创建成功",
		"userID", userID,
		"teamID", teamID,
		"loginIP", loginIP,
		"ipLocation", ipLocation)
	return nil
}

// GetLogs 综合条件分页查询
func (r *loginLogRepository) GetLogs(ctx context.Context, teamID, userID int32, limit, offset int, startTime, endTime *time.Time) ([]*model.LoginLog, int64, error) {
	log := r.query.LoginLog
	q := log.WithContext(ctx)

	var conds []gen.Condition
	var conditions []string

	if teamID > 0 {
		conds = append(conds, log.TeamID.Eq(teamID))
		conditions = append(conditions, "teamID")
	}
	if userID > 0 {
		conds = append(conds, log.UserID.Eq(userID))
		conditions = append(conditions, "userID")
	}
	if startTime != nil {
		conds = append(conds, log.CreatedAt.Gte(*startTime))
		conditions = append(conditions, "startTime")
	}
	if endTime != nil {
		conds = append(conds, log.CreatedAt.Lte(*endTime))
		conditions = append(conditions, "endTime")
	}

	query := q.Where(conds...)

	count, err := query.Count()
	if err != nil {
		r.logger.Error("查询登录日志总数失败",
			"teamID", teamID,
			"userID", userID,
			"limit", limit,
			"offset", offset,
			"conditions", conditions,
			"startTime", startTime,
			"endTime", endTime,
			"error", err)
		return nil, 0, err
	}

	if limit > 500 {
		r.logger.Warn("查询限制已调整为最大值", "requestedLimit", limit, "adjustedLimit", 500)
		limit = 500
	}

	logs, err := query.Order(log.CreatedAt.Desc()).Limit(limit).Offset(offset).Find()
	if err != nil {
		r.logger.Error("查询登录日志失败",
			"teamID", teamID,
			"userID", userID,
			"limit", limit,
			"offset", offset,
			"conditions", conditions,
			"total", count,
			"error", err)
		return nil, 0, err
	}

	r.logger.Debug("查询登录日志成功",
		"teamID", teamID,
		"userID", userID,
		"limit", limit,
		"offset", offset,
		"conditions", conditions,
		"total", count,
		"returnCount", len(logs),
		"startTime", startTime,
		"endTime", endTime)

	return logs, count, nil
}

// GetLogsWithUserName 查询登录日志并关联用户名
func (r *loginLogRepository) GetLogsWithUserName(ctx context.Context, teamID, userID int32, limit, offset int, startTime, endTime *time.Time) ([]*view_model.LoginLogWithUser, int64, error) {
	log := r.query.LoginLog
	user := r.query.User
	q := log.WithContext(ctx)

	// 构建查询条件
	var conds []gen.Condition
	var conditions []string

	if teamID > 0 {
		conds = append(conds, log.TeamID.Eq(teamID))
		conditions = append(conditions, "teamID")
	}
	if userID > 0 {
		conds = append(conds, log.UserID.Eq(userID))
		conditions = append(conditions, "userID")
	}
	if startTime != nil {
		conds = append(conds, log.CreatedAt.Gte(*startTime))
		conditions = append(conditions, "startTime")
	}
	if endTime != nil {
		conds = append(conds, log.CreatedAt.Lte(*endTime))
		conditions = append(conditions, "endTime")
	}

	// 获取总数
	count, err := q.Where(conds...).Count()
	if err != nil {
		r.logger.Error("查询带用户名的登录日志总数失败",
			"teamID", teamID,
			"userID", userID,
			"limit", limit,
			"offset", offset,
			"conditions", conditions,
			"startTime", startTime,
			"endTime", endTime,
			"error", err)
		return nil, 0, err
	}

	// 限制分页
	if limit > 500 {
		r.logger.Warn("查询限制已调整为最大值", "requestedLimit", limit, "adjustedLimit", 500)
		limit = 500
	}

	// 定义结果结构
	type joinResult struct {
		model.LoginLog
		UserName string `gorm:"column:user_name"`
	}

	var results []joinResult

	// 执行联表查询
	err = log.WithContext(ctx).
		Select(log.ALL, user.UserName).
		LeftJoin(user, log.UserID.EqCol(user.ID)).
		Where(conds...).
		Order(log.CreatedAt.Desc()).
		Limit(limit).
		Offset(offset).
		Scan(&results)

	if err != nil {
		r.logger.Error("联表查询带用户名的登录日志失败",
			"teamID", teamID,
			"userID", userID,
			"limit", limit,
			"offset", offset,
			"conditions", conditions,
			"total", count,
			"error", err)
		return nil, 0, err
	}

	// 转换为视图模型
	logs := make([]*view_model.LoginLogWithUser, 0, len(results))
	for _, r := range results {
		logs = append(logs, &view_model.LoginLogWithUser{
			ID:         r.ID,
			UserName:   r.UserName,
			LoginIP:    r.LoginIP,
			IPLocation: r.IPLocation,
			CreatedAt:  r.CreatedAt,
		})
	}

	r.logger.Debug("联表查询带用户名的登录日志成功",
		"teamID", teamID,
		"userID", userID,
		"limit", limit,
		"offset", offset,
		"conditions", conditions,
		"total", count,
		"returnCount", len(logs),
		"joinTableCount", len(results),
		"startTime", startTime,
		"endTime", endTime)

	return logs, count, nil
}
