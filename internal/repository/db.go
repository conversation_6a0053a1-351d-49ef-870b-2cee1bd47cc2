// internal/repository/db.go
package repository

import (
	"fmt"
	"fp-browser/internal/dao/model"
	"fp-browser/internal/dao/query"
	logger2 "fp-browser/internal/utils/logger"
	"fp-browser/pkg/log"

	"github.com/spf13/viper"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/plugin/dbresolver"
)

type Repository struct {
	query  *query.Query
	logger *log.Logger
}

func NewRepository(
	logger *log.Logger,
	db *gorm.DB,
) *Repository {
	// 设置默认DB对象（关键步骤）
	query.SetDefault(db)
	// 返回全局查询对象
	return &Repository{
		query:  query.Q,
		logger: logger,
	}
}

// NewDB 创建数据库连接（使用viper配置）
func NewDB(conf *viper.Viper, l *log.Logger) *gorm.DB {
	// Setup custom GORM logger
	gormLogger := logger2.NewGormZLogger(&l.<PERSON><PERSON>, &logger2.GormLoggerConfig{
		Level:         logger2.ParseGormLevel(conf.GetString("database.pool.gorm_log_level")),
		SlowThreshold: conf.GetDuration("database.pool.slow_query_threshold"),
	})

	// Build primary DSN
	masterDSN := buildDSNFromViper(conf, "database.master")
	db, err := gorm.Open(postgres.Open(masterDSN), &gorm.Config{
		Logger: gormLogger,
	})
	if err != nil {
		l.Error("连接主数据库失败", "error", err)
		panic(err)
	}
	l.Info("主数据库连接成功")

	if err := db.AutoMigrate(
		&model.Admin{},
		&model.CommissionTransaction{},
		&model.Coupon{},
		&model.Environment{},
		&model.Forward{},
		&model.Group{},
		&model.LoginLog{},
		&model.Notice{},
		&model.OperationLog{},
		&model.Order{},
		&model.Proxy{},
		&model.Role{},
		&model.SelfHostProxy{},
		&model.StaticProxyPricing{},
		&model.Subscription{},
		&model.TeamIPWhitelist{},
		&model.Team{},
		&model.UserBalance{},
		&model.UserSubscription{},
		&model.User{},
		&model.WalletTransaction{},
	); err != nil {
		l.Error("数据库迁移失败", "error", err)
	}

	l.Info("数据库迁移成功")
	// Setup replicas if configured
	replicaCount := conf.GetInt("database.replica_count")
	if replicaCount > 0 {
		var replicas []gorm.Dialector
		for i := 0; i < replicaCount; i++ {
			replicaDSN := buildDSNFromViper(conf, fmt.Sprintf("database.replicas.%d", i))
			replicas = append(replicas, postgres.Open(replicaDSN))
		}

		if len(replicas) > 0 {
			err = db.Use(dbresolver.Register(dbresolver.Config{
				Replicas: replicas,
				Policy:   dbresolver.RandomPolicy{},
			}))
			if err != nil {
				l.Error("配置读写分离失败", "error", err)
				panic(err)
			}
			l.Info("从库配置成功", "replica_count", len(replicas))
		}
	}

	// Setup connection pool
	sqlDB, err := db.DB()
	if err != nil {
		l.Error("获取底层 sql.DB 失败", "error", err)
		panic(err)
	}
	sqlDB.SetMaxOpenConns(conf.GetInt("database.pool.max_open_conns"))
	sqlDB.SetMaxIdleConns(conf.GetInt("database.pool.max_idle_conns"))
	sqlDB.SetConnMaxLifetime(conf.GetDuration("database.pool.conn_max_lifetime"))

	l.Info("数据库连接池配置完成")
	return db
}

// buildDSNFromViper 从viper配置构建PostgreSQL DSN
func buildDSNFromViper(conf *viper.Viper, prefix string) string {
	return fmt.Sprintf(
		"host=%s port=%d user=%s password=%s dbname=%s sslmode=disable",
		conf.GetString(prefix+".host"),
		conf.GetInt(prefix+".port"),
		conf.GetString(prefix+".user"),
		conf.GetString(prefix+".password"),
		conf.GetString(prefix+".dbname"),
	)
}
