package user

import (
	"crypto/rand"
	"encoding/hex"
	"encoding/json"
	"fmt"
	v1 "fp-browser/api/v1/user"
	"fp-browser/internal/dao/model"
	"fp-browser/internal/service/user"
	"fp-browser/pkg/log"
	"github.com/kataras/iris/v12"
	"github.com/kataras/iris/v12/sessions"
	"time"
)

type OrderHandler struct {
	*Handler
	orderService user.OrderService
	// TODO: 添加其他服务依赖
	// pricingService service.PricingService
	// paymentService service.PaymentService
}

// NewOrderHandler 创建 OrderHandler 实例
func NewOrderHandler(logger *log.Logger, orderService user.OrderService) *OrderHandler {
	return &OrderHandler{
		Handler:      NewHandler(logger),
		orderService: orderService,
	}
}

// GetOrder 获取单个订单信息
// @Summary 获取订单信息
// @Description 根据订单ID或订单号获取单个订单信息
// @Tags 订单模块
// @Accept json
// @Produce json
// @Param order_id query int false "订单ID"
// @Param order_number query string false "订单号"
// @Success 200 {object} v1.GetOrderResponse
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 404 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/orders/detail [get]
func (h *OrderHandler) GetOrder(ctx iris.Context) {
	// 从 session 中获取 userID 和 teamID
	sess := ctx.Values().Get("session").(*sessions.Session)
	if sess == nil {
		h.HandleUnauthorized(ctx, "Session不存在")
		return
	}

	userIDInterface := sess.Get("user_id")
	teamIDInterface := sess.Get("team_id")

	if userIDInterface == nil || teamIDInterface == nil {
		h.HandleUnauthorized(ctx, "Session中缺少用户或团队信息")
		return
	}

	var userID, teamID int32
	switch v := userIDInterface.(type) {
	case int32:
		userID = v
	case uint:
		userID = int32(v)
	case int:
		userID = int32(v)
	default:
		h.HandleUnauthorized(ctx, "用户ID类型无效")
		return
	}

	switch v := teamIDInterface.(type) {
	case int32:
		teamID = v
	case uint:
		teamID = int32(v)
	case int:
		teamID = int32(v)
	default:
		h.HandleUnauthorized(ctx, "团队ID类型无效")
		return
	}

	// 从请求参数获取 orderID 和 orderNumber
	orderID := int32(ctx.URLParamIntDefault("order_id", 0))
	orderNumber := ctx.URLParamDefault("order_number", "")

	// 如果都没有提供，返回错误
	if orderID == 0 && orderNumber == "" {
		h.HandleBadRequest(ctx, nil, "请提供订单ID或订单号")
		return
	}

	// 调用服务层获取订单信息
	order, err := h.orderService.GetOrder(ctx, orderNumber, userID, teamID)
	if err != nil {
		h.logger.Error("获取订单失败",
			"userID", userID,
			"teamID", teamID,
			"orderID", orderID,
			"orderNumber", orderNumber,
			"error", err)
		h.HandleInternalError(ctx, err, "获取订单失败")
		return
	}

	// 检查订单是否存在
	if order == nil {
		ctx.StatusCode(iris.StatusNotFound)
		_ = ctx.JSON(iris.Map{"error": "订单不存在"})
		return
	}

	response := v1.GetOrderResponse{
		Order: order,
	}

	h.logger.Info("订单获取成功",
		"userID", userID,
		"orderID", order.ID,
		"orderNumber", order.OrderNumber)

	h.HandleSuccess(ctx, response)
}

// GetOrders 获取订单列表
// @Summary 获取订单列表
// @Description 获取用户的订单列表，支持分页和筛选
// @Tags 订单模块
// @Accept json
// @Produce json
// @Param order_type query int false "订单类型"
// @Param status query int false "订单状态"
// @Param payment_method query int false "支付方式"
// @Param limit query int false "每页数量，默认100，最大500" minimum(1) maximum(500)
// @Param offset query int false "偏移量，默认0" minimum(0)
// @Param start_time query string false "开始时间，RFC3339格式" format(date-time)
// @Param end_time query string false "结束时间，RFC3339格式" format(date-time)
// @Success 200 {object} v1.GetOrdersResponse
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/orders [get]
func (h *OrderHandler) GetOrders(ctx iris.Context) {
	// 从 session 中获取 userID 和 teamID
	sess := ctx.Values().Get("session").(*sessions.Session)
	if sess == nil {
		h.HandleUnauthorized(ctx, "Session不存在")
		return
	}

	userIDInterface := sess.Get("user_id")
	teamIDInterface := sess.Get("team_id")

	if userIDInterface == nil || teamIDInterface == nil {
		h.HandleUnauthorized(ctx, "Session中缺少用户或团队信息")
		return
	}

	var userID, teamID int32
	switch v := userIDInterface.(type) {
	case int32:
		userID = v
	case uint:
		userID = int32(v)
	case int:
		userID = int32(v)
	default:
		h.HandleUnauthorized(ctx, "用户ID类型无效")
		return
	}

	switch v := teamIDInterface.(type) {
	case int32:
		teamID = v
	case uint:
		teamID = int32(v)
	case int:
		teamID = int32(v)
	default:
		h.HandleUnauthorized(ctx, "团队ID类型无效")
		return
	}

	// 从请求参数获取筛选条件
	orderType := int16(ctx.URLParamIntDefault("order_type", 0))
	status := int16(ctx.URLParamIntDefault("status", 0))
	paymentMethod := int16(ctx.URLParamIntDefault("payment_method", 0))

	limit := ctx.URLParamIntDefault("limit", 100)
	if limit > 500 {
		limit = 500
	}
	if limit <= 0 {
		limit = 100
	}

	offset := ctx.URLParamIntDefault("offset", 0)
	if offset < 0 {
		offset = 0
	}

	// 解析时间范围参数
	var startTime, endTime *time.Time
	if startStr := ctx.URLParam("start_time"); startStr != "" {
		if start, err := time.Parse(time.RFC3339, startStr); err == nil {
			startTime = &start
		} else {
			h.logger.Warn("解析start_time失败", "start_time", startStr, "error", err)
		}
	}
	if endStr := ctx.URLParam("end_time"); endStr != "" {
		if end, err := time.Parse(time.RFC3339, endStr); err == nil {
			endTime = &end
		} else {
			h.logger.Warn("解析end_time失败", "end_time", endStr, "error", err)
		}
	}

	// 调用服务层获取订单列表
	orders, err := h.orderService.GetOrders(ctx, userID, teamID, orderType, status, paymentMethod, limit, offset, startTime, endTime)
	if err != nil {
		h.logger.Error("获取订单列表失败",
			"userID", userID,
			"teamID", teamID,
			"orderType", orderType,
			"status", status,
			"paymentMethod", paymentMethod,
			"error", err)
		h.HandleInternalError(ctx, err, "获取订单列表失败")
		return
	}

	response := v1.GetOrdersResponse{
		Orders: orders,
	}

	h.logger.Info("订单列表获取成功",
		"userID", userID,
		"teamID", teamID,
		"count", len(orders))

	h.HandleSuccess(ctx, response)
}

// CancelOrder 取消订单
// @Summary 取消订单
// @Description 取消指定的订单
// @Tags 订单模块
// @Accept json
// @Produce json
// @Param request body v1.CancelOrderRequest true "取消订单信息"
// @Success 200 {object} v1.CancelOrderResponse
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/orders/cancel [post]
func (h *OrderHandler) CancelOrder(ctx iris.Context) {
	// 从 session 中获取 userID 和 teamID
	sess := ctx.Values().Get("session").(*sessions.Session)
	if sess == nil {
		h.HandleUnauthorized(ctx, "Session不存在")
		return
	}

	userIDInterface := sess.Get("user_id")
	teamIDInterface := sess.Get("team_id")

	if userIDInterface == nil || teamIDInterface == nil {
		h.HandleUnauthorized(ctx, "Session中缺少用户或团队信息")
		return
	}

	var userID, teamID int32
	switch v := userIDInterface.(type) {
	case int32:
		userID = v
	case uint:
		userID = int32(v)
	case int:
		userID = int32(v)
	default:
		h.HandleUnauthorized(ctx, "用户ID类型无效")
		return
	}

	switch v := teamIDInterface.(type) {
	case int32:
		teamID = v
	case uint:
		teamID = int32(v)
	case int:
		teamID = int32(v)
	default:
		h.HandleUnauthorized(ctx, "团队ID类型无效")
		return
	}

	var request v1.CancelOrderRequest
	if err := ctx.ReadJSON(&request); err != nil {
		h.HandleBadRequest(ctx, err, "请求数据无效")
		return
	}

	// 验证至少提供一个值
	if request.OrderID == 0 && request.OrderNumber == "" {
		h.HandleBadRequest(ctx, nil, "请提供订单ID或订单号")
		return
	}

	// 首先验证订单是否存在且属于当前用户
	_, err := h.orderService.GetOrder(ctx, request.OrderNumber, userID, teamID)
	if err != nil {
		h.logger.Error("验证订单失败",
			"userID", userID,
			"teamID", teamID,
			"orderID", request.OrderID,
			"orderNumber", request.OrderNumber,
			"error", err)
		h.HandleInternalError(ctx, err, "验证订单失败")
		return
	}

	// 设置订单状态为取消（4 表示取消状态）
	cancelStatus := int16(4)
	_, err = h.orderService.UpdateOrderStatus(ctx, request.OrderNumber, teamID, cancelStatus)
	if err != nil {
		h.logger.Error("取消订单失败",
			"userID", userID,
			"teamID", teamID,
			"orderNumber", request.OrderNumber,
			"error", err)
		h.HandleInternalError(ctx, err, "取消订单失败")
		return
	}

	response := v1.CancelOrderResponse{
		Message: "订单取消成功",
	}

	h.logger.Info("订单取消成功",
		"userID", userID,
		"teamID", teamID,
		"orderNumber", request.OrderNumber)

	h.HandleSuccess(ctx, response)
}

// CreateOrder 创建订单
// @Summary 创建订单
// @Description 创建新订单并处理支付
// @Tags 订单模块
// @Accept json
// @Produce json
// @Param request body v1.CreateOrderRequest true "订单信息"
// @Success 200 {object} v1.CreateOrderResponse
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/orders [post]
func (h *OrderHandler) CreateOrder(ctx iris.Context) {
	sess := ctx.Values().Get("session").(*sessions.Session)
	if sess == nil {
		h.HandleUnauthorized(ctx, "Session不存在")
		return
	}

	userIDInterface := sess.Get("user_id")
	teamIDInterface := sess.Get("team_id")

	if userIDInterface == nil || teamIDInterface == nil {
		h.HandleUnauthorized(ctx, "Session中缺少用户或团队信息")
		return
	}

	var userID, teamID int32
	switch v := userIDInterface.(type) {
	case int32:
		userID = v
	case uint:
		userID = int32(v)
	case int:
		userID = int32(v)
	default:
		h.HandleUnauthorized(ctx, "用户ID类型无效")
		return
	}

	switch v := teamIDInterface.(type) {
	case int32:
		teamID = v
	case uint:
		teamID = int32(v)
	case int:
		teamID = int32(v)
	default:
		h.HandleUnauthorized(ctx, "团队ID类型无效")
		return
	}

	var request v1.CreateOrderRequest
	if err := ctx.ReadJSON(&request); err != nil {
		h.HandleBadRequest(ctx, err, "请求数据无效")
		return
	}

	// TODO: 根据订单类型计算价格
	// 需要实现 PricingService
	var totalPrice int64
	var err error
	switch request.OrderType {
	case 1: // 类型 1: 计算环境和成员价格
		// totalPrice, err = h.pricingService.CalculateTotalPrice(request.EnvironmentCount, request.MemberCount, request.Duration)
		totalPrice = 10000 // 临时固定价格
		h.logger.Warn("价格计算服务未实现，使用固定价格", "orderType", request.OrderType, "price", totalPrice)
	case 2: // 类型 2: 计算代理价格
		// totalPrice, err = h.pricingService.GetProxyPricing(request.ProxyID, request.Duration, request.ProxyCount)
		totalPrice = 5000 // 临时固定价格
		h.logger.Warn("价格计算服务未实现，使用固定价格", "orderType", request.OrderType, "price", totalPrice)
	case 3: // 类型 3: 充值金额
		totalPrice = request.Recharge
		if request.PaymentMethod == 1 {
			h.HandleBadRequest(ctx, nil, "充值订单不支持此支付方式")
			return
		}
	default:
		h.HandleBadRequest(ctx, nil, "无效的订单类型")
		return
	}

	if err != nil {
		h.logger.Error("计算订单价格失败", "orderType", request.OrderType, "error", err)
		h.HandleInternalError(ctx, err, "计算订单价格失败")
		return
	}

	// 生成订单号
	orderNumber := h.generateOrderNumber()

	// 构造订单详情
	details := h.buildOrderDetails(request, totalPrice)
	detailsJSON, err := json.Marshal(details)
	if err != nil {
		h.logger.Error("订单详情序列化失败", "error", err)
		h.HandleInternalError(ctx, err, "处理订单详情失败")
		return
	}

	// 构造订单标题
	// TODO: 处理支付并获取支付链接
	// 需要实现 PaymentService
	var paymentURL string
	if request.PaymentMethod == 1 {
		paymentURL = "" // 直接设置为空字符串
	} else {
		// paymentURL, err = h.paymentService.ProcessPayment(title, totalPrice, orderNumber, string(detailsJSON), request.PaymentMethod, ctx.Request().Context())
		paymentURL = "https://pay.example.com/pay?order=" + orderNumber // 临时模拟支付链接
		h.logger.Warn("支付服务未实现，使用模拟支付链接", "paymentURL", paymentURL)
	}

	if err != nil {
		h.logger.Error("处理支付失败", "error", err)
		h.HandleInternalError(ctx, err, "处理支付失败")
		return
	}

	// 创建订单对象
	order := &model.Order{
		UserID:        userID,
		TeamID:        teamID,
		OrderNumber:   orderNumber,
		Amount:        totalPrice,
		RealAmount:    totalPrice,
		Currency:      "CNY",
		Status:        1, // Pending
		OrderType:     request.OrderType,
		PaymentMethod: request.PaymentMethod,
	}

	// 处理指针类型字段
	if paymentURL != "" {
		order.URL = &paymentURL
	}

	detailsStr := string(detailsJSON)
	order.OrderContent = &detailsStr

	// 设置过期时间（24小时后过期）
	expiresAt := time.Now().Add(24 * time.Hour)
	order.ExpiresAt = &expiresAt

	// 调用服务层创建订单
	createdOrder, err := h.orderService.Create(ctx, order)
	if err != nil {
		h.logger.Error("创建订单失败",
			"userID", userID,
			"teamID", teamID,
			"orderNumber", orderNumber,
			"error", err)
		h.HandleInternalError(ctx, err, "创建订单失败")
		return
	}

	response := v1.CreateOrderResponse{
		OrderID:     createdOrder.ID,
		OrderNumber: createdOrder.OrderNumber,
		PaymentURL:  paymentURL,
		Amount:      totalPrice,
	}

	h.logger.Info("订单创建成功",
		"userID", userID,
		"teamID", teamID,
		"orderID", createdOrder.ID,
		"orderNumber", createdOrder.OrderNumber,
		"amount", totalPrice)

	h.HandleSuccess(ctx, response)
}

// GetOrderPrice 计算订单价格
// @Summary 计算订单价格
// @Description 根据订单信息计算总价格
// @Tags 订单模块
// @Accept json
// @Produce json
// @Param request body v1.GetOrderPriceRequest true "订单信息"
// @Success 200 {object} v1.GetOrderPriceResponse
// @Failure 400 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/orders/price [post]
func (h *OrderHandler) GetOrderPrice(ctx iris.Context) {
	var request v1.GetOrderPriceRequest
	if err := ctx.ReadJSON(&request); err != nil {
		h.HandleBadRequest(ctx, err, "请求数据无效")
		return
	}

	// TODO: 根据订单类型计算价格
	// 需要实现 PricingService
	var totalPrice int64
	var err error
	switch request.OrderType {
	case 1: // 类型 1: 计算环境和成员价格
		// totalPrice, err = h.pricingService.CalculateTotalPrice(request.EnvironmentCount, request.MemberCount, request.Duration)
		totalPrice = 10000 // 临时固定价格
		h.logger.Warn("价格计算服务未实现，使用固定价格", "orderType", request.OrderType, "price", totalPrice)
	case 2: // 类型 2: 计算代理价格
		// totalPrice, err = h.pricingService.GetProxyPricing(request.ProxyID, request.Duration, request.ProxyCount)
		totalPrice = 5000 // 临时固定价格
		h.logger.Warn("价格计算服务未实现，使用固定价格", "orderType", request.OrderType, "price", totalPrice)
	case 3: // 类型 3: 充值金额
		totalPrice = request.Recharge
	default:
		h.HandleBadRequest(ctx, nil, "无效的订单类型")
		return
	}

	if err != nil {
		h.logger.Error("计算订单价格失败", "orderType", request.OrderType, "error", err)
		h.HandleInternalError(ctx, err, "计算订单价格失败")
		return
	}

	response := v1.GetOrderPriceResponse{
		TotalPrice: totalPrice,
	}

	h.logger.Info("订单价格计算成功", "orderType", request.OrderType, "totalPrice", totalPrice)
	h.HandleSuccess(ctx, response)
}

// generateOrderNumber 生成订单号
func (h *OrderHandler) generateOrderNumber() string {
	// 获取当前时间并格式化为 "YYYYMMDDHHMMSS"
	now := time.Now().Format("20060102150405")

	// 生成随机的 6 位十六进制数字
	randomBytes := make([]byte, 3) // 3 bytes = 6 hex digits
	_, err := rand.Read(randomBytes)
	if err != nil {
		h.logger.Error("生成随机数失败", "error", err)
		// 如果随机数生成失败，使用时间戳作为后缀
		return fmt.Sprintf("SY%s%06d", now, time.Now().Nanosecond()%1000000)
	}
	randomString := hex.EncodeToString(randomBytes)

	// 拼接订单号
	return fmt.Sprintf("SY%s%s", now, randomString)
}

// buildOrderDetails 构造订单详情
func (h *OrderHandler) buildOrderDetails(request v1.CreateOrderRequest, totalPrice int64) []map[string]interface{} {
	var details []map[string]interface{}

	switch request.OrderType {
	case 1: // 套餐购买
		envPrice := totalPrice / int64(request.EnvironmentCount+request.MemberCount) * int64(request.EnvironmentCount)
		memberPrice := totalPrice / int64(request.EnvironmentCount+request.MemberCount) * int64(request.MemberCount)

		details = []map[string]interface{}{
			{
				"goods_name": "环境",
				"quantity":   request.EnvironmentCount,
				"price":      envPrice,
				"goods_id":   "env",
				"duration":   request.Duration,
			},
			{
				"goods_name": "成员",
				"quantity":   request.MemberCount,
				"price":      memberPrice,
				"goods_id":   "member",
				"duration":   request.Duration,
			},
		}
	case 2: // IP 购买
		unitPrice := totalPrice / int64(request.ProxyCount)
		details = []map[string]interface{}{
			{
				"goods_name": "代理",
				"quantity":   request.ProxyCount,
				"price":      unitPrice,
				"goods_id":   "proxy",
				"duration":   request.Duration,
			},
		}
	case 3: // 充值
		details = []map[string]interface{}{
			{
				"goods_name": "余额充值",
				"quantity":   1,
				"price":      totalPrice,
				"goods_id":   "recharge",
			},
		}
	}

	return details
}

// getOrderTitle 获取订单标题
func (h *OrderHandler) getOrderTitle(orderType int16) string {
	switch orderType {
	case 1:
		return "套餐购买"
	case 2:
		return "IP购买"
	case 3:
		return "余额充值"
	default:
		return "订单"
	}
}
