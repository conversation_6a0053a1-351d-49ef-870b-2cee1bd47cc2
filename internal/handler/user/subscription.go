package user

import (
	v1 "fp-browser/api/v1/user"
	"fp-browser/internal/service/user"
	"fp-browser/pkg/log"

	"github.com/kataras/iris/v12"
	"github.com/kataras/iris/v12/sessions"
)

type SubscriptionHandler struct {
	*Handler
	subscriptionService user.SubscriptionService
}

// NewSubscriptionServiceHandler 创建 SubscriptionServiceHandler 实例
func NewSubscriptionHandler(logger *log.Logger, subscriptionService user.SubscriptionService) *SubscriptionHandler {
	return &SubscriptionHandler{
		Handler:             NewHandler(logger),
		subscriptionService: subscriptionService,
	}
}

// GetAllSubscriptions 获取所有订阅套餐
// @Summary 获取所有订阅套餐
// @Description 获取系统中所有可用的订阅套餐列表
// @Tags 订阅套餐模块
// @Accept json
// @Produce json
// @Success 200 {object} v1.GetSubscriptionsResponse
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/subscriptions [get]
func (h *SubscriptionHandler) GetAllSubscriptions(ctx iris.Context) {
	// 从 session 中获取用户 ID（验证用户是否登录）
	sess := ctx.Values().Get("session").(*sessions.Session)
	if sess == nil {
		h.HandleUnauthorized(ctx, "Session不存在")
		return
	}

	userIDInterface := sess.Get("user_id")
	if userIDInterface == nil {
		h.HandleUnauthorized(ctx, "请先登录")
		return
	}

	// 调用服务层获取所有订阅套餐
	subscriptions, err := h.subscriptionService.GetAll(ctx)
	if err != nil {
		h.logger.Error("获取订阅套餐列表失败", "error", err)
		h.HandleInternalError(ctx, err, "获取订阅套餐列表失败")
		return
	}

	response := v1.GetSubscriptionsResponse{
		Subscriptions: subscriptions,
	}

	h.logger.Info("成功获取订阅套餐列表", "count", len(subscriptions))
	h.HandleSuccess(ctx, response)
}

// PurchaseSubscription 购买订阅
// @Summary 购买订阅套餐
// @Description 为指定团队购买订阅套餐，创建相应的订单
// @Tags 订阅套餐模块
// @Accept json
// @Produce json
// @Param request body v1.PurchaseSubscriptionRequest true "购买订阅信息"
// @Success 200 {object} v1.PurchaseSubscriptionResponse
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/subscriptions/purchase [post]
func (h *SubscriptionHandler) PurchaseSubscription(ctx iris.Context) {
	// 从 session 中获取用户 ID
	sess := ctx.Values().Get("session").(*sessions.Session)
	if sess == nil {
		h.HandleUnauthorized(ctx, "Session不存在")
		return
	}

	userIDInterface := sess.Get("user_id")
	if userIDInterface == nil {
		h.HandleUnauthorized(ctx, "请先登录")
		return
	}

	var userID int32
	switch v := userIDInterface.(type) {
	case int32:
		userID = v
	case uint:
		userID = int32(v)
	case int:
		userID = int32(v)
	default:
		h.HandleUnauthorized(ctx, "用户ID类型无效")
		return
	}

	var request v1.PurchaseSubscriptionRequest
	if err := ctx.ReadJSON(&request); err != nil {
		h.HandleBadRequest(ctx, err, "请求数据无效")
		return
	}

	// 调用服务层购买订阅
	order, err := h.subscriptionService.Purchase(
		ctx,
		userID,
		request.TeamID,
		request.SubscriptionID,
		request.Quantity,
	)

	if err != nil {
		h.logger.Error("购买订阅失败",
			"userID", userID,
			"teamID", request.TeamID,
			"subscriptionID", request.SubscriptionID,
			"quantity", request.Quantity,
			"error", err)
		h.HandleInternalError(ctx, err, "购买订阅失败")
		return
	}

	h.logger.Info("订阅购买成功",
		"userID", userID,
		"teamID", request.TeamID,
		"subscriptionID", request.SubscriptionID,
		"quantity", request.Quantity,
		"orderID", order.ID,
		"orderNumber", order.OrderNumber,
		"amount", order.Amount)

	response := v1.PurchaseSubscriptionResponse{
		Message: "订阅购买成功",
		Order:   order,
	}
	h.HandleSuccess(ctx, response)
}
