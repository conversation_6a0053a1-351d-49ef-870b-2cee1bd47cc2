package user

import (
	"fmt"
	v1 "fp-browser/api/v1/user"
	"fp-browser/internal/dao/model"
	"fp-browser/internal/service/shared"
	"fp-browser/internal/service/user"
	_ "fp-browser/internal/view_model"
	"fp-browser/pkg/log"
	"github.com/kataras/iris/v12"
	"github.com/kataras/iris/v12/sessions"
)

type UserHandler struct {
	*Handler
	userService  user.UserService
	redisService shared.RedisService
}

// NewUserHandler 创建 UserHandler 实例
func NewUserHandler(logger *log.Logger, userService user.UserService, redisService shared.RedisService) *UserHandler {
	return &UserHandler{
		Handler:      NewHandler(logger),
		userService:  userService,
		redisService: redisService,
	}
}

// GetProfile 获取用户个人信息
// @Summary 获取用户个人信息
// @Description 获取当前登录用户的完整个人信息，包括团队、角色、钱包余额等
// @Tags 用户模块
// @Accept json
// @Produce json
// @Success 200 {object} v1.GetProfileResponse
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/users/profile [get]
func (h *UserHandler) GetProfile(ctx iris.Context) {
	sess := ctx.Values().Get("session").(*sessions.Session)
	if sess == nil {
		h.HandleUnauthorized(ctx, "Session不存在")
		return
	}

	// 获取用户 ID
	userIDInterface := sess.Get("user_id")
	if userIDInterface == nil {
		h.HandleUnauthorized(ctx, "请先登录")
		return
	}

	var userID int32
	switch v := userIDInterface.(type) {
	case int32:
		userID = v
	case uint:
		userID = int32(v)
	case int:
		userID = int32(v)
	default:
		h.HandleUnauthorized(ctx, "用户ID类型无效")
		return
	}

	if userID == 0 {
		h.HandleUnauthorized(ctx, "请先登录")
		return
	}

	// 查询用户信息
	userInfo, err := h.userService.GetUser(ctx, userID)
	if err != nil {
		h.logger.Error("获取用户信息失败", "userID", userID, "error", err)
		h.HandleInternalError(ctx, err, "用户不存在")
		return
	}

	// 转换为响应格式
	response := v1.GetProfileResponse{
		UserName:                 userInfo.UserName,
		Email:                    userInfo.Email,
		Telephone:                userInfo.Telephone,
		IsActive:                 userInfo.IsActive,
		IsDeleted:                userInfo.IsDeleted,
		IsTwoFactorEnabled:       userInfo.IsTwoFactorEnabled,
		TwoFactorSecret:          userInfo.TwoFactorSecret,
		RealNameType:             userInfo.RealNameType,
		RealName:                 userInfo.RealName,
		IDCardNumber:             userInfo.IDCardNumber,
		CompanyName:              userInfo.CompanyName,
		CompanyUnifiedSocialCode: userInfo.CompanyUnifiedSocialCode,
		InviteUserID:             userInfo.InviteUserID,
		CommissionType:           userInfo.CommissionType,
		CommissionRate:           userInfo.CommissionRate,
		InviteCode:               userInfo.InviteCode,
		WalletAmount:             userInfo.WalletAmount,
		EnvironmentSizeSum:       userInfo.EnvironmentSizeSum,
	}

	// 处理团队信息
	if userInfo.Team != nil {
		response.Team = &v1.ProfileTeam{
			Name:    userInfo.Team.Name,
			OwnerID: userInfo.Team.OwnerID,
		}
	}

	// 处理角色信息
	if userInfo.Role != nil {
		response.Role = &v1.ProfileRole{
			Name:        userInfo.Role.Name,
			Permissions: userInfo.Role.Permissions,
			Secure:      userInfo.Role.Secure,
		}
	}

	// 处理订阅信息
	if userInfo.Subscription != nil {
		response.Subscription = &v1.ProfileSubscription{
			OrderID:      userInfo.Subscription.OrderID,
			StartDate:    userInfo.Subscription.StartDate,
			EndDate:      userInfo.Subscription.EndDate,
			StorageSize:  userInfo.Subscription.StorageSize,
			MembersCount: userInfo.Subscription.MembersCount,
			TotalPrice:   userInfo.Subscription.TotalPrice,
			Status:       userInfo.Subscription.Status,
		}
	}

	h.logger.Info("成功获取用户信息", "userID", userID)
	h.HandleSuccess(ctx, response)
}

// UpdateProfile 更新用户个人信息
// @Summary 更新用户个人信息
// @Description 更新当前登录用户的个人信息，支持邮箱验证码验证（手机号修改功能暂未实现）
// @Tags 用户模块
// @Accept json
// @Produce json
// @Param request body v1.UpdateProfileRequest true "更新信息"
// @Success 200 {object} v1.UpdateProfileResponse
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/users/profile [put]
func (h *UserHandler) UpdateProfile(ctx iris.Context) {
	// 获取 session 中的用户 ID 和团队 ID
	sess := ctx.Values().Get("session").(*sessions.Session)
	if sess == nil {
		h.HandleUnauthorized(ctx, "Session不存在")
		return
	}

	userIDInterface := sess.Get("user_id")
	if userIDInterface == nil {
		h.HandleUnauthorized(ctx, "请先登录")
		return
	}

	var userID int32
	switch v := userIDInterface.(type) {
	case int32:
		userID = v
	case uint:
		userID = int32(v)
	case int:
		userID = int32(v)
	default:
		h.HandleUnauthorized(ctx, "用户ID类型无效")
		return
	}

	teamIDInterface := sess.Get("team_id")
	if teamIDInterface == nil {
		h.HandleUnauthorized(ctx, "Session中缺少团队ID信息")
		return
	}

	var teamID int32
	switch v := teamIDInterface.(type) {
	case int32:
		teamID = v
	case uint:
		teamID = int32(v)
	case int:
		teamID = int32(v)
	default:
		h.HandleUnauthorized(ctx, "团队ID类型无效")
		return
	}

	// 解析请求体中的 JSON 数据
	var updateRequest v1.UpdateProfileRequest
	if err := ctx.ReadJSON(&updateRequest); err != nil {
		h.HandleBadRequest(ctx, err, "请求数据无效")
		return
	}

	// 初始化更新数据（只包含ID和需要更新的字段）
	user := &model.User{
		ID: userID,
	}

	var hasUpdates bool

	// 验证邮箱验证码并更新邮箱
	if updateRequest.Email != "" {
		if updateRequest.EmailCode == "" {
			h.HandleBadRequest(ctx, nil, "邮箱验证码不能为空")
			return
		}

		// 使用真正的验证码验证逻辑
		if err := h.verifyEmailCode(ctx, updateRequest.Email, updateRequest.EmailCode); err != nil {
			h.logger.Warn("邮箱验证码验证失败",
				"userID", userID,
				"email", updateRequest.Email,
				"code", updateRequest.EmailCode,
				"error", err)
			h.HandleBadRequest(ctx, err, err.Error())
			return
		}

		h.logger.Info("邮箱验证码验证成功", "userID", userID, "email", updateRequest.Email)
		user.Email = updateRequest.Email
		hasUpdates = true
	}

	// 手机号修改功能暂未实现，忽略前端传来的手机号相关数据
	if updateRequest.Telephone != "" || updateRequest.TelephoneCode != "" {
		h.logger.Info("手机号修改功能暂未实现，忽略请求", "userID", userID, "requestTelephone", updateRequest.Telephone)
		// 不对 user.Telephone 进行任何修改
	}

	// 更新双因素验证设置
	if updateRequest.Enable2FA != nil {
		user.IsTwoFactorEnabled = updateRequest.Enable2FA
		hasUpdates = true
	}

	// 检查是否有需要更新的字段
	if !hasUpdates {
		h.HandleBadRequest(ctx, nil, "没有需要更新的字段")
		return
	}

	// 调用服务层更新用户信息
	if err := h.userService.UpdateProfile(ctx, []*model.User{user}, teamID); err != nil {
		h.logger.Error("更新用户信息失败", "userID", userID, "teamID", teamID, "error", err)
		h.HandleInternalError(ctx, err, "更新用户信息失败")
		return
	}

	h.logger.Info("用户信息更新成功",
		"userID", userID,
		"teamID", teamID,
		"updatedEmail", updateRequest.Email != "",
		"updated2FA", updateRequest.Enable2FA != nil)

	response := v1.UpdateProfileResponse{
		Message: "用户信息更新成功",
	}
	h.HandleSuccess(ctx, response)
}

// ChangePassword 修改密码
// @Summary 修改密码
// @Description 用户通过提供原密码和新密码来修改账户密码
// @Tags 用户模块
// @Accept json
// @Produce json
// @Param request body v1.ChangePasswordRequest true "修改密码请求"
// @Success 200 {object} v1.ChangePasswordResponse
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/users/change-password [post]
func (h *UserHandler) ChangePassword(ctx iris.Context) {
	// 获取 session 中的用户信息
	sess := ctx.Values().Get("session").(*sessions.Session)
	if sess == nil {
		h.HandleUnauthorized(ctx, "Session不存在")
		return
	}

	userIDInterface := sess.Get("user_id")
	if userIDInterface == nil {
		h.HandleUnauthorized(ctx, "请先登录")
		return
	}

	var userID int32
	switch v := userIDInterface.(type) {
	case int32:
		userID = v
	case uint:
		userID = int32(v)
	case int:
		userID = int32(v)
	default:
		h.HandleUnauthorized(ctx, "用户ID类型无效")
		return
	}

	usernameInterface := sess.Get("username")
	if usernameInterface == nil {
		h.HandleUnauthorized(ctx, "Session中缺少用户名信息")
		return
	}

	username, ok := usernameInterface.(string)
	if !ok {
		h.HandleUnauthorized(ctx, "用户名类型无效")
		return
	}

	teamIDInterface := sess.Get("team_id")
	if teamIDInterface == nil {
		h.HandleUnauthorized(ctx, "Session中缺少团队ID信息")
		return
	}

	var teamID int32
	switch v := teamIDInterface.(type) {
	case int32:
		teamID = v
	case uint:
		teamID = int32(v)
	case int:
		teamID = int32(v)
	default:
		h.HandleUnauthorized(ctx, "团队ID类型无效")
		return
	}

	// 解析请求体
	var request v1.ChangePasswordRequest
	if err := ctx.ReadJSON(&request); err != nil {
		h.HandleBadRequest(ctx, err, "请求数据无效")
		return
	}

	// 验证请求参数
	if request.OldPassword == "" {
		h.HandleBadRequest(ctx, nil, "原密码不能为空")
		return
	}

	if request.NewPassword == "" {
		h.HandleBadRequest(ctx, nil, "新密码不能为空")
		return
	}

	if len(request.NewPassword) < 8 {
		h.HandleBadRequest(ctx, nil, "新密码长度不能少于8位")
		return
	}

	if request.OldPassword == request.NewPassword {
		h.HandleBadRequest(ctx, nil, "新密码不能与原密码相同")
		return
	}

	// 验证原密码
	_, err := h.userService.Authenticate(ctx, username, request.OldPassword)
	if err != nil {
		h.logger.Warn("原密码验证失败",
			"userID", userID,
			"username", username,
			"error", err)
		h.HandleUnauthorized(ctx, "原密码错误")
		return
	}

	h.logger.Info("原密码验证成功", "userID", userID, "username", username)

	// 构造只包含密码的用户对象
	user := &model.User{
		ID:       userID,
		Password: request.NewPassword, // 新密码，会在repository层加密
	}

	// 调用服务层更新密码
	if err := h.userService.UpdateProfile(ctx, []*model.User{user}, teamID); err != nil {
		h.logger.Error("更新密码失败", "userID", userID, "teamID", teamID, "error", err)
		h.HandleInternalError(ctx, err, "密码修改失败")
		return
	}

	h.logger.Info("密码修改成功", "userID", userID, "username", username)

	response := v1.ChangePasswordResponse{
		Message: "密码修改成功",
	}
	h.HandleSuccess(ctx, response)
}

// verifyEmailCode 验证邮箱验证码
func (h *UserHandler) verifyEmailCode(ctx iris.Context, email, code string) error {
	if email == "" || code == "" {
		return fmt.Errorf("邮箱和验证码不能为空")
	}

	// 构建redis key (使用update前缀区分注册和更新场景)
	redisKey := fmt.Sprintf("email_verification:update:%s", email)

	// 从redis获取验证码
	expectedCode, err := h.redisService.Get(ctx, redisKey)
	if err != nil {
		if h.redisService.IsNil(err) {
			return fmt.Errorf("验证码不存在或已过期")
		}
		return fmt.Errorf("验证码查询失败: %w", err)
	}

	// 验证验证码
	if code != expectedCode {
		return fmt.Errorf("验证码错误")
	}

	// 验证通过，删除redis中的验证码（防止重复使用）
	if err := h.redisService.Delete(ctx, redisKey); err != nil {
		h.logger.Warn("删除邮箱验证码失败", "email", email, "key", redisKey, "error", err)
		// 不影响主流程，只记录警告
	}

	return nil
}

// validateTelephoneCode 验证手机验证码（暂未实现）
func (h *UserHandler) validateTelephoneCode(telephone, code string) error {
	// 手机验证码验证功能暂未实现
	return fmt.Errorf("手机验证码验证功能暂未实现")
}
