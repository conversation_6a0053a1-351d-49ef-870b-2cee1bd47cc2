package user

import (
	v1 "fp-browser/api/v1/user"
	"fp-browser/internal/dao/model"
	"fp-browser/internal/service/user"
	"fp-browser/pkg/log"
	"strconv"

	"github.com/kataras/iris/v12"
	"github.com/kataras/iris/v12/sessions"
)

type UserSubscriptionHandler struct {
	*Handler
	subscriptionService user.UserSubscriptionService
}

// NewUserSubscriptionHandler 创建 UserSubscriptionHandler 实例
func NewUserSubscriptionHandler(logger *log.Logger, subscriptionService user.UserSubscriptionService) *UserSubscriptionHandler {
	return &UserSubscriptionHandler{
		Handler:             NewHandler(logger),
		subscriptionService: subscriptionService,
	}
}

// GetSubscriptionByID 根据ID获取订阅信息
// @Summary 根据ID获取订阅信息
// @Description 根据订阅ID获取订阅详细信息
// @Tags 订阅模块
// @Accept json
// @Produce json
// @Param id path int true "订阅ID"
// @Success 200 {object} v1.GetUserSubscriptionResponse
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 404 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/subscriptions/{id} [get]
func (h *UserSubscriptionHandler) GetSubscriptionByID(ctx iris.Context) {
	// 从 session 中获取用户 ID
	sess := ctx.Values().Get("session").(*sessions.Session)
	if sess == nil {
		h.HandleUnauthorized(ctx, "Session不存在")
		return
	}

	userIDInterface := sess.Get("user_id")
	if userIDInterface == nil {
		h.HandleUnauthorized(ctx, "请先登录")
		return
	}

	// 获取路径参数中的订阅ID
	idParam := ctx.Params().Get("id")
	subscriptionID, err := strconv.ParseInt(idParam, 10, 32)
	if err != nil {
		h.HandleBadRequest(ctx, err, "订阅ID格式无效")
		return
	}

	// 调用服务层获取订阅信息
	subscription, err := h.subscriptionService.GetSubscriptionByID(ctx, int32(subscriptionID))
	if err != nil {
		h.logger.Error("获取订阅信息失败", "subscriptionID", subscriptionID, "error", err)
		h.HandleInternalError(ctx, err, "获取订阅信息失败")
		return
	}

	if subscription == nil {
		h.HandleBadRequest(ctx, nil, "订阅不存在")
		return
	}

	response := v1.GetUserSubscriptionResponse{
		Subscription: subscription,
	}

	h.logger.Info("成功获取订阅信息", "subscriptionID", subscriptionID)
	h.HandleSuccess(ctx, response)
}

// GetActiveSubscriptionByTeam 根据团队ID获取活跃订阅
// @Summary 获取团队活跃订阅
// @Description 根据团队ID获取当前活跃的订阅信息
// @Tags 订阅模块
// @Accept json
// @Produce json
// @Param team_id path int true "团队ID"
// @Success 200 {object} v1.GetUserSubscriptionResponse
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 404 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/teams/{team_id}/subscriptions/active [get]
func (h *UserSubscriptionHandler) GetActiveSubscriptionByTeam(ctx iris.Context) {
	// 从 session 中获取用户 ID
	sess := ctx.Values().Get("session").(*sessions.Session)
	if sess == nil {
		h.HandleUnauthorized(ctx, "Session不存在")
		return
	}

	userIDInterface := sess.Get("user_id")
	if userIDInterface == nil {
		h.HandleUnauthorized(ctx, "请先登录")
		return
	}

	// 获取路径参数中的团队ID
	teamIDParam := ctx.Params().Get("team_id")
	teamID, err := strconv.ParseInt(teamIDParam, 10, 32)
	if err != nil {
		h.HandleBadRequest(ctx, err, "团队ID格式无效")
		return
	}

	// 调用服务层获取活跃订阅
	subscription, err := h.subscriptionService.GetActiveSubscriptionByTeam(ctx, int32(teamID))
	if err != nil {
		h.logger.Error("获取团队活跃订阅失败", "teamID", teamID, "error", err)
		h.HandleInternalError(ctx, err, "获取团队活跃订阅失败")
		return
	}

	if subscription == nil {
		h.HandleBadRequest(ctx, nil, "未找到活跃订阅")
		return
	}

	response := v1.GetUserSubscriptionResponse{
		Subscription: subscription,
	}

	h.logger.Info("成功获取团队活跃订阅", "teamID", teamID, "subscriptionID", subscription.ID)
	h.HandleSuccess(ctx, response)
}

// GetAllSubscriptionsByTeam 根据团队ID获取所有订阅
// @Summary 获取团队所有订阅
// @Description 根据团队ID获取所有订阅记录
// @Tags 订阅模块
// @Accept json
// @Produce json
// @Param team_id path int true "团队ID"
// @Success 200 {object} v1.GetUserSubscriptionsResponse
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/teams/{team_id}/subscriptions [get]
func (h *UserSubscriptionHandler) GetAllSubscriptionsByTeam(ctx iris.Context) {
	// 从 session 中获取用户 ID
	sess := ctx.Values().Get("session").(*sessions.Session)
	if sess == nil {
		h.HandleUnauthorized(ctx, "Session不存在")
		return
	}

	userIDInterface := sess.Get("user_id")
	if userIDInterface == nil {
		h.HandleUnauthorized(ctx, "请先登录")
		return
	}

	// 获取路径参数中的团队ID
	teamIDParam := ctx.Params().Get("team_id")
	teamID, err := strconv.ParseInt(teamIDParam, 10, 32)
	if err != nil {
		h.HandleBadRequest(ctx, err, "团队ID格式无效")
		return
	}

	// 调用服务层获取所有订阅
	subscriptions, err := h.subscriptionService.GetAllSubscriptionsByTeam(ctx, int32(teamID))
	if err != nil {
		h.logger.Error("获取团队订阅列表失败", "teamID", teamID, "error", err)
		h.HandleInternalError(ctx, err, "获取团队订阅列表失败")
		return
	}

	response := v1.GetUserSubscriptionsResponse{
		Subscriptions: subscriptions,
	}

	h.logger.Info("成功获取团队订阅列表", "teamID", teamID, "count", len(subscriptions))
	h.HandleSuccess(ctx, response)
}

// CreateSubscription 创建订阅
// @Summary 创建新订阅
// @Description 创建新的订阅记录
// @Tags 订阅模块
// @Accept json
// @Produce json
// @Param request body v1.CreateUserSubscriptionRequest true "订阅信息"
// @Success 200 {object} v1.CreateUserSubscriptionResponse
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/subscriptions [post]
func (h *UserSubscriptionHandler) CreateSubscription(ctx iris.Context) {
	// 从 session 中获取用户 ID
	sess := ctx.Values().Get("session").(*sessions.Session)
	if sess == nil {
		h.HandleUnauthorized(ctx, "Session不存在")
		return
	}

	userIDInterface := sess.Get("user_id")
	if userIDInterface == nil {
		h.HandleUnauthorized(ctx, "请先登录")
		return
	}

	var userID int32
	switch v := userIDInterface.(type) {
	case int32:
		userID = v
	case uint:
		userID = int32(v)
	case int:
		userID = int32(v)
	default:
		h.HandleUnauthorized(ctx, "用户ID类型无效")
		return
	}

	var request v1.CreateUserSubscriptionRequest
	if err := ctx.ReadJSON(&request); err != nil {
		h.HandleBadRequest(ctx, err, "请求数据无效")
		return
	}

	// 构建订阅模型
	subscription := &model.UserSubscription{
		OrderID:      request.OrderID,
		TeamID:       request.TeamID,
		StartDate:    request.StartDate,
		EndDate:      request.EndDate,
		StorageSize:  request.StorageSize,
		MembersCount: request.MembersCount,
		TotalPrice:   request.TotalPrice,
		Status:       request.Status,
	}

	// 调用服务层创建订阅
	err := h.subscriptionService.CreateSubscription(ctx, subscription)
	if err != nil {
		h.logger.Error("创建订阅失败",
			"userID", userID,
			"orderID", request.OrderID,
			"teamID", request.TeamID,
			"storageSize", request.StorageSize,
			"error", err)
		h.HandleInternalError(ctx, err, "创建订阅失败")
		return
	}

	h.logger.Info("订阅创建成功",
		"userID", userID,
		"orderID", request.OrderID,
		"teamID", request.TeamID,
		"storageSize", request.StorageSize,
		"subscriptionID", subscription.ID)

	response := v1.CreateUserSubscriptionResponse{
		Message:        "订阅创建成功",
		SubscriptionID: subscription.ID,
	}
	h.HandleSuccess(ctx, response)
}

// UpdateSubscription 更新订阅
// @Summary 更新订阅信息
// @Description 更新现有订阅的信息
// @Tags 订阅模块
// @Accept json
// @Produce json
// @Param id path int true "订阅ID"
// @Param request body v1.UpdateUserSubscriptionRequest true "订阅更新信息"
// @Success 200 {object} v1.UpdateUserSubscriptionResponse
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 404 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/subscriptions/{id} [put]
func (h *UserSubscriptionHandler) UpdateSubscription(ctx iris.Context) {
	// 从 session 中获取用户 ID
	sess := ctx.Values().Get("session").(*sessions.Session)
	if sess == nil {
		h.HandleUnauthorized(ctx, "Session不存在")
		return
	}

	userIDInterface := sess.Get("user_id")
	if userIDInterface == nil {
		h.HandleUnauthorized(ctx, "请先登录")
		return
	}

	var userID int32
	switch v := userIDInterface.(type) {
	case int32:
		userID = v
	case uint:
		userID = int32(v)
	case int:
		userID = int32(v)
	default:
		h.HandleUnauthorized(ctx, "用户ID类型无效")
		return
	}

	// 获取路径参数中的订阅ID
	idParam := ctx.Params().Get("id")
	subscriptionID, err := strconv.ParseInt(idParam, 10, 32)
	if err != nil {
		h.HandleBadRequest(ctx, err, "订阅ID格式无效")
		return
	}

	var request v1.UpdateUserSubscriptionRequest
	if err := ctx.ReadJSON(&request); err != nil {
		h.HandleBadRequest(ctx, err, "请求数据无效")
		return
	}

	// 先获取现有订阅
	existingSubscription, err := h.subscriptionService.GetSubscriptionByID(ctx, int32(subscriptionID))
	if err != nil {
		h.logger.Error("获取订阅信息失败", "subscriptionID", subscriptionID, "error", err)
		h.HandleInternalError(ctx, err, "获取订阅信息失败")
		return
	}

	if existingSubscription == nil {
		h.HandleBadRequest(ctx, nil, "订阅不存在")
		return
	}

	// 更新订阅信息
	if request.StartDate != nil {
		existingSubscription.StartDate = *request.StartDate
	}
	if request.EndDate != nil {
		existingSubscription.EndDate = *request.EndDate
	}
	if request.StorageSize != nil {
		existingSubscription.StorageSize = *request.StorageSize
	}
	if request.MembersCount != nil {
		existingSubscription.MembersCount = *request.MembersCount
	}
	if request.TotalPrice != nil {
		existingSubscription.TotalPrice = *request.TotalPrice
	}
	if request.Status != nil {
		existingSubscription.Status = *request.Status
	}

	// 调用服务层更新订阅
	err = h.subscriptionService.UpdateSubscription(ctx, existingSubscription)
	if err != nil {
		h.logger.Error("更新订阅失败",
			"userID", userID,
			"subscriptionID", subscriptionID,
			"error", err)
		h.HandleInternalError(ctx, err, "更新订阅失败")
		return
	}

	h.logger.Info("订阅更新成功",
		"userID", userID,
		"subscriptionID", subscriptionID)

	response := v1.UpdateUserSubscriptionResponse{
		Message: "订阅更新成功",
	}
	h.HandleSuccess(ctx, response)
}
