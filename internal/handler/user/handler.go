package user

import (
	"fp-browser/api/v1/user"
	"fp-browser/pkg/log"

	"github.com/kataras/iris/v12"
	"github.com/kataras/iris/v12/sessions"
)

type Handler struct {
	logger *log.Logger
}

func NewHandler(logger *log.Logger) *Handler {
	return &Handler{
		logger: logger,
	}
}

// GetUserIDFromSession 从session中获取用户ID
func (h *Handler) GetUserIDFromSession(ctx iris.Context) (int32, bool) {
	sess := ctx.Values().Get("session").(*sessions.Session)
	if sess == nil {
		return 0, false
	}

	userIDInterface := sess.Get("user_id")
	if userIDInterface == nil {
		return 0, false
	}

	// 统一的类型转换处理
	switch v := userIDInterface.(type) {
	case int32:
		return v, true
	case uint:
		return int32(v), true
	case int:
		return int32(v), true
	default:
		return 0, false
	}
}

// GetTeamIDFromSession 从session中获取团队ID
func (h *Handler) GetTeamIDFromSession(ctx iris.Context) (int32, bool) {
	sess := ctx.Values().Get("session").(*sessions.Session)
	if sess == nil {
		return 0, false
	}

	teamIDInterface := sess.Get("team_id")
	if teamIDInterface == nil {
		return 0, false
	}

	// 统一的类型转换处理
	switch v := teamIDInterface.(type) {
	case int32:
		return v, true
	case uint:
		return int32(v), true
	case int:
		return int32(v), true
	default:
		return 0, false
	}
}

// GetRoleIDFromSession 从session中获取角色ID
func (h *Handler) GetRoleIDFromSession(ctx iris.Context) (int32, bool) {
	sess := ctx.Values().Get("session").(*sessions.Session)
	if sess == nil {
		return 0, false
	}

	roleIDInterface := sess.Get("role_id")
	if roleIDInterface == nil {
		return 0, false
	}

	// 统一的类型转换处理
	switch v := roleIDInterface.(type) {
	case int32:
		return v, true
	case uint:
		return int32(v), true
	case int:
		return int32(v), true
	default:
		return 0, false
	}
}

// GetUsernameFromSession 从session中获取用户名
func (h *Handler) GetUsernameFromSession(ctx iris.Context) (string, bool) {
	sess := ctx.Values().Get("session").(*sessions.Session)
	if sess == nil {
		return "", false
	}

	usernameInterface := sess.Get("username")
	if usernameInterface == nil {
		return "", false
	}

	username, ok := usernameInterface.(string)
	return username, ok
}

// GetIsOwnerFromSession 从session中获取是否为owner
func (h *Handler) GetIsOwnerFromSession(ctx iris.Context) (bool, bool) {
	sess := ctx.Values().Get("session").(*sessions.Session)
	if sess == nil {
		return false, false
	}

	isOwnerInterface := sess.Get("is_owner")
	if isOwnerInterface == nil {
		return false, false
	}

	// 类型转换处理
	switch v := isOwnerInterface.(type) {
	case bool:
		return v, true
	case string:
		return v == "true", true
	default:
		return false, false
	}
}

// GetSessionInfo 从session中获取所有基本信息
func (h *Handler) GetSessionInfo(ctx iris.Context) (userID, teamID, roleID int32, username string, isOwner bool, ok bool) {
	var valid bool

	// 获取用户ID
	userID, valid = h.GetUserIDFromSession(ctx)
	if !valid {
		return 0, 0, 0, "", false, false
	}

	// 获取团队ID
	teamID, valid = h.GetTeamIDFromSession(ctx)
	if !valid {
		return 0, 0, 0, "", false, false
	}

	// 获取角色ID
	roleID, valid = h.GetRoleIDFromSession(ctx)
	if !valid {
		return 0, 0, 0, "", false, false
	}

	// 获取用户名
	username, valid = h.GetUsernameFromSession(ctx)
	if !valid {
		return 0, 0, 0, "", false, false
	}

	// 获取是否为owner
	isOwner, valid = h.GetIsOwnerFromSession(ctx)
	if !valid {
		return 0, 0, 0, "", false, false
	}

	return userID, teamID, roleID, username, isOwner, true
}

// RequireAuth 检查用户是否已登录，返回基本信息
func (h *Handler) RequireAuth(ctx iris.Context) (userID, teamID, roleID int32, username string, isOwner bool, success bool) {
	userID, teamID, roleID, username, isOwner, ok := h.GetSessionInfo(ctx)
	if !ok {
		h.HandleUnauthorized(ctx, "用户未登录")
		return 0, 0, 0, "", false, false
	}
	return userID, teamID, roleID, username, isOwner, true
}

// RequireTeamAccess 检查用户是否有团队访问权限，返回teamID
func (h *Handler) RequireTeamAccess(ctx iris.Context) (int32, bool) {
	teamID, ok := h.GetTeamIDFromSession(ctx)
	if !ok {
		h.HandleUnauthorized(ctx, "缺少团队访问权限")
		return 0, false
	}
	return teamID, true
}

// HandleUnauthorized 处理未授权错误
func (h *Handler) HandleUnauthorized(ctx iris.Context, message string) {
	h.logger.Error(message)
	ctx.StatusCode(iris.StatusUnauthorized)
	_ = ctx.JSON(user.Response{
		Code:    401,
		Message: "未授权访问",
		Data:    nil,
	})
}

// HandleBadRequest 处理请求参数错误
func (h *Handler) HandleBadRequest(ctx iris.Context, err error, message string) {
	h.logger.Error(message, "error", err)
	ctx.StatusCode(iris.StatusBadRequest)
	_ = ctx.JSON(user.Response{
		Code:    400,
		Message: message,
		Data:    nil,
	})
}

// HandleInternalError 处理内部服务器错误
func (h *Handler) HandleInternalError(ctx iris.Context, err error, message string) {
	h.logger.Error(message, "error", err)
	ctx.StatusCode(iris.StatusInternalServerError)
	_ = ctx.JSON(user.Response{
		Code:    500,
		Message: message,
		Data:    nil,
	})
}

// HandleForbidden 处理禁止访问错误
func (h *Handler) HandleForbidden(ctx iris.Context, err error, message string) {
	h.logger.Error(message, "error", err)
	ctx.StatusCode(iris.StatusForbidden)
	_ = ctx.JSON(user.Response{
		Code:    403,
		Message: message,
		Data:    nil,
	})
}

// HandleSuccess 处理成功响应
func (h *Handler) HandleSuccess(ctx iris.Context, data interface{}) {
	_ = ctx.JSON(user.Response{
		Code:    200,
		Message: "success",
		Data:    data,
	})
}
