package user

import (
	v1 "fp-browser/api/v1/user"
	"fp-browser/internal/service/user"
	"fp-browser/pkg/log"
	"strconv"

	"github.com/kataras/iris/v12"
)

type SelfHostProxyHandler struct {
	*Handler
	selfHostProxyService user.SelfHostProxyService
}

// NewSelfHostProxyHandler 创建 SelfHostProxyHandler 实例
func NewSelfHostProxyHandler(logger *log.Logger, selfHostProxyService user.SelfHostProxyService) *SelfHostProxyHandler {
	return &SelfHostProxyHandler{
		Handler:              NewHandler(logger),
		selfHostProxyService: selfHostProxyService,
	}
}

// CreateProxy 创建新的自托管代理
// @Summary 创建自托管代理
// @Description 创建新的自托管代理
// @Tags 自托管代理模块
// @Accept json
// @Produce json
// @Param request body v1.CreateSelfHostProxyRequest true "代理信息"
// @Success 200 {object} v1.Response
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/self-host-proxies [post]
func (h *SelfHostProxyHandler) CreateProxy(ctx iris.Context) {
	// 获取团队ID
	teamID, ok := h.RequireTeamAccess(ctx)
	if !ok {
		return
	}

	// 解析请求数据
	var proxyRequest v1.CreateSelfHostProxyRequest
	if err := ctx.ReadJSON(&proxyRequest); err != nil {
		h.HandleBadRequest(ctx, err, "请求数据无效")
		return
	}

	// 调用服务层处理业务逻辑
	if err := h.selfHostProxyService.CreateProxy(ctx, proxyRequest, teamID); err != nil {
		h.logger.Error("创建代理失败", "teamID", teamID, "name", proxyRequest.Name, "error", err)
		h.HandleInternalError(ctx, err, "创建代理失败")
		return
	}

	h.logger.Info("代理创建成功", "teamID", teamID, "name", proxyRequest.Name)
	_ = ctx.JSON(iris.Map{"message": "代理创建成功"})
}

// CreateProxies 批量创建自托管代理
// @Summary 批量创建自托管代理
// @Description 批量创建多个自托管代理
// @Tags 自托管代理模块
// @Accept json
// @Produce json
// @Param request body v1.CreateSelfHostProxiesRequest true "代理信息列表"
// @Success 200 {object} v1.Response
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/self-host-proxies/batch [post]
func (h *SelfHostProxyHandler) CreateProxies(ctx iris.Context) {
	// 获取团队ID
	teamID, ok := h.RequireTeamAccess(ctx)
	if !ok {
		return
	}

	// 解析请求数据
	var request v1.CreateSelfHostProxiesRequest
	if err := ctx.ReadJSON(&request); err != nil {
		h.HandleBadRequest(ctx, err, "请求数据无效")
		return
	}

	// 调用服务层处理业务逻辑
	if err := h.selfHostProxyService.CreateProxies(ctx, request, teamID); err != nil {
		h.logger.Error("批量创建代理失败", "teamID", teamID, "count", len(request.Proxies), "error", err)
		h.HandleInternalError(ctx, err, "批量创建代理失败")
		return
	}

	h.logger.Info("批量创建代理成功", "teamID", teamID, "count", len(request.Proxies))
	_ = ctx.JSON(iris.Map{"message": "批量创建代理成功"})
}

// UpdateProxy 更新自托管代理
// @Summary 更新自托管代理
// @Description 更新自托管代理信息
// @Tags 自托管代理模块
// @Accept json
// @Produce json
// @Param request body v1.UpdateSelfHostProxyRequest true "代理信息"
// @Success 200 {object} v1.Response
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/self-host-proxies [put]
func (h *SelfHostProxyHandler) UpdateProxy(ctx iris.Context) {
	// 获取团队ID
	teamID, ok := h.RequireTeamAccess(ctx)
	if !ok {
		return
	}

	// 解析请求数据
	var proxyRequest v1.UpdateSelfHostProxyRequest
	if err := ctx.ReadJSON(&proxyRequest); err != nil {
		h.HandleBadRequest(ctx, err, "请求数据无效")
		return
	}

	// 调用服务层处理业务逻辑
	if err := h.selfHostProxyService.UpdateProxy(ctx, proxyRequest, teamID); err != nil {
		h.logger.Error("更新代理失败", "teamID", teamID, "proxyID", proxyRequest.ID, "error", err)
		h.HandleInternalError(ctx, err, "更新代理失败")
		return
	}

	h.logger.Info("代理更新成功", "teamID", teamID, "proxyID", proxyRequest.ID)
	_ = ctx.JSON(iris.Map{"message": "代理更新成功"})
}

// UpdateProxies 批量更新自托管代理
// @Summary 批量更新自托管代理
// @Description 批量更新多个自托管代理
// @Tags 自托管代理模块
// @Accept json
// @Produce json
// @Param request body v1.UpdateSelfHostProxiesRequest true "代理信息列表"
// @Success 200 {object} v1.Response
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/self-host-proxies/batch [put]
func (h *SelfHostProxyHandler) UpdateProxies(ctx iris.Context) {
	// 获取团队ID
	teamID, ok := h.RequireTeamAccess(ctx)
	if !ok {
		return
	}

	// 解析请求数据
	var request v1.UpdateSelfHostProxiesRequest
	if err := ctx.ReadJSON(&request); err != nil {
		h.HandleBadRequest(ctx, err, "请求数据无效")
		return
	}

	// 调用服务层处理业务逻辑
	if err := h.selfHostProxyService.UpdateProxies(ctx, request, teamID); err != nil {
		h.logger.Error("批量更新代理失败", "teamID", teamID, "count", len(request.Proxies), "error", err)
		h.HandleInternalError(ctx, err, "批量更新代理失败")
		return
	}

	h.logger.Info("批量更新代理成功", "teamID", teamID, "count", len(request.Proxies))
	_ = ctx.JSON(iris.Map{"message": "批量更新代理成功"})
}

// DeleteProxy 删除自托管代理
// @Summary 删除自托管代理
// @Description 删除一个或多个自托管代理
// @Tags 自托管代理模块
// @Accept json
// @Produce json
// @Param request body v1.DeleteSelfHostProxyRequest true "代理ID列表"
// @Success 200 {object} v1.Response
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/self-host-proxies [delete]
func (h *SelfHostProxyHandler) DeleteProxy(ctx iris.Context) {
	// 获取团队ID
	teamID, ok := h.RequireTeamAccess(ctx)
	if !ok {
		return
	}

	// 解析请求数据
	var request v1.DeleteSelfHostProxyRequest
	if err := ctx.ReadJSON(&request); err != nil || len(request.IDs) == 0 {
		h.HandleBadRequest(ctx, err, "请求数据无效或缺少有效的代理ID")
		return
	}

	// 调用服务层删除操作
	if err := h.selfHostProxyService.DeleteProxies(ctx, request, teamID); err != nil {
		h.logger.Error("删除代理失败", "teamID", teamID, "proxyIDs", request.IDs, "error", err)
		h.HandleInternalError(ctx, err, "删除代理失败")
		return
	}

	h.logger.Info("代理删除成功", "teamID", teamID, "proxyIDs", request.IDs)
	_ = ctx.JSON(iris.Map{"message": "代理删除成功"})
}

// GetProxyByID 根据 ID 获取自托管代理信息
// @Summary 根据ID获取自托管代理
// @Description 根据ID获取单个自托管代理信息
// @Tags 自托管代理模块
// @Accept json
// @Produce json
// @Param id path int true "代理ID"
// @Success 200 {object} v1.SelfHostProxyResponse
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 404 {object} v1.Response
// @Router /api/v1/self-host-proxies/{id} [get]
func (h *SelfHostProxyHandler) GetProxyByID(ctx iris.Context) {
	// 获取团队ID
	teamID, ok := h.RequireTeamAccess(ctx)
	if !ok {
		h.logger.Error("团队访问权限检查失败")
		return
	}

	// 解析路径参数
	idStr := ctx.Params().Get("id")
	h.logger.Info("获取代理请求参数", "idStr", idStr, "teamID", teamID)

	id, err := strconv.Atoi(idStr)
	if err != nil {
		h.logger.Error("解析代理ID失败", "idStr", idStr, "error", err)
		h.HandleBadRequest(ctx, err, "无效的代理ID")
		return
	}

	h.logger.Info("开始查询代理", "proxyID", id, "teamID", teamID)

	// 调用服务层获取代理
	proxy, err := h.selfHostProxyService.GetProxyByID(ctx, int32(id), teamID)
	if err != nil {
		h.logger.Error("获取代理失败", "proxyID", id, "teamID", teamID, "error", err)
		ctx.StatusCode(iris.StatusNotFound)
		_ = ctx.JSON(iris.Map{"error": "代理不存在"})
		return
	}

	h.logger.Info("代理获取成功", "proxyID", id, "teamID", teamID, "proxy", proxy)
	h.HandleSuccess(ctx, proxy)
}

// GetSelfHostProxies 根据多条件筛选 SelfHostProxy 记录
// @Summary 获取自托管代理列表
// @Description 根据多条件筛选获取自托管代理记录列表，支持分页
// @Tags 自托管代理模块
// @Accept json
// @Produce json
// @Param id query int false "代理ID筛选"
// @Param environment_id query int false "环境ID筛选"
// @Param type query int false "代理类型筛选"
// @Param name query string false "代理名称筛选"
// @Param limit query int false "每页数量，默认100，最大500" minimum(1) maximum(500)
// @Param offset query int false "偏移量，默认0" minimum(0)
// @Success 200 {object} v1.GetSelfHostProxiesResponse
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/self-host-proxies [get]
func (h *SelfHostProxyHandler) GetSelfHostProxies(ctx iris.Context) {
	// 获取团队ID
	teamID, ok := h.RequireTeamAccess(ctx)
	if !ok {
		return
	}

	// 获取并验证查询参数
	id := int32(ctx.URLParamIntDefault("id", 0))
	environmentID := int32(ctx.URLParamIntDefault("environment_id", -1))
	proxyType := int16(ctx.URLParamIntDefault("type", 0))
	name := ctx.URLParamDefault("name", "")

	// 验证和规范化分页参数
	limit := ctx.URLParamIntDefault("limit", 100)
	if limit > 500 {
		limit = 500
	}
	if limit <= 0 {
		limit = 100
	}

	offset := ctx.URLParamIntDefault("offset", 0)
	if offset < 0 {
		offset = 0
	}

	// 构建请求对象
	request := v1.GetSelfHostProxiesRequest{
		ID:            id,
		EnvironmentID: environmentID,
		ProxyType:     proxyType,
		Name:          name,
		Limit:         limit,
		Offset:        offset,
	}

	// 调用服务层获取代理列表
	proxies, total, err := h.selfHostProxyService.GetProxies(ctx, request, teamID)
	if err != nil {
		h.logger.Error("获取代理列表失败",
			"teamID", teamID,
			"request", request,
			"error", err)
		h.HandleInternalError(ctx, err, "获取代理列表失败")
		return
	}

	response := v1.GetSelfHostProxiesResponse{
		Proxies: proxies,
		Total:   total,
	}

	h.logger.Info("代理列表获取成功",
		"teamID", teamID,
		"total", total,
		"limit", request.Limit,
		"offset", request.Offset)

	h.HandleSuccess(ctx, response)
}
