package user

import (
	v1 "fp-browser/api/v1/user"
	"fp-browser/internal/service/user"
	"fp-browser/pkg/log"
	"github.com/kataras/iris/v12"
	"github.com/kataras/iris/v12/sessions"
	"time"
)

type CommissionHandler struct {
	*Handler
	commissionService user.CommissionService
}

// NewCommissionHandler 创建 CommissionHandler 实例
func NewCommissionHandler(logger *log.Logger, commissionService user.CommissionService) *CommissionHandler {
	return &CommissionHandler{
		Handler:           NewHandler(logger),
		commissionService: commissionService,
	}
}

// CreateCommission 创建佣金交易记录
// @Summary 创建佣金交易记录
// @Description 创建新的佣金交易记录
// @Tags 佣金模块
// @Accept json
// @Produce json
// @Param request body v1.CreateCommissionRequest true "佣金交易信息"
// @Success 200 {object} v1.CreateCommissionResponse
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/commissions [post]
func (h *CommissionHandler) CreateCommission(ctx iris.Context) {
	// 从 session 中获取用户 ID
	sess := ctx.Values().Get("session").(*sessions.Session)
	if sess == nil {
		h.HandleUnauthorized(ctx, "Session不存在")
		return
	}

	userIDInterface := sess.Get("user_id")
	if userIDInterface == nil {
		h.HandleUnauthorized(ctx, "请先登录")
		return
	}

	var userID int32
	switch v := userIDInterface.(type) {
	case int32:
		userID = v
	case uint:
		userID = int32(v)
	case int:
		userID = int32(v)
	default:
		h.HandleUnauthorized(ctx, "用户ID类型无效")
		return
	}

	var request v1.CreateCommissionRequest
	if err := ctx.ReadJSON(&request); err != nil {
		h.HandleBadRequest(ctx, err, "请求数据无效")
		return
	}

	// 调用服务层创建佣金交易
	err := h.commissionService.CreateCommission(
		ctx,
		userID,
		request.Amount,
		request.Currency,
		request.TransactionType,
		request.Description,
		request.ReferenceID,
	)

	if err != nil {
		h.logger.Error("创建佣金交易失败",
			"userID", userID,
			"amount", request.Amount,
			"currency", request.Currency,
			"transactionType", request.TransactionType,
			"error", err)
		h.HandleInternalError(ctx, err, "创建交易失败")
		return
	}

	h.logger.Info("佣金交易创建成功",
		"userID", userID,
		"amount", request.Amount,
		"currency", request.Currency,
		"transactionType", request.TransactionType)

	response := v1.CreateCommissionResponse{
		Message: "交易创建成功",
	}
	h.HandleSuccess(ctx, response)
}

// GetTransactions 根据用户ID获取佣金交易记录
// @Summary 获取佣金交易记录
// @Description 根据用户ID获取佣金交易记录，支持分页和时间范围筛选
// @Tags 佣金模块
// @Accept json
// @Produce json
// @Param limit query int false "每页数量，默认100，最大500" minimum(1) maximum(500)
// @Param offset query int false "偏移量，默认0" minimum(0)
// @Param start_time query string false "开始时间，RFC3339格式" format(date-time)
// @Param end_time query string false "结束时间，RFC3339格式" format(date-time)
// @Success 200 {object} v1.GetTransactionsResponse
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/commissions [get]
func (h *CommissionHandler) GetTransactions(ctx iris.Context) {
	// 从 session 中获取用户 ID
	sess := ctx.Values().Get("session").(*sessions.Session)
	if sess == nil {
		h.HandleUnauthorized(ctx, "Session不存在")
		return
	}

	userIDInterface := sess.Get("user_id")
	if userIDInterface == nil {
		h.HandleUnauthorized(ctx, "请先登录")
		return
	}

	var userID int32
	switch v := userIDInterface.(type) {
	case int32:
		userID = v
	case uint:
		userID = int32(v)
	case int:
		userID = int32(v)
	default:
		h.HandleUnauthorized(ctx, "用户ID类型无效")
		return
	}

	// 从请求参数获取分页和时间过滤条件
	limit := ctx.URLParamIntDefault("limit", 100)
	if limit > 500 {
		limit = 500
	}
	if limit <= 0 {
		limit = 100
	}
	offset := ctx.URLParamIntDefault("offset", 0)
	if offset < 0 {
		offset = 0
	}

	// 解析时间范围参数
	var startTime, endTime *time.Time
	if startStr := ctx.URLParam("start_time"); startStr != "" {
		if start, err := time.Parse(time.RFC3339, startStr); err == nil {
			startTime = &start
		} else {
			h.logger.Warn("解析start_time失败", "start_time", startStr, "error", err)
		}
	}
	if endStr := ctx.URLParam("end_time"); endStr != "" {
		if end, err := time.Parse(time.RFC3339, endStr); err == nil {
			endTime = &end
		} else {
			h.logger.Warn("解析end_time失败", "end_time", endStr, "error", err)
		}
	}

	// 调用服务层获取佣金交易记录
	transactions, err := h.commissionService.ListTransactions(ctx, userID, limit, offset, startTime, endTime)
	if err != nil {
		h.logger.Error("获取佣金交易记录失败",
			"userID", userID,
			"limit", limit,
			"offset", offset,
			"error", err)
		h.HandleInternalError(ctx, err, "获取交易记录失败")
		return
	}

	response := v1.GetTransactionsResponse{
		Transactions: transactions,
	}

	h.logger.Info("成功获取佣金交易记录",
		"userID", userID,
		"count", len(transactions),
		"limit", limit,
		"offset", offset)

	h.HandleSuccess(ctx, response)
}

// GetWithdrawalTransactions 获取用户可提现的佣金记录
// @Summary 获取可提现的佣金记录
// @Description 获取用户当前可以提现的佣金记录
// @Tags 佣金模块
// @Accept json
// @Produce json
// @Success 200 {object} v1.GetWithdrawalTransactionsResponse
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/commissions/withdrawable [get]
func (h *CommissionHandler) GetWithdrawalTransactions(ctx iris.Context) {
	// 从 session 中获取用户 ID
	sess := ctx.Values().Get("session").(*sessions.Session)
	if sess == nil {
		h.HandleUnauthorized(ctx, "Session不存在")
		return
	}

	userIDInterface := sess.Get("user_id")
	if userIDInterface == nil {
		h.HandleUnauthorized(ctx, "请先登录")
		return
	}

	var userID int32
	switch v := userIDInterface.(type) {
	case int32:
		userID = v
	case uint:
		userID = int32(v)
	case int:
		userID = int32(v)
	default:
		h.HandleUnauthorized(ctx, "用户ID类型无效")
		return
	}

	// 调用服务层获取可提现交易记录
	transactions, err := h.commissionService.GetWithdrawableTransactions(ctx, userID)
	if err != nil {
		h.logger.Error("获取可提现佣金记录失败", "userID", userID, "error", err)
		h.HandleInternalError(ctx, err, "获取可提现记录失败")
		return
	}

	response := v1.GetWithdrawalTransactionsResponse{
		Transactions: transactions,
	}

	h.logger.Info("成功获取可提现佣金记录", "userID", userID, "count", len(transactions))
	h.HandleSuccess(ctx, response)
}

// GetCommissionSummary 获取用户的佣金摘要信息
// @Summary 获取佣金摘要信息
// @Description 获取用户的佣金摘要信息，包括总佣金、总提现、可提现金额
// @Tags 佣金模块
// @Accept json
// @Produce json
// @Success 200 {object} v1.GetCommissionSummaryResponse
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/commissions/summary [get]
func (h *CommissionHandler) GetCommissionSummary(ctx iris.Context) {
	// 从 session 中获取用户 ID
	sess := ctx.Values().Get("session").(*sessions.Session)
	if sess == nil {
		h.HandleUnauthorized(ctx, "Session不存在")
		return
	}

	userIDInterface := sess.Get("user_id")
	if userIDInterface == nil {
		h.HandleUnauthorized(ctx, "请先登录")
		return
	}

	var userID int32
	switch v := userIDInterface.(type) {
	case int32:
		userID = v
	case uint:
		userID = int32(v)
	case int:
		userID = int32(v)
	default:
		h.HandleUnauthorized(ctx, "用户ID类型无效")
		return
	}

	// 并行获取三个摘要数据
	totalCommission, err := h.commissionService.GetTotalCommission(ctx, userID)
	if err != nil {
		h.logger.Error("获取总佣金失败", "userID", userID, "error", err)
		h.HandleInternalError(ctx, err, "获取佣金摘要失败")
		return
	}

	totalWithdrawals, err := h.commissionService.GetTotalWithdrawals(ctx, userID)
	if err != nil {
		h.logger.Error("获取总提现失败", "userID", userID, "error", err)
		h.HandleInternalError(ctx, err, "获取佣金摘要失败")
		return
	}

	withdrawableAmount, err := h.commissionService.GetWithdrawableAmount(ctx, userID)
	if err != nil {
		h.logger.Error("获取可提现金额失败", "userID", userID, "error", err)
		h.HandleInternalError(ctx, err, "获取佣金摘要失败")
		return
	}

	// 构造摘要响应
	summary := v1.CommissionSummary{
		TotalCommission:    totalCommission,
		TotalWithdrawals:   totalWithdrawals,
		WithdrawableAmount: withdrawableAmount,
	}

	response := v1.GetCommissionSummaryResponse{
		Summary: summary,
	}

	h.logger.Info("成功获取佣金摘要信息",
		"userID", userID,
		"totalCommission", totalCommission,
		"totalWithdrawals", totalWithdrawals,
		"withdrawableAmount", withdrawableAmount)

	h.HandleSuccess(ctx, response)
}
