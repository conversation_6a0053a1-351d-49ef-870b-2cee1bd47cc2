package user

import (
	v1 "fp-browser/api/v1/user"
	"fp-browser/internal/service/user"
	"fp-browser/pkg/log"
	"github.com/kataras/iris/v12"
	"github.com/kataras/iris/v12/sessions"
)

type WalletHandler struct {
	*Handler
	walletService user.WalletService
}

// NewWalletHandler 创建 WalletHandler 实例
func NewWalletHandler(logger *log.Logger, walletService user.WalletService) *WalletHandler {
	return &WalletHandler{
		Handler:       NewHandler(logger),
		walletService: walletService,
	}
}

// CreateTransaction 创建钱包交易记录
// @Summary 创建钱包交易记录
// @Description 创建新的钱包交易记录
// @Tags 钱包模块
// @Accept json
// @Produce json
// @Param request body v1.CreateWalletTransactionRequest true "钱包交易信息"
// @Success 200 {object} v1.CreateWalletTransactionResponse
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/wallet/transactions [post]
func (h *WalletHandler) CreateTransaction(ctx iris.Context) {
	// 从 session 中获取用户 ID
	sess := ctx.Values().Get("session").(*sessions.Session)
	if sess == nil {
		h.HandleUnauthorized(ctx, "Session不存在")
		return
	}

	userIDInterface := sess.Get("user_id")
	if userIDInterface == nil {
		h.HandleUnauthorized(ctx, "请先登录")
		return
	}

	var userID int32
	switch v := userIDInterface.(type) {
	case int32:
		userID = v
	case uint:
		userID = int32(v)
	case int:
		userID = int32(v)
	default:
		h.HandleUnauthorized(ctx, "用户ID类型无效")
		return
	}

	var request v1.CreateWalletTransactionRequest
	if err := ctx.ReadJSON(&request); err != nil {
		h.HandleBadRequest(ctx, err, "请求数据无效")
		return
	}

	// 调用服务层创建钱包交易
	err := h.walletService.CreateTransaction(
		ctx,
		userID,
		request.Amount,
		request.Currency,
		request.TransactionType,
		request.Description,
		request.ReferenceID,
	)

	if err != nil {
		h.logger.Error("创建钱包交易失败",
			"userID", userID,
			"amount", request.Amount,
			"currency", request.Currency,
			"transactionType", request.TransactionType,
			"error", err)
		h.HandleInternalError(ctx, err, "创建交易失败")
		return
	}

	h.logger.Info("钱包交易创建成功",
		"userID", userID,
		"amount", request.Amount,
		"currency", request.Currency,
		"transactionType", request.TransactionType)

	response := v1.CreateWalletTransactionResponse{
		Message: "交易创建成功",
	}
	h.HandleSuccess(ctx, response)
}

// GetTransactions 根据用户ID获取钱包交易记录
// @Summary 获取钱包交易记录
// @Description 获取当前用户的钱包交易记录列表
// @Tags 钱包模块
// @Accept json
// @Produce json
// @Success 200 {object} v1.GetWalletTransactionsResponse
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/wallet/transactions [get]
func (h *WalletHandler) GetTransactions(ctx iris.Context) {
	// 从 session 中获取用户 ID
	sess := ctx.Values().Get("session").(*sessions.Session)
	if sess == nil {
		h.HandleUnauthorized(ctx, "Session不存在")
		return
	}

	userIDInterface := sess.Get("user_id")
	if userIDInterface == nil {
		h.HandleUnauthorized(ctx, "请先登录")
		return
	}

	var userID int32
	switch v := userIDInterface.(type) {
	case int32:
		userID = v
	case uint:
		userID = int32(v)
	case int:
		userID = int32(v)
	default:
		h.HandleUnauthorized(ctx, "用户ID类型无效")
		return
	}

	// 调用服务层获取钱包交易记录
	transactions, err := h.walletService.GetTransactionsByUserID(ctx, userID)
	if err != nil {
		h.logger.Error("获取钱包交易记录失败", "userID", userID, "error", err)
		h.HandleInternalError(ctx, err, "获取交易记录失败")
		return
	}

	response := v1.GetWalletTransactionsResponse{
		Transactions: transactions,
	}

	h.logger.Info("成功获取钱包交易记录", "userID", userID, "count", len(transactions))
	h.HandleSuccess(ctx, response)
}

// GetBalance 获取用户钱包余额
// @Summary 获取钱包余额
// @Description 获取当前用户的钱包总余额
// @Tags 钱包模块
// @Accept json
// @Produce json
// @Success 200 {object} v1.GetWalletBalanceResponse
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/wallet/balance [get]
func (h *WalletHandler) GetBalance(ctx iris.Context) {
	// 从 session 中获取用户 ID
	sess := ctx.Values().Get("session").(*sessions.Session)
	if sess == nil {
		h.HandleUnauthorized(ctx, "Session不存在")
		return
	}

	userIDInterface := sess.Get("user_id")
	if userIDInterface == nil {
		h.HandleUnauthorized(ctx, "请先登录")
		return
	}

	var userID int32
	switch v := userIDInterface.(type) {
	case int32:
		userID = v
	case uint:
		userID = int32(v)
	case int:
		userID = int32(v)
	default:
		h.HandleUnauthorized(ctx, "用户ID类型无效")
		return
	}

	// 调用服务层获取钱包余额
	totalBalance, err := h.walletService.GetTotalBalance(ctx, userID)
	if err != nil {
		h.logger.Error("获取钱包余额失败", "userID", userID, "error", err)
		h.HandleInternalError(ctx, err, "获取余额失败")
		return
	}

	response := v1.GetWalletBalanceResponse{
		TotalBalance: totalBalance,
	}

	h.logger.Info("成功获取钱包余额", "userID", userID, "balance", totalBalance)
	h.HandleSuccess(ctx, response)
}
