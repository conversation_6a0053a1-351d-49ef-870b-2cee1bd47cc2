package user

import (
	"encoding/json"
	v1 "fp-browser/api/v1/user"
	"fp-browser/internal/service/user"
	"fp-browser/pkg/log"
	"github.com/kataras/iris/v12"
	"github.com/kataras/iris/v12/sessions"
)

type RoleHandler struct {
	*Handler
	roleService user.RoleService
}

// NewRoleHandler 创建 RoleHandler 实例
func NewRoleHandler(logger *log.Logger, roleService user.RoleService) *RoleHandler {
	return &RoleHandler{
		Handler:     NewHandler(logger),
		roleService: roleService,
	}
}

// CreateRole 创建新的角色
// @Summary 创建新的角色
// @Description 创建一个新的角色，包含名称、权限信息和安全登录设置
// @Tags 角色模块
// @Accept json
// @Produce json
// @Param request body v1.CreateRoleRequest true "角色信息"
// @Success 200 {object} v1.Response
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/roles [post]
func (h *RoleHandler) CreateRole(ctx iris.Context) {
	// 从 session 获取 teamID
	sess := ctx.Values().Get("session").(*sessions.Session)
	if sess == nil {
		h.HandleUnauthorized(ctx, "Session不存在")
		return
	}

	teamIDInterface := sess.Get("team_id")
	if teamIDInterface == nil {
		h.HandleUnauthorized(ctx, "Session中缺少团队ID信息")
		return
	}

	// 类型转换处理
	var teamID int32
	switch v := teamIDInterface.(type) {
	case int32:
		teamID = v
	case uint:
		teamID = int32(v)
	case int:
		teamID = int32(v)
	default:
		h.HandleUnauthorized(ctx, "团队ID类型无效")
		return
	}

	// 解析请求体中的角色数据
	var roleRequest v1.CreateRoleRequest
	if err := ctx.ReadJSON(&roleRequest); err != nil {
		h.HandleBadRequest(ctx, err, "请求数据无效")
		return
	}

	// 验证权限字段是否为有效 JSON 格式
	if !h.isValidJSON(roleRequest.Permissions) {
		h.logger.Error("权限数据不是有效的 JSON 格式")
		h.HandleBadRequest(ctx, nil, "权限数据不是有效的 JSON 格式")
		return
	}

	// 调用服务层创建角色
	err := h.roleService.CreateRole(ctx, roleRequest.Name, teamID, roleRequest.Permissions, roleRequest.Secure)
	if err != nil {
		h.logger.Error("创建角色失败", "teamID", teamID, "roleName", roleRequest.Name, "error", err)
		h.HandleInternalError(ctx, err, "角色创建失败")
		return
	}

	// 成功响应
	h.logger.Info("角色创建成功", "teamID", teamID, "roleName", roleRequest.Name)
	h.HandleSuccess(ctx, iris.Map{"message": "角色创建成功"})
}

// GetRoles 根据团队ID和请求参数中的角色ID获取角色记录
// @Summary 获取角色列表
// @Description 根据团队ID和可选的角色ID获取角色记录
// @Tags 角色模块
// @Accept json
// @Produce json
// @Param id query int false "角色ID，为0时获取所有角色"
// @Success 200 {object} v1.GetRolesResponse
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/roles [get]
func (h *RoleHandler) GetRoles(ctx iris.Context) {
	// 从 session 获取 teamID
	sess := ctx.Values().Get("session").(*sessions.Session)
	if sess == nil {
		h.HandleUnauthorized(ctx, "Session不存在")
		return
	}

	teamIDInterface := sess.Get("team_id")
	if teamIDInterface == nil {
		h.HandleUnauthorized(ctx, "Session中缺少团队ID信息")
		return
	}

	// 类型转换处理
	var teamID int32
	switch v := teamIDInterface.(type) {
	case int32:
		teamID = v
	case uint:
		teamID = int32(v)
	case int:
		teamID = int32(v)
	default:
		h.HandleUnauthorized(ctx, "团队ID类型无效")
		return
	}

	// 从请求参数获取角色ID，默认值为 0
	id := int32(ctx.URLParamIntDefault("id", 0))

	// 获取角色记录
	roles, err := h.roleService.GetRoles(ctx, teamID, id)
	if err != nil {
		h.logger.Error("获取角色记录失败", "teamID", teamID, "roleID", id, "error", err)
		h.HandleInternalError(ctx, err, "角色记录获取失败")
		return
	}

	// 返回成功响应
	h.logger.Info("成功获取角色记录", "teamID", teamID, "roleID", id, "count", len(roles))
	h.HandleSuccess(ctx, roles)
}

// UpdateRole 更新角色记录
// @Summary 更新角色信息
// @Description 更新指定角色的名称、权限信息和安全登录设置
// @Tags 角色模块
// @Accept json
// @Produce json
// @Param request body v1.UpdateRoleRequest true "更新角色信息"
// @Success 200 {object} v1.Response
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/roles [put]
func (h *RoleHandler) UpdateRole(ctx iris.Context) {
	// 从 session 中获取 teamID
	sess := ctx.Values().Get("session").(*sessions.Session)
	if sess == nil {
		h.HandleUnauthorized(ctx, "Session不存在")
		return
	}

	teamIDInterface := sess.Get("team_id")
	if teamIDInterface == nil {
		h.HandleUnauthorized(ctx, "Session中缺少团队ID信息")
		return
	}

	// 类型转换处理
	var teamID int32
	switch v := teamIDInterface.(type) {
	case int32:
		teamID = v
	case uint:
		teamID = int32(v)
	case int:
		teamID = int32(v)
	default:
		h.HandleUnauthorized(ctx, "团队ID类型无效")
		return
	}

	var roleRequest v1.UpdateRoleRequest

	// 解析请求体
	if err := ctx.ReadJSON(&roleRequest); err != nil {
		h.HandleBadRequest(ctx, err, "无效的请求数据")
		return
	}

	// 验证权限字段是否为有效 JSON 格式
	if !h.isValidJSON(roleRequest.Permissions) {
		h.logger.Error("权限数据不是有效的 JSON 格式")
		h.HandleBadRequest(ctx, nil, "权限数据不是有效的 JSON 格式")
		return
	}

	// 更新角色记录
	err := h.roleService.UpdateRole(ctx, roleRequest.ID, teamID, roleRequest.Name, roleRequest.Permissions, roleRequest.Secure)
	if err != nil {
		h.logger.Error("更新角色失败", "teamID", teamID, "roleID", roleRequest.ID, "error", err)
		h.HandleInternalError(ctx, err, "更新角色失败")
		return
	}

	// 返回成功响应
	h.logger.Info("成功更新角色", "teamID", teamID, "roleID", roleRequest.ID)
	h.HandleSuccess(ctx, iris.Map{"message": "角色更新成功"})
}

// DeleteRole 删除角色记录
// @Summary 删除角色
// @Description 根据角色ID删除指定的角色
// @Tags 角色模块
// @Accept json
// @Produce json
// @Param request body v1.DeleteRoleRequest true "删除角色信息"
// @Success 200 {object} v1.Response
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/roles [delete]
func (h *RoleHandler) DeleteRole(ctx iris.Context) {
	// 从 POST 数据获取 roleID
	var request v1.DeleteRoleRequest

	// 解析请求体中的 roleID
	if err := ctx.ReadJSON(&request); err != nil || request.RoleID == 0 {
		h.logger.Error("解析删除角色请求失败或缺少有效的 roleID")
		ctx.StatusCode(iris.StatusBadRequest)
		_ = ctx.JSON(iris.Map{"error": "无效的角色ID"})
		return
	}

	// 从 session 中获取 teamID
	sess := ctx.Values().Get("session").(*sessions.Session)
	if sess == nil {
		h.HandleUnauthorized(ctx, "Session不存在")
		return
	}

	teamIDInterface := sess.Get("team_id")
	if teamIDInterface == nil {
		h.HandleUnauthorized(ctx, "Session中缺少团队ID信息")
		return
	}

	// 类型转换处理
	var teamID int32
	switch v := teamIDInterface.(type) {
	case int32:
		teamID = v
	case uint:
		teamID = int32(v)
	case int:
		teamID = int32(v)
	default:
		h.HandleUnauthorized(ctx, "团队ID类型无效")
		return
	}

	// 执行删除操作
	err := h.roleService.DeleteRole(ctx, teamID, request.RoleID)
	if err != nil {
		h.logger.Error("删除角色失败", "teamID", teamID, "roleID", request.RoleID, "error", err)
		h.HandleInternalError(ctx, err, "删除角色失败")
		return
	}

	// 返回成功响应
	h.logger.Info("成功删除角色", "teamID", teamID, "roleID", request.RoleID)
	_ = ctx.JSON(iris.Map{"message": "角色删除成功"})
}

// isValidJSON 检查字符串是否是有效的 JSON 格式（支持对象和数组）
func (h *RoleHandler) isValidJSON(input string) bool {
	var js interface{}
	return json.Unmarshal([]byte(input), &js) == nil
}
