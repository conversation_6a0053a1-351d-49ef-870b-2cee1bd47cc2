package user

import (
	v1 "fp-browser/api/v1/user"
	"fp-browser/internal/dao/model"
	"fp-browser/internal/service/user"
	"fp-browser/pkg/log"

	"strings"

	"github.com/kataras/iris/v12"
	"github.com/kataras/iris/v12/sessions"
)

type GroupHandler struct {
	*Handler
	groupService user.GroupService
}

// NewGroupHandler 创建 GroupHandler 实例
func NewGroupHandler(logger *log.Logger, groupService user.GroupService) *GroupHandler {
	return &GroupHandler{
		Handler:      NewHandler(logger),
		groupService: groupService,
	}
}

// CreateGroup 创建新的分组
// @Summary 创建新的分组
// @Description 创建一个新的分组
// @Tags 分组模块
// @Accept json
// @Produce json
// @Param request body v1.CreateGroupRequest true "分组信息"
// @Success 200 {object} v1.CreateGroupResponse
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/groups [post]
func (h *GroupHandler) CreateGroup(ctx iris.Context) {
	// 从 session 获取用户信息
	sess := ctx.Values().Get("session").(*sessions.Session)
	if sess == nil {
		h.HandleUnauthorized(ctx, "Session不存在")
		return
	}

	userIDInterface := sess.Get("user_id")
	teamIDInterface := sess.Get("team_id")
	if userIDInterface == nil || teamIDInterface == nil {
		h.HandleUnauthorized(ctx, "Session中缺少用户或团队信息")
		return
	}

	// 类型转换处理
	var userID, teamID int32
	switch v := userIDInterface.(type) {
	case int32:
		userID = v
	case uint:
		userID = int32(v)
	case int:
		userID = int32(v)
	default:
		h.HandleUnauthorized(ctx, "用户ID类型无效")
		return
	}

	switch v := teamIDInterface.(type) {
	case int32:
		teamID = v
	case uint:
		teamID = int32(v)
	case int:
		teamID = int32(v)
	default:
		h.HandleUnauthorized(ctx, "团队ID类型无效")
		return
	}

	// 解析请求体
	var request v1.CreateGroupRequest
	if err := ctx.ReadJSON(&request); err != nil {
		h.HandleBadRequest(ctx, err, "请求数据无效")
		return
	}

	// 创建分组对象
	group := &model.Group{
		Name:   request.Name,
		UserID: userID,
		TeamID: teamID,
	}

	// 调用服务层创建分组
	err := h.groupService.CreateGroup(ctx, group)
	if err != nil {
		h.logger.Error("创建分组失败", "userID", userID, "teamID", teamID, "name", request.Name, "error", err)

		// 检查是否是重复分组名称错误或空字符串错误
		if strings.Contains(err.Error(), "已存在") {
			ctx.StatusCode(iris.StatusConflict)
			_ = ctx.JSON(v1.Response{
				Code:    409,
				Message: err.Error(),
				Data:    nil,
			})
			return
		}

		if strings.Contains(err.Error(), "不能为空") {
			h.HandleBadRequest(ctx, err, err.Error())
			return
		}

		h.HandleInternalError(ctx, err, "分组创建失败")
		return
	}

	// 成功响应
	response := v1.CreateGroupResponse{
		Message: "分组创建成功",
		GroupID: group.ID,
	}
	h.logger.Info("分组创建成功", "userID", userID, "teamID", teamID, "groupID", group.ID, "name", request.Name)
	h.HandleSuccess(ctx, response)
}

// GetGroups 获取分组列表
// @Summary 获取分组列表
// @Description 根据团队ID获取分组列表，支持按用户筛选
// @Tags 分组模块
// @Accept json
// @Produce json
// @Param user_id query int false "用户ID筛选，为0时获取团队所有分组"
// @Success 200 {object} v1.GetGroupsResponse
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/groups [get]
func (h *GroupHandler) GetGroups(ctx iris.Context) {
	// 从 session 获取团队信息
	sess := ctx.Values().Get("session").(*sessions.Session)
	if sess == nil {
		h.HandleUnauthorized(ctx, "Session不存在")
		return
	}

	teamIDInterface := sess.Get("team_id")
	if teamIDInterface == nil {
		h.HandleUnauthorized(ctx, "Session中缺少团队ID信息")
		return
	}

	// 类型转换处理
	var teamID int32
	switch v := teamIDInterface.(type) {
	case int32:
		teamID = v
	case uint:
		teamID = int32(v)
	case int:
		teamID = int32(v)
	default:
		h.HandleUnauthorized(ctx, "团队ID类型无效")
		return
	}

	// 从请求参数获取用户ID筛选条件，默认值为 0（获取所有）
	userID := int32(ctx.URLParamIntDefault("user_id", 0))

	// 获取分组记录
	groups, err := h.groupService.GetGroups(ctx, userID, teamID)
	if err != nil {
		h.logger.Error("获取分组列表失败", "teamID", teamID, "userID", userID, "error", err)
		h.HandleInternalError(ctx, err, "分组列表获取失败")
		return
	}

	// 返回成功响应
	response := v1.GetGroupsResponse{
		Groups: groups,
		Total:  int64(len(groups)),
	}
	h.logger.Info("成功获取分组列表", "teamID", teamID, "userID", userID, "count", len(groups))
	h.HandleSuccess(ctx, response)
}

// UpdateGroup 更新分组信息
// @Summary 更新分组信息
// @Description 更新指定分组的名称
// @Tags 分组模块
// @Accept json
// @Produce json
// @Param request body v1.UpdateGroupRequest true "更新分组信息"
// @Success 200 {object} v1.UpdateGroupResponse
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/groups [put]
func (h *GroupHandler) UpdateGroup(ctx iris.Context) {
	// 从 session 获取用户信息
	sess := ctx.Values().Get("session").(*sessions.Session)
	if sess == nil {
		h.HandleUnauthorized(ctx, "Session不存在")
		return
	}

	userIDInterface := sess.Get("user_id")
	teamIDInterface := sess.Get("team_id")
	if userIDInterface == nil || teamIDInterface == nil {
		h.HandleUnauthorized(ctx, "Session中缺少用户或团队信息")
		return
	}

	// 类型转换处理
	var userID, teamID int32
	switch v := userIDInterface.(type) {
	case int32:
		userID = v
	case uint:
		userID = int32(v)
	case int:
		userID = int32(v)
	default:
		h.HandleUnauthorized(ctx, "用户ID类型无效")
		return
	}

	switch v := teamIDInterface.(type) {
	case int32:
		teamID = v
	case uint:
		teamID = int32(v)
	case int:
		teamID = int32(v)
	default:
		h.HandleUnauthorized(ctx, "团队ID类型无效")
		return
	}

	// 解析请求体
	var request v1.UpdateGroupRequest
	if err := ctx.ReadJSON(&request); err != nil {
		h.HandleBadRequest(ctx, err, "无效的请求数据")
		return
	}

	// 创建更新对象
	group := &model.Group{
		ID:     request.ID,
		Name:   request.Name,
		UserID: userID,
	}

	// 调用服务层更新分组
	err := h.groupService.UpdateGroup(ctx, group, teamID)
	if err != nil {
		h.logger.Error("更新分组失败", "groupID", request.ID, "userID", userID, "teamID", teamID, "name", request.Name, "error", err)

		// 检查是否是重复分组名称错误
		if strings.Contains(err.Error(), "已存在") {
			ctx.StatusCode(iris.StatusConflict)
			_ = ctx.JSON(v1.Response{
				Code:    409,
				Message: err.Error(),
				Data:    nil,
			})
			return
		}

		if strings.Contains(err.Error(), "不能为空") {
			h.HandleBadRequest(ctx, err, err.Error())
			return
		}

		if strings.Contains(err.Error(), "分组不存在") || strings.Contains(err.Error(), "无权限访问") {
			ctx.StatusCode(iris.StatusNotFound)
			_ = ctx.JSON(v1.Response{
				Code:    404,
				Message: err.Error(),
				Data:    nil,
			})
			return
		}

		h.HandleInternalError(ctx, err, "分组更新失败")
		return
	}

	// 成功响应
	response := v1.UpdateGroupResponse{
		Message: "分组更新成功",
	}
	h.logger.Info("分组更新成功", "groupID", request.ID, "userID", userID, "teamID", teamID, "name", request.Name)
	h.HandleSuccess(ctx, response)
}

// DeleteGroup 删除分组
// @Summary 删除分组
// @Description 删除指定的分组
// @Tags 分组模块
// @Accept json
// @Produce json
// @Param request body v1.DeleteGroupRequest true "删除分组信息"
// @Success 200 {object} v1.DeleteGroupResponse
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/groups [delete]
func (h *GroupHandler) DeleteGroup(ctx iris.Context) {
	// 从 session 获取团队信息
	sess := ctx.Values().Get("session").(*sessions.Session)
	if sess == nil {
		h.HandleUnauthorized(ctx, "Session不存在")
		return
	}

	teamIDInterface := sess.Get("team_id")
	if teamIDInterface == nil {
		h.HandleUnauthorized(ctx, "Session中缺少团队ID信息")
		return
	}

	// 类型转换处理
	var teamID int32
	switch v := teamIDInterface.(type) {
	case int32:
		teamID = v
	case uint:
		teamID = int32(v)
	case int:
		teamID = int32(v)
	default:
		h.HandleUnauthorized(ctx, "团队ID类型无效")
		return
	}

	// 解析请求体
	var request v1.DeleteGroupRequest
	if err := ctx.ReadJSON(&request); err != nil {
		h.HandleBadRequest(ctx, err, "无效的请求数据")
		return
	}

	// 调用服务层删除分组
	err := h.groupService.DeleteGroup(ctx, request.ID, teamID)
	if err != nil {
		h.logger.Error("删除分组失败", "groupID", request.ID, "teamID", teamID, "error", err)
		h.HandleInternalError(ctx, err, "分组删除失败")
		return
	}

	// 成功响应
	response := v1.DeleteGroupResponse{
		Message: "分组删除成功",
	}
	h.logger.Info("分组删除成功", "groupID", request.ID, "teamID", teamID)
	h.HandleSuccess(ctx, response)
}
