package user

import (
	v1 "fp-browser/api/v1/user"
	"fp-browser/internal/service/user"
	"fp-browser/pkg/log"
	"github.com/kataras/iris/v12"
	"github.com/kataras/iris/v12/sessions"
	"time"
)

type OperationLogHandler struct {
	*Handler
	operationLogService user.OperationLogService
}

// NewOperationLogHandler 创建 OperationLogHandler 实例
func NewOperationLogHandler(logger *log.Logger, operationLogService user.OperationLogService) *OperationLogHandler {
	return &OperationLogHandler{
		Handler:             NewHandler(logger),
		operationLogService: operationLogService,
	}
}

// GetOperationLogs 根据条件获取操作日志
// @Summary 获取操作日志
// @Description 根据团队ID和其他条件获取操作日志记录，支持分页和多种筛选条件
// @Tags 操作日志模块
// @Accept json
// @Produce json
// @Param user_id query int false "用户ID筛选"
// @Param action query int false "操作类型筛选"
// @Param category query int false "操作分类筛选"
// @Param target query string false "操作目标筛选"
// @Param limit query int false "每页数量，默认100，最大500" minimum(1) maximum(500)
// @Param offset query int false "偏移量，默认0" minimum(0)
// @Param start_time query string false "开始时间，RFC3339格式" format(date-time)
// @Param end_time query string false "结束时间，RFC3339格式" format(date-time)
// @Success 200 {object} v1.GetOperationLogsResponse
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/operation-logs [get]
func (h *OperationLogHandler) GetOperationLogs(ctx iris.Context) {
	// 从 session 获取 teamID
	sess := ctx.Values().Get("session").(*sessions.Session)
	if sess == nil {
		h.HandleUnauthorized(ctx, "Session不存在")
		return
	}

	teamIDInterface := sess.Get("team_id")
	if teamIDInterface == nil {
		h.HandleUnauthorized(ctx, "Session中缺少团队ID信息")
		return
	}

	// 类型转换处理
	var teamID int32
	switch v := teamIDInterface.(type) {
	case int32:
		teamID = v
	case uint:
		teamID = int32(v)
	case int:
		teamID = int32(v)
	default:
		h.HandleUnauthorized(ctx, "团队ID类型无效")
		return
	}

	// 从 GET 请求中获取参数
	userID := int32(ctx.URLParamIntDefault("user_id", 0))
	action := int32(ctx.URLParamIntDefault("action", 0))
	category := int32(ctx.URLParamIntDefault("category", 0))
	target := ctx.URLParamDefault("target", "")

	limit := ctx.URLParamIntDefault("limit", 100)
	if limit > 500 {
		limit = 500
	}
	if limit <= 0 {
		limit = 100
	}

	offset := ctx.URLParamIntDefault("offset", 0)
	if offset < 0 {
		offset = 0
	}

	// 解析时间范围参数
	var startTime, endTime *time.Time
	if startStr := ctx.URLParam("start_time"); startStr != "" {
		if start, err := time.Parse(time.RFC3339, startStr); err == nil {
			startTime = &start
		} else {
			h.logger.Warn("解析start_time失败", "start_time", startStr, "error", err)
		}
	}
	if endStr := ctx.URLParam("end_time"); endStr != "" {
		if end, err := time.Parse(time.RFC3339, endStr); err == nil {
			endTime = &end
		} else {
			h.logger.Warn("解析end_time失败", "end_time", endStr, "error", err)
		}
	}

	// 发起查询请求
	logs, total, err := h.operationLogService.GetLogs(
		ctx,
		teamID,
		userID,
		action,
		category,
		target,
		limit,
		offset,
		startTime,
		endTime,
	)
	if err != nil {
		h.logger.Error("获取操作日志失败",
			"teamID", teamID,
			"userID", userID,
			"action", action,
			"category", category,
			"target", target,
			"error", err)
		h.HandleInternalError(ctx, err, "获取日志失败")
		return
	}

	response := v1.GetOperationLogsResponse{
		Logs:  logs,
		Total: total,
	}

	// 返回查询结果
	h.logger.Info("成功获取操作日志",
		"teamID", teamID,
		"userID", userID,
		"action", action,
		"category", category,
		"target", target,
		"total", total,
		"limit", limit,
		"offset", offset)

	h.HandleSuccess(ctx, response)
}
