package user

import (
	"fmt"
	v1 "fp-browser/api/v1/user"
	"fp-browser/internal/service/shared"
	"fp-browser/internal/service/user"
	_ "fp-browser/internal/view_model"
	"fp-browser/pkg/log"
	"io"
	"strconv"

	"github.com/kataras/iris/v12"
)

type EnvironmentHandler struct {
	*Handler
	environmentService user.EnvironmentService
	userService        user.UserService
	ossService         shared.OSSService
	// TODO: 添加Redis服务依赖
	// redisService redis.Service
}

// NewEnvironmentHandler 创建 EnvironmentHandler 实例
func NewEnvironmentHandler(
	logger *log.Logger,
	environmentService user.EnvironmentService,
	userService user.UserService,
	ossService shared.OSSService,
) *EnvironmentHandler {
	return &EnvironmentHandler{
		Handler:            NewHandler(logger),
		environmentService: environmentService,
		userService:        userService,
		ossService:         ossService,
	}
}

// CreateEnvironment 创建新的环境记录
// @Summary 创建环境
// @Description 创建新的环境记录
// @Tags 环境模块
// @Accept json
// @Produce json
// @Param request body v1.CreateEnvironmentRequest true "环境信息"
// @Success 200 {object} v1.CreateEnvironmentResponse
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/environments [post]
func (h *EnvironmentHandler) CreateEnvironment(ctx iris.Context) {
	// 1. 获取Session信息
	userID, ok := h.GetUserIDFromSession(ctx)
	if !ok {
		h.HandleUnauthorized(ctx, "用户未登录")
		return
	}

	teamID, ok := h.GetTeamIDFromSession(ctx)
	if !ok {
		h.HandleUnauthorized(ctx, "团队信息未找到")
		return
	}

	// 2. 解析请求体
	var request v1.CreateEnvironmentRequest
	if err := ctx.ReadJSON(&request); err != nil {
		h.HandleBadRequest(ctx, err, "请求数据无效")
		return
	}

	// 3. 调用Service层
	if err := h.environmentService.CreateEnvironment(ctx, &request, userID, teamID); err != nil {
		h.HandleInternalError(ctx, err, "创建环境失败")
		return
	}

	// 4. 返回成功响应
	h.HandleSuccess(ctx, v1.CreateEnvironmentResponse{Message: "环境创建成功"})
}

// GetEnvironments 获取环境记录列表
// @Summary 获取环境列表
// @Description 根据团队ID获取环境记录，支持分页和筛选
// @Tags 环境模块
// @Accept json
// @Produce json
// @Param name query string false "环境名称筛选"
// @Param user_id query int false "用户ID筛选"
// @Param group_id query int false "分组ID筛选"
// @Param limit query int false "每页数量，默认100，最大500" minimum(1) maximum(500)
// @Param offset query int false "偏移量，默认0" minimum(0)
// @Success 200 {object} v1.GetEnvironmentsResponse
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/environments [get]
func (h *EnvironmentHandler) GetEnvironments(ctx iris.Context) {
	// 1. 获取Session信息
	teamID, ok := h.GetTeamIDFromSession(ctx)
	if !ok {
		h.HandleUnauthorized(ctx, "团队信息未找到")
		return
	}

	// 2. 解析查询参数
	var request v1.GetEnvironmentsRequest
	if err := ctx.ReadQuery(&request); err != nil {
		h.HandleBadRequest(ctx, err, "查询参数无效")
		return
	}

	// 3. 调用Service层
	environments, total, err := h.environmentService.GetEnvironments(ctx, &request, teamID)
	if err != nil {
		h.HandleInternalError(ctx, err, "获取环境列表失败")
		return
	}

	// 4. 返回成功响应
	h.HandleSuccess(ctx, v1.GetEnvironmentsResponse{
		Environments: environments,
		Total:        total,
	})
}

// GetEnvironmentByID 根据环境ID获取环境记录
// @Summary 根据ID获取环境
// @Description 根据环境ID获取环境记录，包含Redis占用检查
// @Tags 环境模块
// @Accept json
// @Produce json
// @Param id path int true "环境ID"
// @Success 200 {object} view_model.EnvironmentWithProxy
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 409 {object} v1.Response "环境已被占用"
// @Failure 500 {object} v1.Response
// @Router /api/v1/environments/{id} [get]
func (h *EnvironmentHandler) GetEnvironmentByID(ctx iris.Context) {
	// 1. 获取Session信息
	userID, ok := h.GetUserIDFromSession(ctx)
	if !ok {
		h.HandleUnauthorized(ctx, "用户未登录")
		return
	}

	teamID, ok := h.GetTeamIDFromSession(ctx)
	if !ok {
		h.HandleUnauthorized(ctx, "团队信息未找到")
		return
	}

	// 2. 解析路径参数
	envIDStr := ctx.Params().Get("id")
	envID, err := strconv.Atoi(envIDStr)
	if err != nil {
		h.HandleBadRequest(ctx, err, "环境ID无效")
		return
	}

	// 3. 调用Service层
	environment, err := h.environmentService.GetEnvironmentByID(ctx, int32(envID), userID, teamID)
	if err != nil {
		h.HandleInternalError(ctx, err, "获取环境信息失败")
		return
	}

	// 4. 返回成功响应
	h.HandleSuccess(ctx, environment)
}

// UpdateEnvironment 批量更新环境记录
// @Summary 批量更新环境
// @Description 批量更新多个环境记录
// @Tags 环境模块
// @Accept json
// @Produce json
// @Param request body v1.UpdateEnvironmentRequest true "环境信息列表"
// @Success 200 {object} v1.UpdateEnvironmentResponse
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/environments [put]
func (h *EnvironmentHandler) UpdateEnvironment(ctx iris.Context) {
	// 1. 获取Session信息
	teamID, ok := h.GetTeamIDFromSession(ctx)
	if !ok {
		h.HandleUnauthorized(ctx, "团队信息未找到")
		return
	}

	// 2. 解析请求体
	var request v1.UpdateEnvironmentRequest
	if err := ctx.ReadJSON(&request); err != nil {
		h.HandleBadRequest(ctx, err, "请求数据无效")
		return
	}

	// 3. 调用Service层
	if err := h.environmentService.UpdateEnvironment(ctx, &request, teamID); err != nil {
		h.HandleInternalError(ctx, err, "更新环境失败")
		return
	}

	// 4. 返回成功响应
	h.HandleSuccess(ctx, v1.UpdateEnvironmentResponse{Message: "环境更新成功"})
}

// DeleteEnvironments 批量删除环境记录
// @Summary 批量删除环境
// @Description 批量删除多个环境记录
// @Tags 环境模块
// @Accept json
// @Produce json
// @Param request body v1.DeleteEnvironmentRequest true "环境ID列表"
// @Success 200 {object} v1.DeleteEnvironmentResponse
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/environments [delete]
func (h *EnvironmentHandler) DeleteEnvironments(ctx iris.Context) {
	// 1. 获取Session信息
	teamID, ok := h.GetTeamIDFromSession(ctx)
	if !ok {
		h.HandleUnauthorized(ctx, "团队信息未找到")
		return
	}

	// 2. 解析请求体
	var request v1.DeleteEnvironmentRequest
	if err := ctx.ReadJSON(&request); err != nil {
		h.HandleBadRequest(ctx, err, "请求数据无效")
		return
	}

	// 3. 调用Service层
	if err := h.environmentService.DeleteEnvironment(ctx, &request, teamID); err != nil {
		h.HandleInternalError(ctx, err, "删除环境失败")
		return
	}

	// 4. 返回成功响应
	h.HandleSuccess(ctx, v1.DeleteEnvironmentResponse{Message: "环境删除成功"})
}

// GetDeletedEnvironments 获取已删除的环境列表
// @Summary 获取已删除的环境列表
// @Description 获取团队下已删除的环境记录，支持分页
// @Tags 环境模块
// @Accept json
// @Produce json
// @Param limit query int false "每页数量，默认100，最大500" minimum(1) maximum(500) default(100)
// @Param offset query int false "偏移量，默认0" minimum(0) default(0)
// @Success 200 {object} v1.GetEnvironmentsResponse
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/environments/deleted [get]
func (h *EnvironmentHandler) GetDeletedEnvironments(ctx iris.Context) {
	// 1. 获取Session信息
	teamID, ok := h.GetTeamIDFromSession(ctx)
	if !ok {
		h.HandleUnauthorized(ctx, "团队信息未找到")
		return
	}

	// 2. 解析查询参数
	limit := ctx.URLParamIntDefault("limit", 100)
	offset := ctx.URLParamIntDefault("offset", 0)

	// 3. 参数验证
	if limit <= 0 || limit > 500 {
		limit = 100
	}
	if offset < 0 {
		offset = 0
	}

	// 4. 调用Service层
	environments, total, err := h.environmentService.GetDeletedEnvironments(ctx, limit, offset, teamID)
	if err != nil {
		h.HandleInternalError(ctx, err, "获取已删除环境列表失败")
		return
	}

	// 5. 返回成功响应
	response := v1.GetEnvironmentsResponse{
		Environments: environments,
		Total:        total,
	}
	h.HandleSuccess(ctx, response)
}

// RestoreEnvironment 恢复已删除的环境
// @Summary 恢复已删除的环境
// @Description 批量恢复已删除的环境记录
// @Tags 环境模块
// @Accept json
// @Produce json
// @Param request body v1.DeleteEnvironmentRequest true "环境ID列表"
// @Success 200 {object} v1.RestoreEnvironmentResponse
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/environments/restore [put]
func (h *EnvironmentHandler) RestoreEnvironment(ctx iris.Context) {
	// 1. 获取Session信息
	teamID, ok := h.GetTeamIDFromSession(ctx)
	if !ok {
		h.HandleUnauthorized(ctx, "团队信息未找到")
		return
	}

	// 2. 解析请求体
	var request v1.DeleteEnvironmentRequest
	if err := ctx.ReadJSON(&request); err != nil {
		h.HandleBadRequest(ctx, err, "请求数据无效")
		return
	}

	// 3. 调用Service层
	if err := h.environmentService.RestoreEnvironment(ctx, &request, teamID); err != nil {
		h.HandleInternalError(ctx, err, "恢复环境失败")
		return
	}

	// 4. 返回成功响应
	h.HandleSuccess(ctx, v1.RestoreEnvironmentResponse{Message: "环境恢复成功"})
}

// PermanentDeleteEnvironment 永久删除环境
// @Summary 永久删除环境
// @Description 批量永久删除环境记录，此操作不可逆
// @Tags 环境模块
// @Accept json
// @Produce json
// @Param request body v1.DeleteEnvironmentRequest true "环境ID列表"
// @Success 200 {object} v1.DeleteEnvironmentResponse
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/environments/permanent [delete]
func (h *EnvironmentHandler) PermanentDeleteEnvironment(ctx iris.Context) {
	// 1. 获取Session信息
	teamID, ok := h.GetTeamIDFromSession(ctx)
	if !ok {
		h.HandleUnauthorized(ctx, "团队信息未找到")
		return
	}

	// 2. 解析请求体
	var request v1.DeleteEnvironmentRequest
	if err := ctx.ReadJSON(&request); err != nil {
		h.HandleBadRequest(ctx, err, "请求数据无效")
		return
	}

	// 3. 调用Service层
	if err := h.environmentService.PermanentDeleteEnvironment(ctx, &request, teamID); err != nil {
		h.HandleInternalError(ctx, err, "永久删除环境失败")
		return
	}

	// 4. 返回成功响应
	h.HandleSuccess(ctx, v1.DeleteEnvironmentResponse{Message: "环境永久删除成功"})
}

// UpdateEnvironmentProxy 批量更新环境代理ID
// @Summary 批量更新环境代理
// @Description 批量更新多个环境的代理ID
// @Tags 环境模块
// @Accept json
// @Produce json
// @Param request body v1.UpdateEnvironmentProxyRequest true "环境ID列表和代理ID"
// @Success 200 {object} v1.UpdateEnvironmentProxyResponse
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/environments/proxy [put]
func (h *EnvironmentHandler) UpdateEnvironmentProxy(ctx iris.Context) {
	// 1. 获取Session信息
	teamID, ok := h.GetTeamIDFromSession(ctx)
	if !ok {
		h.HandleUnauthorized(ctx, "团队信息未找到")
		return
	}

	// 2. 解析请求体
	var request v1.UpdateEnvironmentProxyRequest
	if err := ctx.ReadJSON(&request); err != nil {
		h.HandleBadRequest(ctx, err, "请求数据无效")
		return
	}

	// 3. 调用Service层
	if err := h.environmentService.UpdateEnvironmentProxy(ctx, &request, teamID); err != nil {
		h.HandleInternalError(ctx, err, "更新环境代理失败")
		return
	}

	// 4. 返回成功响应
	h.HandleSuccess(ctx, v1.UpdateEnvironmentProxyResponse{Message: "环境代理更新成功"})
}

// RefreshEnvironmentRedisRecord 刷新环境在 Redis 中的记录
// @Summary 刷新环境Redis记录
// @Description 刷新环境在Redis中的占用记录
// @Tags 环境模块
// @Accept json
// @Produce json
// @Param id path int true "环境ID"
// @Success 200 {object} v1.RefreshEnvironmentResponse
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/environments/{id}/refresh [post]
func (h *EnvironmentHandler) RefreshEnvironmentRedisRecord(ctx iris.Context) {
	// 1. 获取Session信息
	userID, ok := h.GetUserIDFromSession(ctx)
	if !ok {
		h.HandleUnauthorized(ctx, "用户未登录")
		return
	}

	// 2. 解析路径参数
	envIDStr := ctx.Params().Get("id")
	envID, err := strconv.Atoi(envIDStr)
	if err != nil {
		h.HandleBadRequest(ctx, err, "环境ID无效")
		return
	}

	// 3. 调用Service层
	if err := h.environmentService.RefreshEnvironmentOccupation(ctx, int32(envID), userID); err != nil {
		h.HandleInternalError(ctx, err, "刷新环境占用记录失败")
		return
	}

	// 4. 返回成功响应
	h.HandleSuccess(ctx, iris.Map{"message": "刷新成功"})
}

// RemoveEnvironmentRedisRecord 移除环境在 Redis 中的记录
// @Summary 移除环境Redis记录
// @Description 移除环境在Redis中的占用记录
// @Tags 环境模块
// @Accept json
// @Produce json
// @Param id path int true "环境ID"
// @Success 200 {object} v1.RemoveEnvironmentResponse
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 403 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/environments/{id}/remove [delete]
func (h *EnvironmentHandler) RemoveEnvironmentRedisRecord(ctx iris.Context) {
	// 1. 获取Session信息
	userID, ok := h.GetUserIDFromSession(ctx)
	if !ok {
		h.HandleUnauthorized(ctx, "用户未登录")
		return
	}

	// 2. 解析路径参数
	envIDStr := ctx.Params().Get("id")
	envID, err := strconv.Atoi(envIDStr)
	if err != nil {
		h.HandleBadRequest(ctx, err, "环境ID无效")
		return
	}

	// 3. 调用Service层
	if err := h.environmentService.ReleaseEnvironmentOccupation(ctx, int32(envID), userID); err != nil {
		h.HandleInternalError(ctx, err, "释放环境占用记录失败")
		return
	}

	// 4. 返回成功响应
	h.HandleSuccess(ctx, iris.Map{"message": "释放成功"})
}

// ==================== 环境文件相关API ====================

// GenerateUploadURL 生成上传URL
// @Summary      生成环境文件上传URL
// @Description  生成环境文件上传URL
// @Tags         环境文件
// @Accept       json
// @Produce      json
// @Param        Authorization header string true "用户令牌"
// @Param        request body v1.GenerateEnvironmentURLRequest true "请求参数"
// @Success      200 {object} v1.GenerateEnvironmentURLResponse
// @Failure      400 {object} map[string]interface{} "错误信息"
// @Failure      401 {object} map[string]interface{} "未授权"
// @Failure      500 {object} map[string]interface{} "服务器错误"
// @Router       /api/v1/environments/file/upload-url [post]
func (h *EnvironmentHandler) GenerateUploadURL(ctx iris.Context) {
	// 1. 从Session获取用户信息
	userID, ok := h.GetUserIDFromSession(ctx)
	if !ok {
		h.HandleUnauthorized(ctx, "请先登录")
		return
	}

	teamID, ok := h.GetTeamIDFromSession(ctx)
	if !ok {
		h.HandleUnauthorized(ctx, "团队信息未找到")
		return
	}

	// 2. 解析请求参数
	var req v1.GenerateEnvironmentURLRequest
	if err := ctx.ReadJSON(&req); err != nil {
		h.HandleBadRequest(ctx, err, "无效的请求参数")
		return
	}

	// 3. 调用服务层生成上传URL
	resp, err := h.environmentService.GenerateEnvironmentUploadURL(ctx.Request().Context(), &req, userID, teamID)
	if err != nil {
		h.HandleInternalError(ctx, err, "生成上传URL失败")
		return
	}

	// 4. 返回响应
	h.HandleSuccess(ctx, resp)
}

// DeleteEnvironmentFile 删除环境文件
// @Summary 删除环境文件
// @Description 删除环境文件并清除环境的存储路径
// @Tags 环境文件
// @Accept json
// @Produce json
// @Param Authorization header string true "用户令牌"
// @Param request body v1.DeleteEnvironmentFileRequest true "请求参数"
// @Success 200 {object} map[string]interface{} "成功信息"
// @Failure 400 {object} map[string]interface{} "错误信息"
// @Failure 401 {object} map[string]interface{} "未授权"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Router /api/v1/environments/file [delete]
func (h *EnvironmentHandler) DeleteEnvironmentFile(ctx iris.Context) {
	// 1. 从Session获取用户信息
	userID, ok := h.GetUserIDFromSession(ctx)
	if !ok {
		h.HandleUnauthorized(ctx, "请先登录")
		return
	}

	teamID, ok := h.GetTeamIDFromSession(ctx)
	if !ok {
		h.HandleUnauthorized(ctx, "团队信息未找到")
		return
	}

	// 2. 解析请求参数
	var req v1.DeleteEnvironmentFileRequest
	if err := ctx.ReadJSON(&req); err != nil {
		h.HandleBadRequest(ctx, err, "无效的请求参数")
		return
	}

	// 3. 调用服务层删除环境文件
	if err := h.environmentService.DeleteEnvironmentFile(ctx.Request().Context(), &req, userID, teamID); err != nil {
		h.HandleInternalError(ctx, err, "删除环境文件失败")
		return
	}

	// 4. 返回响应
	h.HandleSuccess(ctx, iris.Map{"message": "环境文件删除成功"})
}

// DownloadEnvironmentFile 下载环境文件
// @Summary 下载环境文件
// @Description 服务器从OSS获取环境文件并流式传输给客户端
// @Tags 环境文件
// @Accept json
// @Produce octet-stream
// @Param id path int true "环境ID"
// @Param token query string true "下载令牌"
// @Success 200 {file} file "环境文件内容"
// @Failure 400 {object} map[string]interface{} "错误信息"
// @Failure 401 {object} map[string]interface{} "未授权"
// @Failure 404 {object} map[string]interface{} "文件不存在"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Router /api/v1/environments/{id}/download [get]
func (h *EnvironmentHandler) DownloadEnvironmentFile(ctx iris.Context) {
	// 1. 从请求中获取参数
	envIDStr := ctx.Params().Get("id")
	envID, err := strconv.Atoi(envIDStr)
	if err != nil {
		h.HandleBadRequest(ctx, err, "环境ID无效")
		return
	}

	// 获取下载令牌
	token := ctx.URLParam("token")
	if token == "" {
		h.HandleUnauthorized(ctx, "缺少下载令牌")
		return
	}

	// 2. 从Session获取用户信息
	userID, ok := h.GetUserIDFromSession(ctx)
	if !ok {
		h.HandleUnauthorized(ctx, "请先登录")
		return
	}

	teamID, ok := h.GetTeamIDFromSession(ctx)
	if !ok {
		h.HandleUnauthorized(ctx, "团队信息未找到")
		return
	}

	// 3. 验证下载令牌（在实际应用中应该检查Redis中存储的令牌）
	// TODO: 使用Redis服务验证令牌有效性

	// 4. 获取环境存储信息
	env, err := h.environmentService.GetEnvironmentStorageInfo(ctx.Request().Context(), int32(envID), userID, teamID)
	if err != nil {
		h.HandleInternalError(ctx, err, "获取环境信息失败")
		return
	}

	if env == nil || env.Storage == "" {
		h.HandleNotFound(ctx, "环境文件不存在")
		return
	}

	// 5. 从OSS下载文件
	downloadResult, err := h.ossService.DownloadObject(ctx.Request().Context(), env.Storage)
	if err != nil {
		h.HandleInternalError(ctx, err, "下载环境文件失败")
		return
	}

	if downloadResult.Body == nil {
		h.HandleInternalError(ctx, nil, "文件内容为空")
		return
	}

	// 6. 设置响应头
	ctx.ContentType("application/octet-stream")
	ctx.Header("Content-Disposition", fmt.Sprintf("attachment; filename=environment_%d.zip", envID))
	if downloadResult.ContentLength != nil {
		ctx.Header("Content-Length", strconv.FormatInt(*downloadResult.ContentLength, 10))
	}

	// 7. 流式传输文件内容给客户端
	defer downloadResult.Body.Close() // 确保关闭流
	_, err = io.Copy(ctx.ResponseWriter(), downloadResult.Body)
	if err != nil {
		h.logger.Error("传输文件流失败", "error", err)
		// 注意：在这里不能再次发送HTTP错误，因为已经开始发送响应体
	}
}

func (h *Handler) HandleNotFound(ctx iris.Context, message string) {
	h.logger.Error(message)
	ctx.StatusCode(iris.StatusNotFound)
	_ = ctx.JSON(iris.Map{"error": message})
}
