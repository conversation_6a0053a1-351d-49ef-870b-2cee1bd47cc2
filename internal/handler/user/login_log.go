package user

import (
	v1 "fp-browser/api/v1/user"
	"fp-browser/internal/service/user"
	"fp-browser/pkg/log"
	"github.com/kataras/iris/v12"
	"github.com/kataras/iris/v12/sessions"
	"time"
)

type LoginLogHandler struct {
	*Handler
	loginLogService user.LoginLogService
}

// NewLoginLogHandler 创建 LoginLogHandler 实例
func NewLoginLogHandler(logger *log.Logger, loginLogService user.LoginLogService) *LoginLogHandler {
	return &LoginLogHandler{
		Handler:         NewHandler(logger),
		loginLogService: loginLogService,
	}
}

// GetLoginLogs 根据团队ID和其他条件获取登录日志
// @Summary 获取登录日志
// @Description 根据团队ID和其他条件获取登录日志记录，支持分页和时间范围筛选
// @Tags 登录日志模块
// @Accept json
// @Produce json
// @Param user_id query int false "用户ID筛选"
// @Param limit query int false "每页数量，默认100，最大500" minimum(1) maximum(500)
// @Param offset query int false "偏移量，默认0" minimum(0)
// @Param start_time query string false "开始时间，RFC3339格式" format(date-time)
// @Param end_time query string false "结束时间，RFC3339格式" format(date-time)
// @Success 200 {object} v1.GetLoginLogsResponse
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/login-logs [get]
func (h *LoginLogHandler) GetLoginLogs(ctx iris.Context) {
	// 从 session 中获取 teamID
	sess := ctx.Values().Get("session").(*sessions.Session)
	if sess == nil {
		h.HandleUnauthorized(ctx, "Session不存在")
		return
	}

	teamIDInterface := sess.Get("team_id")
	if teamIDInterface == nil {
		h.HandleUnauthorized(ctx, "Session中缺少团队ID信息")
		return
	}

	// 类型转换处理
	var teamID int32
	switch v := teamIDInterface.(type) {
	case int32:
		teamID = v
	case uint:
		teamID = int32(v)
	case int:
		teamID = int32(v)
	default:
		h.HandleUnauthorized(ctx, "团队ID类型无效")
		return
	}

	// 从 GET 请求中获取其他参数
	userID := int32(ctx.URLParamIntDefault("user_id", 0))
	limit := ctx.URLParamIntDefault("limit", 100)
	if limit > 500 {
		limit = 500
	}
	if limit <= 0 {
		limit = 100
	}
	offset := ctx.URLParamIntDefault("offset", 0)
	if offset < 0 {
		offset = 0
	}

	// 解析时间范围
	var startTime, endTime *time.Time
	if startStr := ctx.URLParam("start_time"); startStr != "" {
		if start, err := time.Parse(time.RFC3339, startStr); err == nil {
			startTime = &start
		} else {
			h.logger.Warn("解析start_time失败", "start_time", startStr, "error", err)
		}
	}
	if endStr := ctx.URLParam("end_time"); endStr != "" {
		if end, err := time.Parse(time.RFC3339, endStr); err == nil {
			endTime = &end
		} else {
			h.logger.Warn("解析end_time失败", "end_time", endStr, "error", err)
		}
	}

	// 调用服务方法获取带用户名的日志
	logs, total, err := h.loginLogService.GetLogsWithUserName(ctx, teamID, userID, limit, offset, startTime, endTime)
	if err != nil {
		h.logger.Error("获取登录日志失败", "teamID", teamID, "userID", userID, "error", err)
		h.HandleInternalError(ctx, err, "获取日志失败")
		return
	}

	response := v1.GetLoginLogsResponse{
		Logs:  logs,
		Total: total,
	}

	h.logger.Info("成功获取登录日志", "teamID", teamID, "userID", userID, "total", total, "limit", limit, "offset", offset)
	h.HandleSuccess(ctx, response)
}
