package user

import (
	v1 "fp-browser/api/v1/user"
	"fp-browser/internal/dao/model"
	"fp-browser/internal/service/user"
	"fp-browser/pkg/log"
	"github.com/kataras/iris/v12"
	"github.com/kataras/iris/v12/sessions"
)

type TeamHandler struct {
	*Handler
	teamService user.TeamService
	roleService user.RoleService
}

// NewTeamHandler 创建 TeamHandler 实例
func NewTeamHandler(logger *log.Logger, teamService user.TeamService, roleService user.RoleService) *TeamHandler {
	return &TeamHandler{
		Handler:     NewHandler(logger),
		teamService: teamService,
		roleService: roleService,
	}
}

// GetUserList 根据 teamID 获取所有关联的用户，支持分页和筛选
// @Summary 获取团队用户列表
// @Description 根据团队ID获取所有关联的用户，支持分页和筛选
// @Tags 团队模块
// @Accept json
// @Produce json
// @Param username query string false "用户名筛选"
// @Param role_id query int false "角色ID筛选"
// @Param limit query int true "每页数量，最大500" minimum(1) maximum(500)
// @Param offset query int true "偏移量" minimum(0)
// @Success 200 {object} v1.GetUserListResponse
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/teams/users [get]
func (h *TeamHandler) GetUserList(ctx iris.Context) {
	// 从 session 获取 teamID
	sess := ctx.Values().Get("session").(*sessions.Session)
	if sess == nil {
		h.HandleUnauthorized(ctx, "Session不存在")
		return
	}

	teamIDInterface := sess.Get("team_id")
	if teamIDInterface == nil {
		h.HandleUnauthorized(ctx, "Session中缺少团队ID信息")
		return
	}

	// 类型转换处理
	var teamID int32
	switch v := teamIDInterface.(type) {
	case int32:
		teamID = v
	case uint:
		teamID = int32(v)
	case int:
		teamID = int32(v)
	default:
		h.HandleUnauthorized(ctx, "团队ID类型无效")
		return
	}

	// 获取查询参数
	username := ctx.URLParamDefault("username", "")
	roleID := int32(ctx.URLParamIntDefault("role_id", 0))

	limit, err := ctx.URLParamInt("limit")
	if err != nil || limit <= 0 {
		h.HandleBadRequest(ctx, err, "无效的limit参数")
		return
	}
	if limit > 500 {
		limit = 500
	}

	offset, err := ctx.URLParamInt("offset")
	if err != nil || offset < 0 {
		h.HandleBadRequest(ctx, err, "无效的offset参数")
		return
	}

	// 调用服务层获取用户列表
	users, total, err := h.teamService.GetUserList(ctx, username, teamID, roleID, limit, offset)
	if err != nil {
		h.logger.Error("获取团队用户列表失败", "teamID", teamID, "error", err)
		h.HandleInternalError(ctx, err, "获取用户失败")
		return
	}

	// 获取团队信息
	team, err := h.teamService.GetTeamByID(ctx, uint(teamID))
	if err != nil {
		h.logger.Error("获取团队信息失败", "teamID", teamID, "error", err)
		h.HandleUnauthorized(ctx, "用户团队异常！")
		return
	}

	// 格式化用户列表
	formattedUsers := make([]v1.FormattedUser, len(users))
	for i, userInfo := range users {
		roleName := "未绑定角色"
		if userInfo.RoleID != 0 {
			role, err := h.roleService.GetRoleByID(ctx, userInfo.RoleID)
			if err != nil {
				h.logger.Warn("获取用户角色失败", "userID", userInfo.ID, "roleID", userInfo.RoleID, "error", err)
				roleName = "角色不存在"
			} else {
				roleName = role.Name
			}
		}

		formattedUsers[i] = v1.FormattedUser{
			ID:                 userInfo.ID,
			Username:           userInfo.UserName,
			Email:              userInfo.Email,
			IsTwoFactorEnabled: userInfo.IsTwoFactorEnabled != nil && *userInfo.IsTwoFactorEnabled,
			IsActive:           userInfo.IsActive != nil && *userInfo.IsActive,
			RoleName:           roleName,
			IsOwner:            userInfo.ID == team.OwnerID,
		}
	}

	response := v1.GetUserListResponse{
		Users: formattedUsers,
		Total: total,
	}

	h.logger.Info("成功获取团队用户列表", "teamID", teamID, "total", total)
	h.HandleSuccess(ctx, response)
}

// AddUsersToTeam 添加多个新用户到指定的 teamID
// @Summary 添加用户到团队
// @Description 添加多个新用户到指定的团队
// @Tags 团队模块
// @Accept json
// @Produce json
// @Param request body v1.AddUsersToTeamRequest true "用户信息列表"
// @Success 200 {object} v1.Response
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 403 {object} v1.Response
// @Router /api/v1/teams/users [post]
func (h *TeamHandler) AddUsersToTeam(ctx iris.Context) {
	// 从 session 获取 teamID
	sess := ctx.Values().Get("session").(*sessions.Session)
	if sess == nil {
		h.HandleUnauthorized(ctx, "Session不存在")
		return
	}

	teamIDInterface := sess.Get("team_id")
	if teamIDInterface == nil {
		h.HandleUnauthorized(ctx, "Session中缺少团队ID信息")
		return
	}

	var teamID int32
	switch v := teamIDInterface.(type) {
	case int32:
		teamID = v
	case uint:
		teamID = int32(v)
	case int:
		teamID = int32(v)
	default:
		h.HandleUnauthorized(ctx, "团队ID类型无效")
		return
	}

	var request v1.AddUsersToTeamRequest
	if err := ctx.ReadJSON(&request); err != nil {
		h.HandleBadRequest(ctx, err, "请求数据无效")
		return
	}

	// 逐个添加用户（因为服务接口只支持单个用户）
	for _, userReq := range request.Users {
		// 只使用请求中的四个字段，其他字段设置为默认值
		userModel := &model.User{
			UserName:                 userReq.UserName,
			Email:                    "", // 空字符串，待后续处理
			Password:                 userReq.Password,
			Telephone:                "", // 空字符串，待后续处理
			RealName:                 "", // 空字符串
			IDCardNumber:             "", // 空字符串
			CompanyName:              "", // 空字符串
			CompanyUnifiedSocialCode: "", // 空字符串
			TeamID:                   teamID,
			RoleID:                   userReq.RoleID,
			IsActive:                 userReq.IsActive,
			// 其他指针字段保持nil，表示未设置
			IsDeleted:          nil,
			IsTwoFactorEnabled: nil,
			TwoFactorSecret:    nil,
			RealNameType:       nil,
			InviteUserID:       nil,
			CommissionType:     nil,
			CommissionRate:     nil,
			InviteCode:         nil,
		}

		if err := h.teamService.AddUsersToTeam(ctx, userModel); err != nil {
			h.logger.Error("添加用户到团队失败", "teamID", teamID, "username", userReq.UserName, "error", err)
			h.HandleForbidden(ctx, err, err.Error())
			return
		}
	}

	h.logger.Info("成功添加用户到团队", "teamID", teamID, "userCount", len(request.Users))
	h.HandleSuccess(ctx, iris.Map{"message": "用户添加到团队成功"})
}

// DeleteUsersFromTeam 根据 teamID 删除多个用户
// @Summary 从团队删除用户
// @Description 根据团队ID删除多个用户
// @Tags 团队模块
// @Accept json
// @Produce json
// @Param request body v1.DeleteUsersFromTeamRequest true "用户ID列表"
// @Success 200 {object} v1.Response
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/teams/users [delete]
func (h *TeamHandler) DeleteUsersFromTeam(ctx iris.Context) {
	// 从 session 获取信息
	sess := ctx.Values().Get("session").(*sessions.Session)
	if sess == nil {
		h.HandleUnauthorized(ctx, "Session不存在")
		return
	}

	teamIDInterface := sess.Get("team_id")
	if teamIDInterface == nil {
		h.HandleUnauthorized(ctx, "Session中缺少团队ID信息")
		return
	}

	var teamID int32
	switch v := teamIDInterface.(type) {
	case int32:
		teamID = v
	case uint:
		teamID = int32(v)
	case int:
		teamID = int32(v)
	default:
		h.HandleUnauthorized(ctx, "团队ID类型无效")
		return
	}

	// 获取当前用户 ID
	userIDInterface := sess.Get("user_id")
	if userIDInterface == nil {
		h.HandleUnauthorized(ctx, "需要登录以进行操作")
		return
	}

	var currentUserID int32
	switch v := userIDInterface.(type) {
	case int32:
		currentUserID = v
	case uint:
		currentUserID = int32(v)
	case int:
		currentUserID = int32(v)
	default:
		h.HandleUnauthorized(ctx, "用户ID类型无效")
		return
	}

	// 获取团队所有者 ID
	ownerIDInterface := sess.Get("owner_id")
	if ownerIDInterface == nil {
		h.HandleUnauthorized(ctx, "Session中缺少团队所有者信息")
		return
	}

	var ownerID int32
	switch v := ownerIDInterface.(type) {
	case int32:
		ownerID = v
	case uint:
		ownerID = int32(v)
	case int:
		ownerID = int32(v)
	default:
		h.HandleUnauthorized(ctx, "所有者ID类型无效")
		return
	}

	var request v1.DeleteUsersFromTeamRequest
	if err := ctx.ReadJSON(&request); err != nil {
		h.HandleBadRequest(ctx, err, "请求数据无效")
		return
	}

	// 过滤用户ID列表，排除当前用户和团队所有者
	filteredUserIDs := make([]int32, 0, len(request.UserIDs))
	for _, id := range request.UserIDs {
		if id != currentUserID && id != ownerID {
			filteredUserIDs = append(filteredUserIDs, id)
		}
	}

	if len(filteredUserIDs) == 0 {
		h.logger.Warn("过滤后无用户可删除", "teamID", teamID)
		h.HandleBadRequest(ctx, nil, "无法删除自己或团队所有者")
		return
	}

	// 调用服务层删除用户
	if err := h.teamService.DeleteUsersFromTeam(ctx, teamID, filteredUserIDs); err != nil {
		h.logger.Error("删除团队用户失败", "teamID", teamID, "error", err)
		h.HandleInternalError(ctx, err, "删除团队用户失败")
		return
	}

	h.logger.Info("成功删除团队用户", "teamID", teamID, "deletedCount", len(filteredUserIDs))
	h.HandleSuccess(ctx, iris.Map{"message": "团队用户删除成功"})
}

// UpdateUsersInTeam 更新 teamID 下的多个用户的信息
// @Summary 更新团队用户信息
// @Description 更新团队下多个用户的信息（只允许更新用户名、密码、角色ID和启用状态）
// @Tags 团队模块
// @Accept json
// @Produce json
// @Param request body v1.UpdateUsersInTeamRequest true "用户信息列表"
// @Success 200 {object} v1.Response
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/teams/users [put]
func (h *TeamHandler) UpdateUsersInTeam(ctx iris.Context) {
	// 从 session 获取 teamID
	sess := ctx.Values().Get("session").(*sessions.Session)
	if sess == nil {
		h.HandleUnauthorized(ctx, "Session不存在")
		return
	}

	teamIDInterface := sess.Get("team_id")
	if teamIDInterface == nil {
		h.HandleUnauthorized(ctx, "Session中缺少团队ID信息")
		return
	}

	var teamID int32
	switch v := teamIDInterface.(type) {
	case int32:
		teamID = v
	case uint:
		teamID = int32(v)
	case int:
		teamID = int32(v)
	default:
		h.HandleUnauthorized(ctx, "团队ID类型无效")
		return
	}

	var request v1.UpdateUsersInTeamRequest
	if err := ctx.ReadJSON(&request); err != nil {
		h.HandleBadRequest(ctx, err, "无效的请求数据")
		return
	}

	// 构造更新数据，只处理允许的字段
	users := make([]*model.User, 0, len(request.Users))
	for _, userReq := range request.Users {
		userModel := &model.User{
			ID:     userReq.ID,
			TeamID: teamID,
		}

		// 仅更新请求中存在的允许字段
		if userReq.UserName != "" {
			userModel.UserName = userReq.UserName
		}
		if userReq.Password != "" {
			userModel.Password = userReq.Password
		}
		if userReq.RoleID != 0 {
			userModel.RoleID = userReq.RoleID
		}

		// IsActive是指针类型，直接赋值
		userModel.IsActive = userReq.IsActive

		users = append(users, userModel)
	}

	// 调用服务层更新用户信息
	if err := h.teamService.UpdateUsersInTeam(ctx, teamID, users); err != nil {
		h.logger.Error("更新团队用户失败", "teamID", teamID, "error", err)
		h.HandleInternalError(ctx, err, "更新团队用户失败")
		return
	}

	h.logger.Info("成功更新团队用户", "teamID", teamID, "userCount", len(users))
	h.HandleSuccess(ctx, iris.Map{"message": "团队用户更新成功"})
}

// UpdateTeam 更新团队信息
// @Summary 更新团队信息
// @Description 更新团队名称等信息
// @Tags 团队模块
// @Accept json
// @Produce json
// @Param request body v1.UpdateTeamRequest true "团队信息"
// @Success 200 {object} v1.Response
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 403 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/teams [put]
func (h *TeamHandler) UpdateTeam(ctx iris.Context) {
	// 获取 session 信息
	sess := ctx.Values().Get("session").(*sessions.Session)
	if sess == nil {
		h.HandleUnauthorized(ctx, "Session不存在")
		return
	}

	teamIDInterface := sess.Get("team_id")
	if teamIDInterface == nil {
		h.HandleUnauthorized(ctx, "Session中缺少团队ID信息")
		return
	}

	var teamID int32
	switch v := teamIDInterface.(type) {
	case int32:
		teamID = v
	case uint:
		teamID = int32(v)
	case int:
		teamID = int32(v)
	default:
		h.HandleUnauthorized(ctx, "团队ID类型无效")
		return
	}

	ownerInterface := sess.Get("owner")
	if ownerInterface == nil {
		h.HandleForbidden(ctx, nil, "您没有权限更新此团队")
		return
	}

	isOwner, ok := ownerInterface.(bool)
	if !ok || !isOwner {
		h.logger.Warn("用户无权限更新团队", "teamID", teamID)
		h.HandleForbidden(ctx, nil, "您没有权限更新此团队")
		return
	}

	var request v1.UpdateTeamRequest
	if err := ctx.ReadJSON(&request); err != nil {
		h.HandleBadRequest(ctx, err, "请求数据无效")
		return
	}

	// 调用服务更新团队信息
	err := h.teamService.UpdateTeam(ctx, teamID, request.Name)
	if err != nil {
		h.logger.Error("更新团队失败", "teamID", teamID, "error", err)
		h.HandleInternalError(ctx, err, "更新团队失败")
		return
	}

	h.logger.Info("团队更新成功", "teamID", teamID, "newName", request.Name)
	h.HandleSuccess(ctx, iris.Map{"message": "团队更新成功"})
}
