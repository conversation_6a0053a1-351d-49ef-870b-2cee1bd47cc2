package user

import (
	"fmt"
	"strings"
	"time"

	v1 "fp-browser/api/v1/user"
	"fp-browser/internal/service/shared"
	"fp-browser/internal/service/user"
	"fp-browser/pkg/log"

	"github.com/kataras/iris/v12"
)

const (
	LoginAuthExpire = 15 * time.Minute // 登录授权过期时间
)

type TeamIPWhitelistHandler struct {
	*Handler
	teamIPWhitelistService user.TeamIPWhitelistService
	redisService           shared.RedisService
}

// NewTeamIPWhitelistHandler 创建 TeamIPWhitelistHandler 实例
func NewTeamIPWhitelistHandler(
	logger *log.Logger,
	teamIPWhitelistService user.TeamIPWhitelistService,
	redisService shared.RedisService,
) *TeamIPWhitelistHandler {
	return &TeamIPWhitelistHandler{
		Handler:                NewHandler(logger),
		teamIPWhitelistService: teamIPWhitelistService,
		redisService:           redisService,
	}
}

// CreateIPWhitelist 创建新的团队IP白名单
// @Summary 创建团队IP白名单
// @Description 创建新的团队IP白名单
// @Tags 团队IP白名单模块
// @Accept json
// @Produce json
// @Param request body v1.CreateTeamIPWhitelistRequest true "IP白名单信息"
// @Success 200 {object} v1.Response
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/team-ip-whitelists [post]
func (h *TeamIPWhitelistHandler) CreateIPWhitelist(ctx iris.Context) {
	// 获取团队ID
	teamID, ok := h.RequireTeamAccess(ctx)
	if !ok {
		return
	}

	// 获取用户名用于日志
	username, _ := h.GetUsernameFromSession(ctx)

	// 解析请求数据
	var request v1.CreateTeamIPWhitelistRequest
	if err := ctx.ReadJSON(&request); err != nil {
		h.HandleBadRequest(ctx, err, "请求数据无效")
		return
	}

	// 调用服务层处理业务逻辑
	if err := h.teamIPWhitelistService.CreateIPWhitelist(ctx, request, teamID); err != nil {
		h.logger.Error("创建IP白名单失败", "teamID", teamID, "username", username, "ipAddress", request.IPAddress, "error", err)
		h.HandleInternalError(ctx, err, "创建IP白名单失败")
		return
	}

	h.logger.Info("IP白名单创建成功", "teamID", teamID, "username", username, "ipAddress", request.IPAddress)
	_ = ctx.JSON(iris.Map{"message": "IP白名单创建成功"})
}

// CreateIPWhitelists 批量创建团队IP白名单
// @Summary 批量创建团队IP白名单
// @Description 批量创建多个团队IP白名单
// @Tags 团队IP白名单模块
// @Accept json
// @Produce json
// @Param request body v1.CreateTeamIPWhitelistsRequest true "IP白名单信息列表"
// @Success 200 {object} v1.Response
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/team-ip-whitelists/batch [post]
func (h *TeamIPWhitelistHandler) CreateIPWhitelists(ctx iris.Context) {
	// 获取团队ID
	teamID, ok := h.RequireTeamAccess(ctx)
	if !ok {
		return
	}

	// 获取用户名用于日志
	username, _ := h.GetUsernameFromSession(ctx)

	// 解析请求数据
	var request v1.CreateTeamIPWhitelistsRequest
	if err := ctx.ReadJSON(&request); err != nil {
		h.HandleBadRequest(ctx, err, "请求数据无效")
		return
	}

	// 调用服务层处理业务逻辑
	if err := h.teamIPWhitelistService.CreateIPWhitelists(ctx, request, teamID); err != nil {
		h.logger.Error("批量创建IP白名单失败", "teamID", teamID, "username", username, "count", len(request.IPAddresses), "error", err)
		h.HandleInternalError(ctx, err, "批量创建IP白名单失败")
		return
	}

	h.logger.Info("批量创建IP白名单成功", "teamID", teamID, "username", username, "count", len(request.IPAddresses))
	_ = ctx.JSON(iris.Map{"message": "批量创建IP白名单成功"})
}

// DeleteIPWhitelists 删除团队IP白名单
// @Summary 删除团队IP白名单
// @Description 删除一个或多个团队IP白名单
// @Tags 团队IP白名单模块
// @Accept json
// @Produce json
// @Param request body v1.DeleteTeamIPWhitelistRequest true "IP白名单ID列表"
// @Success 200 {object} v1.Response
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/team-ip-whitelists [delete]
func (h *TeamIPWhitelistHandler) DeleteIPWhitelists(ctx iris.Context) {
	// 获取团队ID
	teamID, ok := h.RequireTeamAccess(ctx)
	if !ok {
		return
	}

	// 获取用户名用于日志
	username, _ := h.GetUsernameFromSession(ctx)

	// 解析请求数据
	var request v1.DeleteTeamIPWhitelistRequest
	if err := ctx.ReadJSON(&request); err != nil || len(request.IDs) == 0 {
		h.HandleBadRequest(ctx, err, "请求数据无效或缺少有效的IP白名单ID")
		return
	}

	// 调用服务层删除操作
	if err := h.teamIPWhitelistService.DeleteIPWhitelists(ctx, request, teamID); err != nil {
		h.logger.Error("删除IP白名单失败", "teamID", teamID, "username", username, "whitelistIDs", request.IDs, "error", err)
		h.HandleInternalError(ctx, err, "删除IP白名单失败")
		return
	}

	h.logger.Info("IP白名单删除成功", "teamID", teamID, "username", username, "whitelistIDs", request.IDs)
	_ = ctx.JSON(iris.Map{"message": "IP白名单删除成功"})
}

// GetIPWhitelists 获取团队IP白名单列表
// @Summary 获取团队IP白名单列表
// @Description 获取当前团队的所有IP白名单记录
// @Tags 团队IP白名单模块
// @Accept json
// @Produce json
// @Success 200 {object} v1.GetTeamIPWhitelistsResponse
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/team-ip-whitelists [get]
func (h *TeamIPWhitelistHandler) GetIPWhitelists(ctx iris.Context) {
	// 获取团队ID
	teamID, ok := h.RequireTeamAccess(ctx)
	if !ok {
		return
	}

	// 获取用户名用于日志
	username, _ := h.GetUsernameFromSession(ctx)

	// 调用服务层获取IP白名单列表
	whitelists, err := h.teamIPWhitelistService.GetIPWhitelists(ctx, teamID)
	if err != nil {
		h.logger.Error("获取IP白名单列表失败", "teamID", teamID, "username", username, "error", err)
		h.HandleInternalError(ctx, err, "获取IP白名单列表失败")
		return
	}

	response := v1.GetTeamIPWhitelistsResponse{
		Whitelists: whitelists,
		Total:      int64(len(whitelists)),
	}

	h.logger.Info("IP白名单列表获取成功", "teamID", teamID, "username", username, "total", len(whitelists))
	h.HandleSuccess(ctx, response)
}

// GetPendingLoginRequests 获取待授权的登录请求列表
// @Summary 获取待授权登录请求
// @Description 获取当前团队所有待授权的登录请求
// @Tags 团队IP白名单模块
// @Accept json
// @Produce json
// @Success 200 {object} v1.GetPendingLoginRequestsResponse
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/team-ip-whitelists/pending-logins [get]
func (h *TeamIPWhitelistHandler) GetPendingLoginRequests(ctx iris.Context) {
	// 获取团队ID
	teamID, ok := h.RequireTeamAccess(ctx)
	if !ok {
		return
	}

	// 获取用户名用于日志
	username, _ := h.GetUsernameFromSession(ctx)

	// 查找Redis中所有匹配的登录请求
	pattern := fmt.Sprintf("login_auth:%d:*:login", teamID)
	pendingRequests, err := h.scanLoginRequests(ctx, pattern)
	if err != nil {
		h.logger.Error("获取待授权登录请求失败", "teamID", teamID, "username", username, "error", err)
		h.HandleInternalError(ctx, err, "获取待授权登录请求失败")
		return
	}

	response := v1.GetPendingLoginRequestsResponse{
		Requests: pendingRequests,
		Total:    int64(len(pendingRequests)),
	}

	h.logger.Info("获取待授权登录请求成功", "teamID", teamID, "username", username, "total", len(pendingRequests))
	h.HandleSuccess(ctx, response)
}

// AuthorizeLoginRequest 授权登录请求
// @Summary 授权登录请求
// @Description 授权用户登录请求
// @Tags 团队IP白名单模块
// @Accept json
// @Produce json
// @Param request body v1.AuthorizeLoginRequest true "授权请求"
// @Success 200 {object} v1.Response
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/team-ip-whitelists/authorize-login [post]
func (h *TeamIPWhitelistHandler) AuthorizeLoginRequest(ctx iris.Context) {
	// 获取团队ID
	teamID, ok := h.RequireTeamAccess(ctx)
	if !ok {
		return
	}

	// 获取当前用户名（用于记录日志）
	currentUsername, ok := h.GetUsernameFromSession(ctx)
	if !ok {
		h.HandleUnauthorized(ctx, "获取用户信息失败")
		return
	}

	// 解析请求数据
	var request v1.AuthorizeLoginRequest
	if err := ctx.ReadJSON(&request); err != nil {
		h.HandleBadRequest(ctx, err, "请求数据无效")
		return
	}

	if request.Username == "" {
		h.HandleBadRequest(ctx, nil, "用户名不能为空")
		return
	}

	// 构建Redis key
	authKey := fmt.Sprintf("login_auth:%d:%s:login", teamID, request.Username)

	// 检查授权记录是否存在
	currentValue, err := h.redisService.Get(ctx, authKey)
	if err != nil {
		if h.redisService.IsNil(err) {
			h.HandleBadRequest(ctx, nil, "未找到该用户的登录请求")
			return
		}
		h.logger.Error("查询登录请求失败", "teamID", teamID, "authorizer", currentUsername, "targetUsername", request.Username, "error", err)
		h.HandleInternalError(ctx, err, "查询登录请求失败")
		return
	}

	// 如果已经是授权状态，返回提示
	if currentValue == "true" {
		h.HandleBadRequest(ctx, nil, "该登录请求已经被授权")
		return
	}

	// 设置授权记录为true，保持原有的过期时间
	if err := h.redisService.Set(ctx, authKey, "true", LoginAuthExpire); err != nil {
		h.logger.Error("设置登录授权失败", "teamID", teamID, "authorizer", currentUsername, "targetUsername", request.Username, "error", err)
		h.HandleInternalError(ctx, err, "设置登录授权失败")
		return
	}

	h.logger.Info("登录授权设置成功", "teamID", teamID, "authorizer", currentUsername, "targetUsername", request.Username)
	_ = ctx.JSON(iris.Map{"message": "登录授权设置成功"})
}

// scanLoginRequests 扫描Redis中匹配的登录请求
func (h *TeamIPWhitelistHandler) scanLoginRequests(ctx iris.Context, pattern string) ([]v1.PendingLoginRequest, error) {
	var pendingRequests []v1.PendingLoginRequest

	// 使用Redis SCAN命令查找匹配的key
	keys, err := h.redisService.Scan(ctx, pattern)
	if err != nil {
		return nil, fmt.Errorf("扫描Redis key失败: %w", err)
	}

	// 遍历每个key，获取详细信息
	for _, key := range keys {
		// 解析key获取用户名：login_auth:{teamID}:{username}:login
		parts := strings.Split(key, ":")
		if len(parts) != 4 {
			h.logger.Warn("Redis key格式异常", "key", key)
			continue
		}
		username := parts[2]

		// 获取值（状态）
		value, err := h.redisService.Get(ctx, key)
		if err != nil {
			if h.redisService.IsNil(err) {
				// key已过期或被删除，跳过
				continue
			}
			h.logger.Warn("获取Redis值失败", "key", key, "error", err)
			continue
		}

		// 获取TTL
		ttl, err := h.redisService.TTL(ctx, key)
		if err != nil {
			h.logger.Warn("获取TTL失败", "key", key, "error", err)
			ttl = 0
		}

		// 计算请求时间（估算：当前时间 - 剩余TTL时间）
		requestAt := time.Now().Add(-LoginAuthExpire + ttl)

		// 确定状态
		status := "pending"
		if value == "true" {
			status = "authorized"
		}

		pendingRequest := v1.PendingLoginRequest{
			Username:  username,
			RequestAt: requestAt,
			Status:    status,
			TTL:       int64(ttl.Seconds()),
		}

		pendingRequests = append(pendingRequests, pendingRequest)
	}

	return pendingRequests, nil
}
