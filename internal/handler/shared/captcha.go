package shared

import (
	v1 "fp-browser/api/v1/shared"
	"fp-browser/internal/service/shared"
	"fp-browser/pkg/log"
	"github.com/kataras/iris/v12"
	"time"
)

const (
	CaptchaExpire = 2 * time.Minute // 验证码有效期
)

type CaptchaHandler struct {
	*Handler
	captchaService shared.CaptchaService
	redisService   shared.RedisService
}

// NewCaptchaHandler 创建 CaptchaHandler 实例
func NewCaptchaHandler(logger *log.Logger, captchaService shared.CaptchaService, redisService shared.RedisService) *CaptchaHandler {
	return &CaptchaHandler{
		Handler:        NewHandler(logger),
		captchaService: captchaService,
		redisService:   redisService,
	}
}

// Generate 生成验证码 + PoW挑战
// @Summary 生成验证码
// @Description 生成验证码图片和PoW挑战，返回验证码图片、种子和难度目标
// @Tags 验证码模块
// @Accept json
// @Produce json
// @Success 200 {object} v1.Response{data=v1.GenerateCaptchaData}
// @Failure 500 {object} v1.Response
// @Router /api/v1/captcha/generate [post]
func (h *CaptchaHandler) Generate(ctx iris.Context) {
	// 调用服务生成验证码
	response, err := h.captchaService.GenerateCaptcha()
	if err != nil {
		h.logger.Error("生成验证码失败", "error", err)
		h.HandleInternalError(ctx, err, "生成验证码失败")
		return
	}

	// 构建redis key：使用seed和target组合
	redisKey := "captcha:" + response.Seed + ":" + response.Target

	// 将验证码答案存储到redis
	if err := h.redisService.Set(ctx, redisKey, response.Answer, CaptchaExpire); err != nil {
		h.logger.Error("保存验证码到redis失败", "key", redisKey, "error", err)
		h.HandleInternalError(ctx, err, "保存验证码失败")
		return
	}

	// 返回响应（不包含答案）
	result := v1.GenerateCaptchaData{
		CaptchaImage: response.CaptchaImage,
		Seed:         response.Seed,
		Target:       response.Target,
	}

	h.logger.Info("成功生成验证码", "seed", response.Seed, "target", response.Target)
	h.HandleSuccess(ctx, result)
}

// Verify 校验验证码 + PoW
// @Summary 验证验证码
// @Description 验证验证码答案和PoW工作量证明
// @Tags 验证码模块
// @Accept json
// @Produce json
// @Param request body v1.VerifyCaptchaRequest true "验证请求"
// @Success 200 {object} v1.Response{data=v1.VerifyCaptchaData}
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/captcha/verify [post]
func (h *CaptchaHandler) Verify(ctx iris.Context) {
	var req v1.VerifyCaptchaRequest
	if err := ctx.ReadJSON(&req); err != nil {
		h.logger.Warn("解析验证请求失败", "error", err)
		h.HandleBadRequest(ctx, err, "请求参数格式错误")
		return
	}

	// 验证必要参数
	if req.Seed == "" {
		h.HandleBadRequest(ctx, nil, "seed不能为空")
		return
	}
	if req.Answer == "" {
		h.HandleBadRequest(ctx, nil, "answer不能为空")
		return
	}
	if req.Nonce == "" {
		h.HandleBadRequest(ctx, nil, "nonce不能为空")
		return
	}
	if req.Target == "" {
		h.HandleBadRequest(ctx, nil, "target不能为空")
		return
	}

	// 构建redis key
	redisKey := "captcha:" + req.Seed + ":" + req.Target

	// 从redis获取验证码答案
	expectedAnswer, err := h.redisService.Get(ctx, redisKey)
	if err != nil {
		if h.redisService.IsNil(err) {
			h.logger.Warn("验证码不存在或已过期", "seed", req.Seed, "target", req.Target)
			h.HandleUnauthorized(ctx, "验证码不存在或已过期")
			return
		}
		h.logger.Error("从redis获取验证码失败", "key", redisKey, "error", err)
		h.HandleInternalError(ctx, err, "验证码查询失败")
		return
	}

	// 验证验证码答案
	if req.Answer != expectedAnswer {
		h.logger.Warn("验证码答案错误", "seed", req.Seed, "expected", expectedAnswer, "actual", req.Answer)
		h.HandleUnauthorized(ctx, "验证码错误")
		return
	}

	// 验证PoW工作量证明
	if !h.captchaService.VerifyPoW(req.Seed, req.Nonce, req.Target) {
		h.logger.Warn("PoW校验失败", "seed", req.Seed, "nonce", req.Nonce, "target", req.Target)
		h.HandleUnauthorized(ctx, "PoW校验失败")
		return
	}

	// 验证通过，删除redis中的验证码（防止重复使用）
	if err := h.redisService.Delete(ctx, redisKey); err != nil {
		h.logger.Warn("删除验证码失败", "key", redisKey, "error", err)
		// 不影响主流程，只记录警告
	}

	response := v1.VerifyCaptchaData{
		Valid: true,
	}

	h.logger.Info("验证码验证成功", "seed", req.Seed, "target", req.Target)
	h.HandleSuccess(ctx, response)
}
