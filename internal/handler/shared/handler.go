package shared

import (
	"fmt"
	"fp-browser/pkg/log"

	"github.com/kataras/iris/v12"
	"github.com/kataras/iris/v12/sessions"
)

type Handler struct {
	logger *log.Logger
}

func NewHandler(logger *log.Logger) *Handler {
	return &Handler{
		logger: logger,
	}
}

// GetUserIDFromSession 从session中获取用户ID
func (h *Handler) GetUserIDFromSession(ctx iris.Context) (int32, bool) {
	sess := ctx.Values().Get("session").(*sessions.Session)
	if sess == nil {
		return 0, false
	}

	userID, ok := sess.Get("user_id").(int32)
	return userID, ok
}

// GetTeamIDFromSession 从session中获取团队ID
func (h *Handler) GetTeamIDFromSession(ctx iris.Context) (int32, bool) {
	sess := ctx.Values().Get("session").(*sessions.Session)
	if sess == nil {
		return 0, false
	}

	teamID, ok := sess.Get("team_id").(int32)
	return teamID, ok
}

// GetTeamIDFromSessionWithError 从session中获取团队ID并进行类型转换，返回详细错误信息
func (h *Handler) GetTeamIDFromSessionWithError(ctx iris.Context) (int32, error) {
	sess := ctx.Values().Get("session").(*sessions.Session)
	if sess == nil {
		return 0, fmt.Errorf("Session不存在")
	}

	teamIDInterface := sess.Get("team_id")
	if teamIDInterface == nil {
		return 0, fmt.Errorf("Session中缺少团队ID信息")
	}

	// 统一的类型转换处理
	var teamID int32
	switch v := teamIDInterface.(type) {
	case int32:
		teamID = v
	case uint:
		teamID = int32(v)
	case int:
		teamID = int32(v)
	default:
		return 0, fmt.Errorf("团队ID类型无效")
	}

	return teamID, nil
}

// HandleUnauthorized 处理未授权错误
func (h *Handler) HandleUnauthorized(ctx iris.Context, message string) {
	h.logger.Error(message)
	ctx.StatusCode(iris.StatusUnauthorized)
	_ = ctx.JSON(iris.Map{"error": "未授权访问"})
}

// HandleBadRequest 处理请求参数错误
func (h *Handler) HandleBadRequest(ctx iris.Context, err error, message string) {
	h.logger.Error(message, "error", err)
	ctx.StatusCode(iris.StatusBadRequest)
	_ = ctx.JSON(iris.Map{"error": message})
}

// HandleInternalError 处理内部服务器错误
func (h *Handler) HandleInternalError(ctx iris.Context, err error, message string) {
	h.logger.Error(message, "error", err)
	ctx.StatusCode(iris.StatusInternalServerError)
	_ = ctx.JSON(iris.Map{"error": message})
}

// HandleSuccess 处理成功响应
func (h *Handler) HandleSuccess(ctx iris.Context, data interface{}) {
	_ = ctx.JSON(data)
}
