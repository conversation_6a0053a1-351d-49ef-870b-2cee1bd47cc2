package shared

import (
	"crypto/rand"
	"fmt"
	"html/template"
	"io"
	"math/big"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	v1 "fp-browser/api/v1/shared"
	"fp-browser/internal/service/shared"
	"fp-browser/pkg/log"
	"github.com/kataras/iris/v12"
)

const (
	VerificationCodeExpire = 10 * time.Minute // 验证码有效期
	EmailCooldownExpire    = 60 * time.Second // 邮件发送冷却时间
	EmailCounterExpire     = 1 * time.Hour    // 邮件计数器有效期
	MaxEmailsPerHour       = 5                // 每小时最大发送次数
)

type EmailHandler struct {
	*Handler
	captchaService shared.CaptchaService
	redisService   shared.RedisService
	emailService   shared.EmailService
}

// NewEmailHandler 创建 EmailHandler 实例
func NewEmailHandler(
	logger *log.Logger,
	captchaService shared.CaptchaService,
	redisService shared.RedisService,
	emailService shared.EmailService,
) *EmailHandler {
	return &EmailHandler{
		Handler:        <PERSON>Hand<PERSON>(logger),
		captchaService: captchaService,
		redisService:   redisService,
		emailService:   emailService,
	}
}

// generateVerificationCode 生成6位随机验证码
func (h *EmailHandler) generateVerificationCode() (string, error) {
	// 生成100000-999999之间的随机数
	min := big.NewInt(100000)
	max := big.NewInt(999999)

	n, err := rand.Int(rand.Reader, new(big.Int).Sub(max, min))
	if err != nil {
		return "", fmt.Errorf("生成随机数失败: %w", err)
	}

	return new(big.Int).Add(min, n).String(), nil
}

// checkEmailRateLimit 检查邮件发送频率限制
func (h *EmailHandler) checkEmailRateLimit(ctx iris.Context, email string) error {
	// 检查冷却时间（60秒内不能重复发送）
	cooldownKey := fmt.Sprintf("email_cooldown:%s", email)
	_, err := h.redisService.Get(ctx, cooldownKey)
	if err != nil && !h.redisService.IsNil(err) {
		return fmt.Errorf("检查邮件冷却时间失败: %w", err)
	}
	if err == nil {
		// key存在，说明在冷却期内
		return fmt.Errorf("发送过于频繁，请等待60秒后再试")
	}

	// 检查每小时发送次数限制
	counterKey := fmt.Sprintf("email_counter:%s", email)
	countStr, err := h.redisService.Get(ctx, counterKey)
	if err != nil && !h.redisService.IsNil(err) {
		return fmt.Errorf("检查邮件发送次数失败: %w", err)
	}

	if countStr != "" {
		count, err := strconv.Atoi(countStr)
		if err != nil {
			h.logger.Error("解析邮件计数器失败", "email", email, "countStr", countStr, "error", err)
			// 重置计数器
			if delErr := h.redisService.Delete(ctx, counterKey); delErr != nil {
				h.logger.Error("重置邮件计数器失败", "email", email, "error", delErr)
			}
		} else if count >= MaxEmailsPerHour {
			return fmt.Errorf("发送次数超限，每小时最多发送%d封邮件", MaxEmailsPerHour)
		}
	}

	return nil
}

// updateEmailRateLimit 更新邮件发送频率限制记录
func (h *EmailHandler) updateEmailRateLimit(ctx iris.Context, email string) error {
	// 设置冷却时间记录
	cooldownKey := fmt.Sprintf("email_cooldown:%s", email)
	if err := h.redisService.Set(ctx, cooldownKey, "1", EmailCooldownExpire); err != nil {
		h.logger.Error("设置邮件冷却时间失败", "email", email, "error", err)
		// 不影响主流程，继续执行
	}

	// 更新发送次数记录
	counterKey := fmt.Sprintf("email_counter:%s", email)
	countStr, err := h.redisService.Get(ctx, counterKey)
	if err != nil && !h.redisService.IsNil(err) {
		h.logger.Error("获取邮件计数器失败", "email", email, "error", err)
		// 设置为1
		if setErr := h.redisService.Set(ctx, counterKey, "1", EmailCounterExpire); setErr != nil {
			h.logger.Error("设置邮件计数器失败", "email", email, "error", setErr)
		}
		return nil
	}

	var newCount int
	if countStr == "" {
		newCount = 1
	} else {
		count, err := strconv.Atoi(countStr)
		if err != nil {
			h.logger.Error("解析邮件计数器失败", "email", email, "countStr", countStr, "error", err)
			newCount = 1
		} else {
			newCount = count + 1
		}
	}

	if err := h.redisService.Set(ctx, counterKey, strconv.Itoa(newCount), EmailCounterExpire); err != nil {
		h.logger.Error("更新邮件计数器失败", "email", email, "newCount", newCount, "error", err)
	}

	h.logger.Debug("邮件计数器已更新", "email", email, "count", newCount)
	return nil
}

// getEmailTextContent 根据邮件类型生成纯文本内容
func (h *EmailHandler) getEmailTextContent(emailType, verificationCode string) string {
	switch emailType {
	case "login":
		return fmt.Sprintf(`验证您的登录

您正在尝试登录您的账户，请使用以下验证码完成登录：

验证码：%s

验证码将在10分钟内有效。如果您没有尝试登录，请立即联系我们的客服团队。

为了您的账户安全，请勿将验证码泄露给他人。

谢谢，
PrismBrowser`, verificationCode)

	case "register":
		return fmt.Sprintf(`验证您的账户

感谢您注册我们的服务！请使用以下验证码完成注册：

验证码：%s

验证码将在10分钟内有效。如果您没有请求此验证码，请忽略此邮件。

如有任何问题，请随时联系我们的客服团队。

谢谢，
PrismBrowser`, verificationCode)

	case "reset":
		return fmt.Sprintf(`重置您的密码

您已请求重置密码，请使用以下验证码完成密码重置：

验证码：%s

验证码将在10分钟内有效。如果您没有请求重置密码，请忽略此邮件并考虑更改您的密码以确保账户安全。

为了您的账户安全，请勿将验证码泄露给他人。

谢谢，
PrismBrowser`, verificationCode)

	case "update":
		return fmt.Sprintf(`验证您的新邮箱

您正在修改账户绑定的邮箱地址，请使用以下验证码完成邮箱更换：

验证码：%s

验证码将在10分钟内有效。如果您没有请求修改邮箱，请立即联系我们的客服团队。

为了您的账户安全，请确保是您本人操作。如果不是您的操作，请立即修改账户密码。

谢谢，
PrismBrowser`, verificationCode)

	default:
		return fmt.Sprintf(`验证您的操作

请使用以下验证码完成您的操作：

验证码：%s

验证码将在10分钟内有效。如果您没有请求此验证码，请忽略此邮件。

谢谢，
PrismBrowser`, verificationCode)
	}
}

// getEmailSubject 根据邮件类型获取邮件主题
func (h *EmailHandler) getEmailSubject(emailType string) string {
	switch emailType {
	case "login":
		return "验证您的登录"
	case "register":
		return "验证您的账户"
	case "reset":
		return "重置您的密码"
	case "update":
		return "验证您的新邮箱"
	default:
		return "验证您的操作"
	}
}

// loadEmailTemplate 根据邮件类型加载对应的HTML模板
func (h *EmailHandler) loadEmailTemplate(emailType string) (string, error) {
	templatePath := filepath.Join("storage", "templates", "zh-CN", fmt.Sprintf("%s.html", emailType))

	// 检查文件是否存在
	if _, err := os.Stat(templatePath); os.IsNotExist(err) {
		return "", fmt.Errorf("邮件模板文件不存在: %s", templatePath)
	}

	// 读取模板文件
	file, err := os.Open(templatePath)
	if err != nil {
		return "", fmt.Errorf("打开模板文件失败: %w", err)
	}
	defer file.Close()

	content, err := io.ReadAll(file)
	if err != nil {
		return "", fmt.Errorf("读取模板文件失败: %w", err)
	}

	return string(content), nil
}

// verifyCaptchaAndPoW 验证验证码和PoW
func (h *EmailHandler) verifyCaptchaAndPoW(ctx iris.Context, req *v1.SendVerificationEmailRequest) error {
	// 构建redis key
	redisKey := "captcha:" + req.Seed + ":" + req.Target

	// 从redis获取验证码答案
	expectedAnswer, err := h.redisService.Get(ctx, redisKey)
	if err != nil {
		if h.redisService.IsNil(err) {
			return fmt.Errorf("验证码不存在或已过期")
		}
		return fmt.Errorf("验证码查询失败: %w", err)
	}

	// 验证验证码答案
	if req.Answer != expectedAnswer {
		return fmt.Errorf("验证码错误")
	}

	// 验证PoW工作量证明
	if !h.captchaService.VerifyPoW(req.Seed, req.Nonce, req.Target) {
		return fmt.Errorf("PoW校验失败")
	}

	// 验证通过，删除redis中的验证码（防止重复使用）
	if err := h.redisService.Delete(ctx, redisKey); err != nil {
		h.logger.Warn("删除验证码失败", "key", redisKey, "error", err)
		// 不影响主流程，只记录警告
	}

	return nil
}

// sendVerificationEmail 发送验证邮件
func (h *EmailHandler) sendVerificationEmail(ctx iris.Context, email, emailType string) error {
	// 验证邮箱格式
	if err := h.emailService.ValidateEmail(email); err != nil {
		return fmt.Errorf("邮箱格式无效: %w", err)
	}

	// 检查频率限制
	if err := h.checkEmailRateLimit(ctx, email); err != nil {
		return err
	}

	// 生成6位验证码
	verificationCode, err := h.generateVerificationCode()
	if err != nil {
		return fmt.Errorf("生成验证码失败: %w", err)
	}

	// 存储验证码到redis
	redisKey := fmt.Sprintf("email_verification:%s:%s", emailType, email)
	if err := h.redisService.Set(ctx, redisKey, verificationCode, VerificationCodeExpire); err != nil {
		return fmt.Errorf("保存验证码失败: %w", err)
	}

	// 加载对应的HTML模板
	templateContent, err := h.loadEmailTemplate(emailType)
	if err != nil {
		return fmt.Errorf("加载邮件模板失败: %w", err)
	}

	// 渲染HTML模板（只替换验证码）
	tpl, err := template.New("email").Parse(templateContent)
	if err != nil {
		return fmt.Errorf("解析邮件模板失败: %w", err)
	}

	var htmlBody strings.Builder
	templateData := struct {
		Code string
	}{
		Code: verificationCode,
	}

	if err := tpl.Execute(&htmlBody, templateData); err != nil {
		return fmt.Errorf("渲染邮件模板失败: %w", err)
	}

	// 根据邮件类型生成纯文本内容
	textBody := h.getEmailTextContent(emailType, verificationCode)

	// 根据邮件类型获取邮件主题
	subject := h.getEmailSubject(emailType)

	// 发送邮件
	if err := h.emailService.SendHTMLEmail(ctx, email, subject, htmlBody.String(), textBody); err != nil {
		return fmt.Errorf("发送邮件失败: %w", err)
	}

	// 更新频率限制记录
	if err := h.updateEmailRateLimit(ctx, email); err != nil {
		h.logger.Error("更新邮件频率限制失败", "email", email, "error", err)
		// 不影响主流程，只记录错误
	}

	return nil
}

// SendLoginVerification 发送登录验证邮件
// @Summary 发送登录验证邮件
// @Description 验证captcha和PoW后，发送登录验证邮件
// @Tags 邮件模块
// @Accept json
// @Produce json
// @Param request body v1.SendVerificationEmailRequest true "发送验证邮件请求"
// @Success 200 {object} v1.Response{data=v1.SendVerificationEmailData}
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/email/send-login-verification [post]
func (h *EmailHandler) SendLoginVerification(ctx iris.Context) {
	var req v1.SendVerificationEmailRequest
	if err := ctx.ReadJSON(&req); err != nil {
		h.logger.Warn("解析请求失败", "error", err)
		h.HandleBadRequest(ctx, err, "请求参数格式错误")
		return
	}

	// 验证验证码和PoW
	if err := h.verifyCaptchaAndPoW(ctx, &req); err != nil {
		h.logger.Warn("验证码或PoW验证失败", "email", req.Email, "error", err)
		h.HandleUnauthorized(ctx, err.Error())
		return
	}

	// 发送登录验证邮件
	if err := h.sendVerificationEmail(ctx, req.Email, "login"); err != nil {
		h.logger.Error("发送登录验证邮件失败", "email", req.Email, "error", err)
		// 根据错误类型返回不同的状态码
		if strings.Contains(err.Error(), "发送过于频繁") || strings.Contains(err.Error(), "发送次数超限") {
			h.HandleBadRequest(ctx, err, err.Error())
		} else {
			h.HandleInternalError(ctx, err, "发送邮件失败")
		}
		return
	}

	response := v1.SendVerificationEmailData{
		Message: "登录验证邮件发送成功",
		Sent:    true,
	}

	h.logger.Info("登录验证邮件发送成功", "email", req.Email)
	h.HandleSuccess(ctx, response)
}

// SendRegisterVerification 发送注册验证邮件
// @Summary 发送注册验证邮件
// @Description 验证captcha和PoW后，发送注册验证邮件
// @Tags 邮件模块
// @Accept json
// @Produce json
// @Param request body v1.SendVerificationEmailRequest true "发送验证邮件请求"
// @Success 200 {object} v1.Response{data=v1.SendVerificationEmailData}
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/email/send-register-verification [post]
func (h *EmailHandler) SendRegisterVerification(ctx iris.Context) {
	var req v1.SendVerificationEmailRequest
	if err := ctx.ReadJSON(&req); err != nil {
		h.logger.Warn("解析请求失败", "error", err)
		h.HandleBadRequest(ctx, err, "请求参数格式错误")
		return
	}

	// 验证验证码和PoW
	if err := h.verifyCaptchaAndPoW(ctx, &req); err != nil {
		h.logger.Warn("验证码或PoW验证失败", "email", req.Email, "error", err)
		h.HandleUnauthorized(ctx, err.Error())
		return
	}

	// 发送注册验证邮件
	if err := h.sendVerificationEmail(ctx, req.Email, "register"); err != nil {
		h.logger.Error("发送注册验证邮件失败", "email", req.Email, "error", err)
		// 根据错误类型返回不同的状态码
		if strings.Contains(err.Error(), "发送过于频繁") || strings.Contains(err.Error(), "发送次数超限") {
			h.HandleBadRequest(ctx, err, err.Error())
		} else {
			h.HandleInternalError(ctx, err, "发送邮件失败")
		}
		return
	}

	response := v1.SendVerificationEmailData{
		Message: "注册验证邮件发送成功",
		Sent:    true,
	}

	h.logger.Info("注册验证邮件发送成功", "email", req.Email)
	h.HandleSuccess(ctx, response)
}

// SendResetPasswordVerification 发送重置密码验证邮件
// @Summary 发送重置密码验证邮件
// @Description 验证captcha和PoW后，发送重置密码验证邮件
// @Tags 邮件模块
// @Accept json
// @Produce json
// @Param request body v1.SendVerificationEmailRequest true "发送验证邮件请求"
// @Success 200 {object} v1.Response{data=v1.SendVerificationEmailData}
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/email/send-reset-verification [post]
func (h *EmailHandler) SendResetPasswordVerification(ctx iris.Context) {
	var req v1.SendVerificationEmailRequest
	if err := ctx.ReadJSON(&req); err != nil {
		h.logger.Warn("解析请求失败", "error", err)
		h.HandleBadRequest(ctx, err, "请求参数格式错误")
		return
	}

	// 验证验证码和PoW
	if err := h.verifyCaptchaAndPoW(ctx, &req); err != nil {
		h.logger.Warn("验证码或PoW验证失败", "email", req.Email, "error", err)
		h.HandleUnauthorized(ctx, err.Error())
		return
	}

	// 发送重置密码验证邮件
	if err := h.sendVerificationEmail(ctx, req.Email, "reset"); err != nil {
		h.logger.Error("发送重置密码验证邮件失败", "email", req.Email, "error", err)
		// 根据错误类型返回不同的状态码
		if strings.Contains(err.Error(), "发送过于频繁") || strings.Contains(err.Error(), "发送次数超限") {
			h.HandleBadRequest(ctx, err, err.Error())
		} else {
			h.HandleInternalError(ctx, err, "发送邮件失败")
		}
		return
	}

	response := v1.SendVerificationEmailData{
		Message: "重置密码验证邮件发送成功",
		Sent:    true,
	}

	h.logger.Info("重置密码验证邮件发送成功", "email", req.Email)
	h.HandleSuccess(ctx, response)
}

// SendUpdateEmailVerification 发送修改邮箱验证邮件
// @Summary 发送修改邮箱验证邮件
// @Description 验证captcha和PoW后，发送修改邮箱验证邮件
// @Tags 邮件模块
// @Accept json
// @Produce json
// @Param request body v1.SendVerificationEmailRequest true "发送验证邮件请求"
// @Success 200 {object} v1.Response{data=v1.SendVerificationEmailData}
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/email/send-update-verification [post]
func (h *EmailHandler) SendUpdateEmailVerification(ctx iris.Context) {
	var req v1.SendVerificationEmailRequest
	if err := ctx.ReadJSON(&req); err != nil {
		h.logger.Warn("解析请求失败", "error", err)
		h.HandleBadRequest(ctx, err, "请求参数格式错误")
		return
	}

	// 验证验证码和PoW
	if err := h.verifyCaptchaAndPoW(ctx, &req); err != nil {
		h.logger.Warn("验证码或PoW验证失败", "email", req.Email, "error", err)
		h.HandleUnauthorized(ctx, err.Error())
		return
	}

	// 发送修改邮箱验证邮件
	if err := h.sendVerificationEmail(ctx, req.Email, "update"); err != nil {
		h.logger.Error("发送修改邮箱验证邮件失败", "email", req.Email, "error", err)
		// 根据错误类型返回不同的状态码
		if strings.Contains(err.Error(), "发送过于频繁") || strings.Contains(err.Error(), "发送次数超限") {
			h.HandleBadRequest(ctx, err, err.Error())
		} else {
			h.HandleInternalError(ctx, err, "发送邮件失败")
		}
		return
	}

	response := v1.SendVerificationEmailData{
		Message: "修改邮箱验证邮件发送成功",
		Sent:    true,
	}

	h.logger.Info("修改邮箱验证邮件发送成功", "email", req.Email)
	h.HandleSuccess(ctx, response)
}
