package auth

import (
	"fmt"
	"fp-browser/api/v1/auth"
	"fp-browser/pkg/log"
	"github.com/kataras/iris/v12"
	"net"
	"strings"
)

type Handler struct {
	logger *log.Logger
}

func NewHandler(logger *log.Logger) *Handler {
	return &Handler{
		logger: logger,
	}
}

// HandleUnauthorized 处理未授权错误
func (h *Handler) HandleUnauthorized(ctx iris.Context, message string) {
	h.logger.Error(message)
	ctx.StatusCode(iris.StatusUnauthorized)
	_ = ctx.JSON(auth.Response{
		Code:    401,
		Message: message,
		Data:    nil,
	})
}

// HandleBadRequest 处理请求参数错误
func (h *Handler) HandleBadRequest(ctx iris.Context, err error, message string) {
	h.logger.Error(message, "error", err)
	ctx.StatusCode(iris.StatusBadRequest)
	_ = ctx.JSON(auth.Response{
		Code:    400,
		Message: message,
		Data:    nil,
	})
}

// HandleInternalError 处理内部服务器错误
func (h *Handler) HandleInternalError(ctx iris.Context, err error, message string) {
	h.logger.Error(message, "error", err)
	ctx.StatusCode(iris.StatusInternalServerError)
	_ = ctx.JSON(auth.Response{
		Code:    500,
		Message: message,
		Data:    nil,
	})
}

// HandleForbidden 处理禁止访问错误
func (h *Handler) HandleForbidden(ctx iris.Context, err error, message string) {
	h.logger.Error(message, "error", err)
	ctx.StatusCode(iris.StatusForbidden)
	_ = ctx.JSON(auth.Response{
		Code:    403,
		Message: message,
		Data:    nil,
	})
}

// HandleSuccess 处理成功响应
func (h *Handler) HandleSuccess(ctx iris.Context, data interface{}) {
	_ = ctx.JSON(auth.Response{
		Code:    200,
		Message: "success",
		Data:    data,
	})
}

// GetClientIP 获取客户端IP地址
func (h *Handler) GetClientIP(ctx iris.Context) string {
	// 优先从 X-Forwarded-For 获取真实IP
	if forwarded := ctx.GetHeader("X-Forwarded-For"); forwarded != "" {
		ips := strings.Split(forwarded, ",")
		if len(ips) > 0 {
			return strings.TrimSpace(ips[0])
		}
	}

	// 从 X-Real-IP 获取
	if realIP := ctx.GetHeader("X-Real-IP"); realIP != "" {
		return realIP
	}

	// 从 RemoteAddr 获取
	remoteAddr := ctx.RemoteAddr()
	ip := strings.Split(remoteAddr, ":")[0]
	return ip
}

// IPToUint32 将IP地址转换为uint32
func (h *Handler) IPToUint32(ip string) int32 {
	parsedIP := net.ParseIP(ip)
	if parsedIP == nil {
		return 0
	}

	// 转换为IPv4
	ipv4 := parsedIP.To4()
	if ipv4 == nil {
		return 0
	}

	// 转换为uint32然后转为int32
	result := uint32(ipv4[0])<<24 + uint32(ipv4[1])<<16 + uint32(ipv4[2])<<8 + uint32(ipv4[3])
	return int32(result)
}

// Uint32ToIP 将uint32转换为IP地址字符串
func (h *Handler) Uint32ToIP(ip uint32) string {
	return fmt.Sprintf("%d.%d.%d.%d",
		(ip>>24)&0xFF,
		(ip>>16)&0xFF,
		(ip>>8)&0xFF,
		ip&0xFF)
}
