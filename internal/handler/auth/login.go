package auth

import (
	"context"
	"encoding/json"
	"fmt"
	"net"
	"time"

	v1 "fp-browser/api/v1/auth"
	"fp-browser/internal/service/shared"
	"fp-browser/internal/service/user"
	"fp-browser/pkg/ip"
	"fp-browser/pkg/log"

	"github.com/kataras/iris/v12"
	"github.com/kataras/iris/v12/sessions"
)

const (
	LoginAuthExpire = 15 * time.Minute // 登录授权等待时间
)

type LoginHandler struct {
	*Handler
	userService            user.UserService
	loginLogService        user.LoginLogService
	teamIPWhitelistService user.TeamIPWhitelistService
	redisService           shared.RedisService
	ip2locationService     *ip.Client
}

// NewLoginHandler 创建 LoginHandler 实例
func NewLoginHandler(
	logger *log.Logger,
	userService user.UserService,
	loginLogService user.LoginLogService,
	teamIPWhitelistService user.TeamIPWhitelistService,
	redisService shared.RedisService,
	ip2locationService *ip.Client,
) *LoginHandler {
	return &LoginHandler{
		Handler:                <PERSON><PERSON><PERSON><PERSON>(logger),
		userService:            userService,
		loginLogService:        loginLogService,
		teamIPWhitelistService: teamIPWhitelistService,
		redisService:           redisService,
		ip2locationService:     ip2locationService,
	}
}

// getIPLocation 获取IP地理位置信息并格式化为 country-city-region
func (h *LoginHandler) getIPLocation(ip string) string {
	if h.ip2locationService == nil {
		return "未知位置"
	}

	info, err := h.ip2locationService.QueryIP(ip)
	if err != nil {
		h.logger.Debug("查询IP地理位置失败", "ip", ip, "error", err)
		return "未知位置"
	}

	// 格式化为 country-city-region
	location := fmt.Sprintf("%s-%s-%s", info.Country, info.Region, info.City)
	return location
}

// ipToInt32 将IP地址字符串转换为int32
func (h *LoginHandler) ipToInt32(ipStr string) int32 {
	ip := net.ParseIP(ipStr)
	if ip == nil {
		h.logger.Warn("无效的IP地址", "ip", ipStr)
		return 0
	}

	// 转换为IPv4
	ipv4 := ip.To4()
	if ipv4 == nil {
		h.logger.Warn("非IPv4地址", "ip", ipStr)
		return 0
	}

	// 将4个字节转换为int32
	return int32(ipv4[0])<<24 + int32(ipv4[1])<<16 + int32(ipv4[2])<<8 + int32(ipv4[3])
}

// Login 用户登录
// @Summary 用户登录
// @Description 用户使用用户名和密码登录系统，支持IP白名单和授权机制
// @Tags 用户认证模块
// @Accept json
// @Produce json
// @Param request body v1.LoginRequest true "登录请求"
// @Success 200 {object} v1.LoginResponse
// @Failure 400 {object} v1.Response
// @Failure 401 {object} v1.Response
// @Failure 403 {object} v1.Response
// @Failure 500 {object} v1.Response
// @Router /api/v1/login [post]
func (h *LoginHandler) Login(ctx iris.Context) {
	// 解析请求数据
	var request v1.LoginRequest
	if err := ctx.ReadJSON(&request); err != nil {
		h.HandleBadRequest(ctx, err, "请求数据无效")
		return
	}

	// 基础参数验证
	if request.Identifier == "" || request.Password == "" {
		h.HandleBadRequest(ctx, nil, "用户标识符和密码不能为空")
		return
	}

	// 获取客户端IP和地理位置
	clientIP := h.getClientIP(ctx)
	location := h.getIPLocation(clientIP)

	// 1. 验证用户名密码并获取完整用户信息
	userInfo, err := h.userService.Authenticate(ctx, request.Identifier, request.Password)
	if err != nil {
		h.logger.Warn("用户认证失败", "identifier", request.Identifier, "clientIP", clientIP, "location", location, "error", err)
		h.HandleUnauthorized(ctx, "用户名或密码错误")
		return
	}

	// 2. 检查用户账户是否激活
	if userInfo.IsActive == nil || !*userInfo.IsActive {
		h.logger.Warn("用户账户未激活", "userID", userInfo.ID, "username", userInfo.UserName, "identifier", request.Identifier, "clientIP", clientIP, "location", location)
		h.HandleUnauthorized(ctx, "账户未激活，请联系管理员")
		return
	}

	// 添加详细的用户信息调试
	h.logger.Info("用户认证成功，用户详细信息",
		"userID", userInfo.ID,
		"username", userInfo.UserName,
		"userTeamID", userInfo.TeamID,
		"userRoleID", userInfo.RoleID,
		"isActive", userInfo.IsActive,
		"teamIsNil", userInfo.Team == nil,
		"roleIsNil", userInfo.Role == nil)

	if userInfo.Team != nil {
		h.logger.Info("团队信息", "teamID", userInfo.Team.ID, "teamName", userInfo.Team.Name, "ownerID", userInfo.Team.OwnerID)
	}

	if userInfo.Role != nil {
		h.logger.Info("角色信息", "roleID", userInfo.Role.ID, "roleName", userInfo.Role.Name, "roleTeamID", userInfo.Role.TeamID, "permissions", userInfo.Role.Permissions)
	}

	// 3. 检查团队信息完整性（Team必须存在）
	if userInfo.Team == nil {
		h.logger.Error("用户团队信息缺失", "userID", userInfo.ID, "identifier", request.Identifier)
		h.HandleInternalError(ctx, nil, "用户团队信息异常")
		return
	}

	// 4. 检查是否为团队owner
	isOwner := userInfo.Team.OwnerID == userInfo.ID
	h.logger.Debug("用户身份检查", "userID", userInfo.ID, "teamID", userInfo.Team.ID, "isOwner", isOwner)

	// 定义权限和角色ID变量
	var permissions []string
	var roleID int32

	// 5. 根据身份进行不同的处理
	if isOwner {
		// Owner用户：角色可以为空，权限为空
		h.logger.Debug("Owner用户登录", "userID", userInfo.ID, "teamID", userInfo.Team.ID)
		permissions = []string{} // Owner权限为空
		roleID = 0               // Owner角色ID为0
	} else {
		// 非Owner用户：必须有角色信息
		if userInfo.Role == nil {
			h.logger.Error("非Owner用户角色信息缺失", "userID", userInfo.ID, "identifier", request.Identifier)
			h.HandleInternalError(ctx, nil, "用户角色信息异常")
			return
		}

		roleID = userInfo.Role.ID

		// 检查角色是否开启安全登录
		roleSecure := userInfo.Role.Secure != nil && *userInfo.Role.Secure
		h.logger.Debug("角色安全设置检查", "userID", userInfo.ID, "roleID", userInfo.Role.ID, "secure", roleSecure)

		if roleSecure {
			// 进行IP白名单和授权检查
			allowed, err := h.checkSecureLogin(ctx, userInfo.Team.ID, userInfo.UserName, clientIP, location)
			if err != nil {
				h.logger.Error("安全登录检查失败", "userID", userInfo.ID, "clientIP", clientIP, "location", location, "error", err)
				h.HandleInternalError(ctx, err, "登录安全检查失败")
				return
			}

			if !allowed {
				h.logger.Warn("安全登录被拒绝", "userID", userInfo.ID, "username", userInfo.UserName, "clientIP", clientIP, "location", location, "teamID", userInfo.Team.ID)
				h.HandleUnauthorized(ctx, "登录需要授权，请等待管理员授权或联系团队管理员")
				return
			}
		}

		//// 解析用户权限
		var err error
		permissions, err = h.parsePermissions(userInfo.Role.Permissions)
		if err != nil {
			h.logger.Error("解析用户权限失败", "userID", userInfo.ID, "roleID", userInfo.Role.ID, "error", err)
			h.HandleInternalError(ctx, err, "权限解析失败")
			return
		}
	}

	// 6. 登录成功，创建session
	sess := ctx.Values().Get("session").(*sessions.Session)
	if sess == nil {
		h.logger.Error("Session获取失败", "userID", userInfo.ID)
		h.HandleInternalError(ctx, nil, "Session初始化失败")
		return
	}
	sess.Set("user_id", userInfo.ID)
	sess.Set("team_id", userInfo.Team.ID)
	sess.Set("role_id", roleID)
	sess.Set("username", userInfo.UserName)
	sess.Set("is_owner", isOwner)
	sess.Set("permissions", permissions)

	// 7. 记录登录日志
	h.logger.Info("用户登录成功",
		"userID", userInfo.ID,
		"username", userInfo.UserName,
		"teamID", userInfo.Team.ID,
		"roleID", roleID,
		"isOwner", isOwner,
		"permissionsCount", len(permissions),
		"clientIP", clientIP,
		"location", location)

	// 8. 记录登录日志到数据库
	if err := h.loginLogService.CreateLog(ctx, userInfo.ID, userInfo.Team.ID, clientIP, location); err != nil {
		h.logger.Error("记录登录日志失败", "userID", userInfo.ID, "clientIP", clientIP, "location", location, "error", err)
		// 登录日志记录失败不影响登录流程，只记录错误
	} else {
		h.logger.Debug("登录日志记录成功", "userID", userInfo.ID, "clientIP", clientIP, "location", location)
	}

	// 9. 返回登录响应
	response := v1.LoginResponse{
		Message: "用户登入成功！",
	}

	h.HandleSuccess(ctx, response)
}

// checkSecureLogin 检查安全登录权限
func (h *LoginHandler) checkSecureLogin(ctx context.Context, teamID int32, username, clientIP, location string) (bool, error) {
	// 1. 首先检查IP是否在白名单中
	hasAccess, err := h.teamIPWhitelistService.ValidateIPAccess(ctx, teamID, clientIP)
	if err != nil {
		return false, fmt.Errorf("检查IP白名单失败: %w", err)
	}

	// 2. 如果IP在白名单中，直接允许登录
	if hasAccess {
		h.logger.Debug("IP在白名单中，允许登录", "teamID", teamID, "username", username, "clientIP", clientIP, "location", location)
		return true, nil
	}

	// 3. IP不在白名单中，检查Redis授权记录
	authKey := fmt.Sprintf("login_auth:%d:%s:login", teamID, username)

	authValue, err := h.redisService.Get(ctx, authKey)
	if err != nil {
		if h.redisService.IsNil(err) {
			// 记录不存在，创建等待授权的记录
			h.logger.Info("创建登录授权等待记录", "teamID", teamID, "username", username, "clientIP", clientIP, "location", location)
			if setErr := h.redisService.Set(ctx, authKey, "false", LoginAuthExpire); setErr != nil {
				return false, fmt.Errorf("创建授权等待记录失败: %w", setErr)
			}
			return false, nil
		}
		return false, fmt.Errorf("查询授权记录失败: %w", err)
	}

	// 4. 检查授权状态
	if authValue == "true" {
		// 授权通过，删除记录并允许登录
		h.logger.Info("找到授权记录，允许登录", "teamID", teamID, "username", username, "clientIP", clientIP, "location", location)
		if delErr := h.redisService.Delete(ctx, authKey); delErr != nil {
			h.logger.Warn("删除授权记录失败", "key", authKey, "error", delErr)
			// 删除失败不影响登录流程
		}
		return true, nil
	}

	// 5. 授权记录存在但值为false，更新过期时间并拒绝登录
	h.logger.Debug("授权记录存在但未通过，更新等待时间", "teamID", teamID, "username", username, "clientIP", clientIP, "location", location)
	if setErr := h.redisService.Set(ctx, authKey, "false", LoginAuthExpire); setErr != nil {
		h.logger.Warn("更新授权等待记录失败", "key", authKey, "error", setErr)
	}

	return false, nil
}

// getClientIP 获取客户端真实IP
func (h *LoginHandler) getClientIP(ctx iris.Context) string {
	// 优先从代理头中获取
	if ip := ctx.GetHeader("X-Forwarded-For"); ip != "" {
		return ip
	}
	if ip := ctx.GetHeader("X-Real-IP"); ip != "" {
		return ip
	}
	if ip := ctx.GetHeader("CF-Connecting-IP"); ip != "" {
		return ip
	}

	// fallback到RemoteAddr
	return ctx.RemoteAddr()
}

// parsePermissions 解析权限JSON字符串为字符串数组
func (h *LoginHandler) parsePermissions(permissionsJSON string) ([]string, error) {
	if permissionsJSON == "" {
		h.logger.Debug("权限字符串为空，返回空权限列表")
		return []string{}, nil
	}

	var permissions []string
	if err := json.Unmarshal([]byte(permissionsJSON), &permissions); err != nil {
		h.logger.Error("权限JSON解析失败", "permissionsJSON", permissionsJSON, "error", err)
		return nil, fmt.Errorf("权限格式无效: %w", err)
	}

	h.logger.Debug("权限解析成功", "permissionsCount", len(permissions), "permissions", permissions)
	return permissions, nil
}
