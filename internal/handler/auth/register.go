package auth

import (
	"fmt"
	v1 "fp-browser/api/v1/auth"
	"fp-browser/internal/dao/model"
	"fp-browser/internal/service/shared"
	"fp-browser/internal/service/user"
	"fp-browser/pkg/log"
	"github.com/kataras/iris/v12"
	"strings"
)

type RegisterHandler struct {
	*Handler
	userService  user.UserService
	teamService  user.TeamService
	redisService shared.RedisService
}

// NewRegisterHandler 创建 RegisterHandler 实例
func NewRegisterHandler(
	logger *log.Logger,
	userService user.UserService,
	teamService user.TeamService,
	redisService shared.RedisService,
) *RegisterHandler {
	return &RegisterHandler{
		Handler:      NewHandler(logger),
		userService:  userService,
		teamService:  teamService,
		redisService: redisService,
	}
}

// verifyEmailCode 验证邮箱验证码
func (h *RegisterHandler) verifyEmailCode(ctx iris.Context, email, code string) error {
	if email == "" || code == "" {
		return fmt.Errorf("邮箱和验证码不能为空")
	}

	// 构建redis key
	redisKey := fmt.Sprintf("email_verification:register:%s", email)

	// 从redis获取验证码
	expectedCode, err := h.redisService.Get(ctx, redisKey)
	if err != nil {
		if h.redisService.IsNil(err) {
			return fmt.Errorf("验证码不存在或已过期")
		}
		return fmt.Errorf("验证码查询失败: %w", err)
	}

	// 验证验证码
	if code != expectedCode {
		return fmt.Errorf("验证码错误")
	}

	// 验证通过，删除redis中的验证码（防止重复使用）
	if err := h.redisService.Delete(ctx, redisKey); err != nil {
		h.logger.Warn("删除邮箱验证码失败", "email", email, "key", redisKey, "error", err)
		// 不影响主流程，只记录警告
	}

	return nil
}

// Register 用户注册
// @Summary 用户注册
// @Description 用户注册，创建新用户和对应的团队。必须提供用户名，邮箱，密码不少于8位，需要邮箱验证码（手机号注册暂未开放）
// @Tags 用户认证模块
// @Accept json
// @Produce json
// @Param request body v1.RegisterRequest true "注册信息"
// @Success 201 {object} v1.RegisterResponse
// @Failure 400 {object} v1.Response
// @Failure 409 {object} v1.Response "邮箱/电话/用户名已被占用"
// @Failure 500 {object} v1.Response
// @Router /api/v1/auth/register [post]
func (h *RegisterHandler) Register(ctx iris.Context) {
	var request v1.RegisterRequest
	if err := ctx.ReadJSON(&request); err != nil {
		h.HandleBadRequest(ctx, err, "请求数据无效")
		return
	}

	// 验证必须提供用户名
	if request.UserName == "" {
		h.HandleBadRequest(ctx, nil, "用户名不能为空")
		return
	}

	// 验证邮箱和手机号二选一
	if request.Email == "" && request.Telephone == "" {
		h.HandleBadRequest(ctx, nil, "请提供邮箱或手机号码")
		return
	}

	// 验证不能同时提供邮箱和手机号（严格二选一）
	if request.Email != "" && request.Telephone != "" {
		h.HandleBadRequest(ctx, nil, "邮箱和手机号码只能提供一个")
		return
	}

	// 暂时关闭手机号注册功能
	if request.Telephone != "" {
		h.logger.Info("用户尝试使用手机号注册",
			"username", request.UserName,
			"telephone", request.Telephone)
		h.HandleBadRequest(ctx, nil, "暂未开放手机号注册，请使用邮箱注册")
		return
	}

	// 确保提供了邮箱（因为手机号注册已关闭）
	if request.Email == "" {
		h.HandleBadRequest(ctx, nil, "请提供邮箱进行注册")
		return
	}

	// 验证密码长度
	if len(request.Password) < 8 {
		h.HandleBadRequest(ctx, nil, "密码长度不能少于8位")
		return
	}

	// 验证邮箱验证码（现在是必须的）
	if request.EmailCode == "" {
		h.HandleBadRequest(ctx, nil, "邮箱验证码不能为空")
		return
	}

	if err := h.verifyEmailCode(ctx, request.Email, request.EmailCode); err != nil {
		h.logger.Warn("邮箱验证码验证失败",
			"email", request.Email,
			"code", request.EmailCode,
			"error", err)
		h.HandleBadRequest(ctx, err, err.Error())
		return
	}

	h.logger.Info("邮箱验证码验证成功", "email", request.Email)

	// 处理邀请码
	var inviteUserID *int32
	if request.InviteCode != "" {
		inviteUser, err := h.userService.GetUserByInviteCode(ctx, request.InviteCode)
		if err != nil {
			h.logger.Error("查询邀请码失败",
				"inviteCode", request.InviteCode,
				"error", err)
			h.HandleBadRequest(ctx, err, "邀请码无效")
			return
		}
		if inviteUser == nil {
			h.HandleBadRequest(ctx, nil, "邀请码不存在")
			return
		}
		inviteUserID = &inviteUser.ID
		h.logger.Info("找到邀请用户",
			"inviteCode", request.InviteCode,
			"inviteUserID", inviteUser.ID,
			"inviteUserName", inviteUser.UserName)
	}
	commissionType := int16(1)
	commissionRate := int16(30)

	// 构造用户对象，设置系统默认值
	newUser := &model.User{
		UserName:  request.UserName,
		Email:     request.Email,
		Password:  request.Password,        // 不在这里加密，直接传递
		Telephone: "",                      // 手机号暂时设为空
		RealName:  "用户" + request.UserName, // 使用用户名生成真实姓名
		// 系统默认值
		IDCardNumber:             "",
		CompanyName:              "",
		CompanyUnifiedSocialCode: "",
		RealNameType:             &[]int32{0}[0],    // 0 未认证
		InviteUserID:             inviteUserID,      // 设置邀请用户ID
		CommissionType:           &commissionType,   // 无佣金类型
		CommissionRate:           &commissionRate,   // 无佣金比例
		IsActive:                 &[]bool{true}[0],  // 默认激活
		IsDeleted:                &[]bool{false}[0], // 默认未删除
		IsTwoFactorEnabled:       &[]bool{false}[0], // 默认未启用两步验证
		RoleID:                   0,                 // 默认无角色
	}

	// 注册用户
	createdUser, err := h.userService.Register(ctx, newUser)
	if err != nil {
		h.logger.Error("用户注册失败",
			"username", request.UserName,
			"email", request.Email,
			"inviteCode", request.InviteCode,
			"inviteUserID", inviteUserID,
			"error", err)

		// 处理不同类型的错误
		errorMessage := err.Error()
		switch {
		case strings.Contains(errorMessage, "email"):
			ctx.StatusCode(iris.StatusConflict)
			_ = ctx.JSON(iris.Map{"error": "邮箱已被占用！"})
		case strings.Contains(errorMessage, "telephone"):
			ctx.StatusCode(iris.StatusConflict)
			_ = ctx.JSON(iris.Map{"error": "电话号码已被占用！"})
		case strings.Contains(errorMessage, "username") || strings.Contains(errorMessage, "user_name"):
			ctx.StatusCode(iris.StatusConflict)
			_ = ctx.JSON(iris.Map{"error": "用户名已被占用！"})
		default:
			h.HandleInternalError(ctx, err, "注册失败")
		}
		return
	}

	// 创建团队
	newTeam, err := h.teamService.CreateTeam(ctx, createdUser.UserName, uint(createdUser.ID))
	if err != nil {
		h.logger.Error("创建团队失败", "userID", createdUser.ID, "username", createdUser.UserName, "error", err)
		h.HandleInternalError(ctx, err, "创建团队失败")
		return
	}

	// 更新用户的 TeamID（只传递必要字段）
	updateUser := &model.User{
		ID:     createdUser.ID,
		TeamID: newTeam.ID,
	}
	if err := h.userService.UpdateProfile(ctx, []*model.User{updateUser}, 0); err != nil {
		h.logger.Error("更新用户团队信息失败", "userID", createdUser.ID, "teamID", newTeam.ID, "error", err)
		h.HandleInternalError(ctx, err, "更新用户数据失败")
		return
	}

	h.logger.Info("用户注册成功",
		"userID", createdUser.ID,
		"username", createdUser.UserName,
		"email", createdUser.Email,
		"teamID", newTeam.ID,
		"teamName", newTeam.Name,
		"inviteCode", request.InviteCode,
		"inviteUserID", inviteUserID)

	response := v1.RegisterResponse{
		Message: "用户注册成功！",
	}

	ctx.StatusCode(iris.StatusCreated)
	h.HandleSuccess(ctx, response)
}
