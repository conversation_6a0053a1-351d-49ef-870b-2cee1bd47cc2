package smtp

import (
	"crypto/tls"
	"fmt"
	"net"
	"net/smtp"
	"strings"
	"time"

	"fp-browser/internal/config"
)

type SmtpClient struct {
	host       string
	port       int
	username   string
	password   string
	fromName   string
	fromEmail  string
	auth       smtp.Auth
	serverAddr string
}

// BatchEmail 批量发邮件时用的结构
type BatchEmail struct {
	To      string
	Subject string
	Body    string
}

// smtpSession 表示一个SMTP连接会话
type smtpSession struct {
	client *smtp.Client
	conn   net.Conn
}

func (s *smtpSession) Close() {
	if s.client != nil {
		_ = s.client.Quit()
	}
	if s.conn != nil {
		_ = s.conn.Close()
	}
}

// NewSmtpClient 创建SMTP客户端
func NewSmtpClient(cfg *config.SmtpConfig) (*SmtpClient, error) {
	auth := smtp.PlainAuth("", cfg.Username, cfg.Password, cfg.Host)
	return &SmtpClient{
		host:       cfg.Host,
		port:       cfg.Port,
		username:   cfg.Username,
		password:   cfg.Password,
		fromName:   cfg.FromName,
		fromEmail:  cfg.FromEmail,
		auth:       auth,
		serverAddr: fmt.Sprintf("%s:%d", cfg.Host, cfg.Port),
	}, nil
}

// buildMessage 构建单个收件人的邮件
func (c *SmtpClient) buildMessage(to string, subject string, body string) string {
	headers := map[string]string{
		"From":         fmt.Sprintf("%s <%s>", c.fromName, c.fromEmail),
		"To":           to,
		"Subject":      subject,
		"MIME-Version": "1.0",
		"Content-Type": "text/html; charset=\"UTF-8\"",
	}
	var msg strings.Builder
	for k, v := range headers {
		msg.WriteString(fmt.Sprintf("%s: %s\r\n", k, v))
	}
	msg.WriteString("\r\n")
	msg.WriteString(body)
	return msg.String()
}

// openSession 打开SMTP连接
func (c *SmtpClient) openSession() (*smtpSession, error) {
	conn, err := tls.DialWithDialer(&net.Dialer{Timeout: 5 * time.Second}, "tcp", c.serverAddr, &tls.Config{
		InsecureSkipVerify: true,
		ServerName:         c.host,
	})
	if err != nil {
		return nil, fmt.Errorf("连接SMTP服务器失败: %w", err)
	}

	client, err := smtp.NewClient(conn, c.host)
	if err != nil {
		_ = conn.Close()
		return nil, fmt.Errorf("创建SMTP客户端失败: %w", err)
	}

	if err := client.Auth(c.auth); err != nil {
		_ = client.Quit()
		_ = conn.Close()
		return nil, fmt.Errorf("SMTP认证失败: %w", err)
	}

	return &smtpSession{
		client: client,
		conn:   conn,
	}, nil
}

// sendWithSession 复用连接发送一封邮件
func (c *SmtpClient) sendWithSession(session *smtpSession, to string, msg string) error {
	if err := session.client.Mail(c.fromEmail); err != nil {
		return fmt.Errorf("设置发件人失败: %w", err)
	}
	if err := session.client.Rcpt(to); err != nil {
		return fmt.Errorf("设置收件人失败: %w", err)
	}

	wc, err := session.client.Data()
	if err != nil {
		return fmt.Errorf("发送邮件数据失败: %w", err)
	}
	defer wc.Close()

	_, err = wc.Write([]byte(msg))
	if err != nil {
		return fmt.Errorf("写入邮件数据失败: %w", err)
	}
	return nil
}

// SendToOne 发送一封邮件给单个人
func (c *SmtpClient) SendToOne(to string, subject string, body string) error {
	session, err := c.openSession()
	if err != nil {
		return err
	}
	defer session.Close()

	msg := c.buildMessage(to, subject, body)
	return c.sendWithSession(session, to, msg)
}

// SendToMany 单封邮件同时发送给多个人（失败不中断，统一返回）
func (c *SmtpClient) SendToMany(toList []string, subject string, body string) error {
	session, err := c.openSession()
	if err != nil {
		return err
	}
	defer session.Close()

	var failedList []string

	for _, to := range toList {
		msg := c.buildMessage(to, subject, body)
		if err := c.sendWithSession(session, to, msg); err != nil {
			failedList = append(failedList, fmt.Sprintf("%s: %v", to, err))
		}
	}

	if len(failedList) > 0 {
		return fmt.Errorf("部分邮件发送失败: %s", strings.Join(failedList, "; "))
	}
	return nil
}

// SendBatch 每人一个不同的邮件内容（失败不中断，统一返回）
func (c *SmtpClient) SendBatch(emails []BatchEmail) error {
	session, err := c.openSession()
	if err != nil {
		return err
	}
	defer session.Close()

	var failedList []string

	for _, e := range emails {
		msg := c.buildMessage(e.To, e.Subject, e.Body)
		if err := c.sendWithSession(session, e.To, msg); err != nil {
			failedList = append(failedList, fmt.Sprintf("%s: %v", e.To, err))
		}
	}

	if len(failedList) > 0 {
		return fmt.Errorf("部分邮件发送失败: %s", strings.Join(failedList, "; "))
	}
	return nil
}
