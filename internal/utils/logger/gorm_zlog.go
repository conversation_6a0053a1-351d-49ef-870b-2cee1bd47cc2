// internal/utils/logger/gorm_zlog.go
package logger

import (
	"context"
	"strings"
	"time"

	"github.com/rs/zerolog"
	"gorm.io/gorm/logger"
)

type GormZLogger struct {
	zlog       *zerolog.Logger
	logLevel   logger.LogLevel
	slowThresh time.Duration
}

// NewGormZLogger 构造函数
func NewGormZLogger(zlog *zerolog.Logger, cfg *GormLoggerConfig) logger.Interface {
	return &GormZLogger{
		zlog:       zlog,
		logLevel:   cfg.Level,
		slowThresh: cfg.SlowThreshold,
	}
}

// GormLoggerConfig 日志适配器配置
type GormLoggerConfig struct {
	Level         logger.LogLevel
	SlowThreshold time.Duration
}

func (l *GormZLogger) LogMode(level logger.LogLevel) logger.Interface {
	newLogger := *l
	newLogger.logLevel = level
	return &newLogger
}

func (l *GormZLogger) Info(ctx context.Context, msg string, data ...interface{}) {
	if l.logLevel >= logger.Info {
		l.zlog.Info().Msgf(msg, data...)
	}
}

func (l *GormZLogger) Warn(ctx context.Context, msg string, data ...interface{}) {
	if l.logLevel >= logger.Warn {
		l.zlog.Warn().Msgf(msg, data...)
	}
}

func (l *GormZLogger) Error(ctx context.Context, msg string, data ...interface{}) {
	if l.logLevel >= logger.Error {
		l.zlog.Error().Msgf(msg, data...)
	}
}

func (l *GormZLogger) Trace(ctx context.Context, begin time.Time, fc func() (string, int64), err error) {
	elapsed := time.Since(begin)
	sql, rows := fc()

	event := l.zlog.With().
		Str("sql", sql).
		Dur("elapsed", elapsed).
		Int64("rows", rows).
		Logger()

	switch {
	case err != nil && l.logLevel >= logger.Error:
		event.Error().Err(err).Msg("SQL 执行错误")
	case elapsed > l.slowThresh && l.logLevel >= logger.Warn:
		event.Warn().Msg("慢查询")
	case l.logLevel >= logger.Info:
		event.Info().Msg("SQL 执行完成")
	}
}

// ParseGormLevel 将字符串转换为 GORM 日志等级
func ParseGormLevel(level string) logger.LogLevel {
	switch strings.ToLower(level) {
	case "silent":
		return logger.Silent
	case "error":
		return logger.Error
	case "warn":
		return logger.Warn
	case "info":
		return logger.Info
	default:
		return logger.Warn
	}
}
