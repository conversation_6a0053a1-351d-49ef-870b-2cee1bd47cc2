// internal/utils/logger/logger.go
package logger

import (
	"fmt"
	"io"
	"os"
	"strings"
	"time"

	"fp-browser/internal/config"
	"github.com/rs/zerolog"
)

func NewLogger(cfg *config.LogConfig) zerolog.Logger {
	// 设置全局时间格式
	zerolog.TimeFieldFormat = time.RFC3339

	// 设置日志级别
	level, err := zerolog.ParseLevel(strings.ToLower(cfg.Level))
	if err != nil {
		level = zerolog.InfoLevel
	}
	zerolog.SetGlobalLevel(level)

	// 设置输出
	var writer io.Writer
	if cfg.File != "" {
		f, err := os.OpenFile(cfg.File, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
		if err != nil {
			panic(fmt.Sprintf("无法打开日志文件: %v", err))
		}
		writer = f
	} else {
		writer = os.Stdout
	}

	// 美化输出（仅限 stdout）
	if cfg.Pretty && writer == os.Stdout {
		writer = zerolog.ConsoleWriter{
			Out:        os.Stdout,
			TimeFormat: "2006-01-02 15:04:05",
		}
	}

	log := zerolog.New(writer).With().Timestamp()

	if cfg.WithCaller {
		log = log.Caller()
	}

	return log.Logger()
}
