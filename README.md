# 🌐 Go Backend for Admin & User APIs

这是一个基于 **Go + Iris + GORM + PostgreSQL** 构建的高性能 Web 后端项目，专注于以 JSON 格式与前端交互，支持用户与管理员权限隔离，采用现代工程架构实践，具备如下能力：

- ✨ **RESTful JSON API**
- 🔐 **用户 / 管理员 分权路由与业务隔离**
- 🧬 **Google Wire 自动依赖注入**
- 🗃️ **GORM 支持 PostgreSQL 读写分离**
- 🗓️ **定时任务（Cron）& 计划任务（Scheduled）支持**
- 🧱 **大表查询优化（分区 / 索引）**

---

## 🧩 项目目录结构

```bash
your-project/
├── cmd/                    # 启动入口（user / admin / scheduler）
│   ├── server/
│   └── scheduler/
├── configs/                # 配置文件（YAML）
├── internal/
│   ├── controller/         # 控制器层（user/admin 路由逻辑）
│   │   ├── node/
│   │   ├── shared/
│   │   ├── admin/
│   │   └── user/
│   ├── service/            # 服务层（业务逻辑，支持自动注入）
│   │   ├── node/
│   │   ├── shared/
│   │   ├── admin/
│   │   └── user/
│   ├── repository/         # 仓储层（GORM封装，支持分库）
│   ├── model/              # 数据模型（GORM结构体）
│   ├── middleware/         # 中间件（JWT/Auth/CORS/Logger）
│   ├── router/             # 路由注册
│   ├── scheduler/          # 定时任务、计划任务
│   └── wire/               # Google Wire provider 分层注入
├── pkg/                    # 公共组件（utils/logger/config等）
├── go.mod
└── README.md
```

---

## ⚙️ 系统配置（config.yaml 示例）

```yaml
server:
  port: 8080
  mode: "release"

database:
  driver: "postgres"
  master: "***********************************/appdb?sslmode=disable"
  replicas:
    - "*************************************/appdb?sslmode=disable"
    - "*************************************/appdb?sslmode=disable"
  maxOpenConns: 100
  maxIdleConns: 50
  connMaxLifetime: "1h"

jwt:
  secret: "your-super-secret"
  expireHours: 72

scheduler:
  timezone: "Asia/Shanghai"
```

---

## 🚀 启动方式

支持三种启动入口：

```bash
# 启动用户端 API
go run ./cmd/user

# 启动管理员 API
go run ./cmd/admin

# 启动定时调度系统（支持 Cron + 计划任务）
go run ./cmd/scheduler
```

---

## 🛠️ 项目架构设计

### ✅ 分层结构（简化 Clean Architecture）

- `Controller`：处理 HTTP 请求，绑定参数，返回 JSON
- `Service`：核心业务逻辑，可组合调用多个仓储或服务
- `Repository`：封装 GORM 数据访问，读写分离由 DBResolver 控制
- `Model`：GORM 的结构定义（含分区键）
- `Middleware`：认证、日志、错误恢复等中间件
- `Scheduler`：定时任务 + DB 持久化计划任务
- `Wire`：所有依赖的注册和注入入口（按 admin/user/scheduler 区分）

---

### 🔐 路由分区

| 模块   | 前缀路径  | 鉴权中间件       |
|--------|-----------|------------------|
| 用户端 | `/user/*` | `AuthUserJWT`    |
| 管理端 | `/admin/*`| `AuthAdminJWT`   |

---

### 🧬 依赖注入（Google Wire）

每个系统入口对应一个注入链：

- `wire/user_provider.go`：注入用户服务与控制器
- `wire/admin_provider.go`：注入管理员服务与控制器
- `wire/scheduler_provider.go`：注入所有 Job 实例与调度器

使用命令构建依赖树：

```bash
# 生成依赖注入代码
wire ./cmd/user
wire ./cmd/admin
wire ./cmd/scheduler
```

---

## 🗃️ 数据访问层（GORM + DBResolver）

支持 PostgreSQL 主从读写分离：

```go
db.Use(dbresolver.Register(dbresolver.Config{
  Sources:  []gorm.Dialector{postgres.Open(cfg.Database.Master)},
  Replicas: []gorm.Dialector{
    postgres.Open(cfg.Database.Replicas[0]),
    postgres.Open(cfg.Database.Replicas[1]),
  },
  Policy: dbresolver.RandomPolicy{},
}))
```

- 所有查询（SELECT）走从库
- 所有写入（INSERT/UPDATE/DELETE）走主库
- 事务内自动走主库

---

## 📆 定时任务 & 计划任务

使用 `robfig/cron/v3` + `Job 接口` 支持自动注入：

```go
type CleanOrderJob struct {
  OrderService *services.OrderService
}
func (j *CleanOrderJob) Run() {
  j.OrderService.CleanupExpired()
}
```

任务注册示例：

```go
cronEngine.AddJob("@every 1h", cleanOrderJob)
```

计划任务（Scheduled Task）：

- 持久化至 DB（`scheduled_jobs` 表）
- 每分钟轮询最近 5 分钟内的待执行任务
- 动态调度任务队列（支持按类型执行 handler）

---

## 🧱 大表优化策略

针对交易/账本类大表采用：

- PostgreSQL 原生分区表：按月切分
- 范围分区字段：`created_at`, `tx_time`
- 自动裁剪分区 + 索引维护（建议 pg_cron 自动化）
- 查询默认加时间范围条件
- 联表使用 JOIN + 预加载策略，减少 N+1 查询

---

## ✅ TODO / 建议特性

- [ ] 接入 OpenAPI / Swagger 文档自动生成
- [ ] 权限模型可扩展为 RBAC / ABAC
- [ ] 引入任务分发系统（如 Asynq）支持更复杂的异步逻辑
- [ ] 添加 SQL 语句审计 / 慢查询监控

---

## 🧠 技术栈

| 技术         | 用途               |
|--------------|--------------------|
| Go           | 主语言             |
| Iris         | Web 框架（高性能） |
| GORM         | ORM + 插件支持     |
| PostgreSQL   | 数据库             |
| DBResolver   | GORM 读写分离插件  |
| Google Wire  | 依赖注入           |
| robfig/cron  | 定时任务调度       |
| viper        | 配置加载           |
| zap/logrus   | 日志系统           |

---

## 🧊 作者建议

- 🧽 **所有服务都走 DI 构造，避免全局变量**
- 📦 **大表设计优先考虑分区 + 避免 SELECT * 查询**
- 🧪 **Controller 保持瘦，测试聚焦 Service 层**
- 📎 **代码层级明确：Controller → Service → Repo → DB**

---

## 🐾 License

MIT License. © You Own This Project.