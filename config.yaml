server:
  port: 8080
  mode: "release"

log:
  level: debug
  gorm_level: info
  file: logs/app.log
  with_caller: true
  pretty: true

database:
  master:
    host: localhost
    port: 5432
    user: postgres
    password: secret
    dbname: fp_browser
  replicas:
    - host: replica1
      port: 5432
      user: postgres
      password: secret
      dbname: fp_browser
  pool:
    max_open_conns: 50
    max_idle_conns: 10
    conn_max_lifetime: 1h

jwt:
  secret: "change-me-please"
  expireHours: 72

schedule:
  timezone: "Asia/Shanghai"

redis:
  host: 127.0.0.1
  port: 6379
  password: ""
  db: 0
  minIdleConns: 10
  poolSize: 100

# Session配置
session:
  cookie_name: "suiyu_session"
  expire: 24h
  secure: false
  use_redis: true
  redis:
    host: "127.0.0.1"
    port: 6379
    password: ""
    db: "1"
    pool_size: 100
    timeout: 30s

smtp:
  host: smtp.example.com
  port: 465
  username: <EMAIL>
  password: your_password
  fromName: "Your App Name"
  fromEmail: <EMAIL>

# 阿里云OSS配置
oss:
  access_key_id: "your_access_key_id"
  access_key_secret: "your_access_key_secret"
  endpoint: "https://your-bucket.oss-cn-hangzhou.aliyuncs.com"
  bucket_name: "your-bucket"
  region: "cn-hangzhou"
  default_expire: 3600s
