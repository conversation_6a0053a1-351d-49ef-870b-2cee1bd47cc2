oss:
  region: "ap-northeast-1"              # OSS 所在区域（必填）
  bucket_name: "your-bucket-name"       # 存储桶名称（必填）
  access_key_id: "your-access-key-id"   # 可选，如果不设置，则使用环境变量
  access_key_secret: "your-secret-key"  # 可选，如果不设置，则使用环境变量
  callback_url: "https://yourdomain.com/oss/callback"  # 可选，用于上传回调

smtp:
  host: "smtpdm.aliyun.com"        # SMTP服务器地址（必填）
  port: 465                       # SMTP端口，默认587（可选）
  username: "<EMAIL>" # 登录用户名（必填）
  password: "SUIYUkeji2024"  # 登录密码（必填）
  from_address: "<EMAIL>" # 发件人邮箱地址（必填）
  from_name: "PrismBrowser" # 发件人名

log:
  log_file_name: "./logs/app.log"  # 日志文件路径，如为空则只输出到控制台
  log_level: "info"                # 日志级别，可选: debug, info, warn, error
  max_size: 100                    # 单个日志文件最大大小（单位：MB）
  max_backups: 7                   # 最大备份数量（滚动日志数量）
  max_age: 30                      # 日志最大保留天数
  compress: true                  # 是否压缩旧日志（gz格式）
  encoding: "console"             # 输出格式: "console" 或 "json"
  colorize: true                  # 控制台输出是否彩色（仅当 encoding 为 console 时有效）

env: "dev"  # 运行环境: dev, staging, prod，非prod时附加stack

payment:
  alipay:
    app_id: "your-app-id"                     # 支付宝应用 AppID（必填）
    private_key: "your-app-private-key"       # 应用私钥（必填）
    is_production: false                      # 是否生产环境（true=生产，false=沙箱）

    notify_url: "https://yourdomain.com/pay/alipay/notify"  # 支付结果通知地址（必填）
    return_url: "https://yourdomain.com/pay/alipay/return"  # 支付完成跳转地址（必填）

    cert_mode: true                           # 是否启用证书模式（启用时需填写以下路径）
    app_cert_path: "./cert/appCertPublicKey.crt"      # 应用公钥证书路径（必填）
    root_cert_path: "./cert/alipayRootCert.crt"       # 支付宝根证书路径（必填）
    public_cert_path: "./cert/alipayPublicCert.crt"   # 支付宝公钥证书路径（必填）

    encrypt_key: "your-content-encrypt-key"   # 加密密钥（可选，用于加密请求参数）

database:
  master:
    host: "*************"           # 主数据库地址
    port: 5432                  # 主数据库端口
    user: "suiyu"        # 主数据库用户名
    password: "suiyu"    # 主数据库密码
    dbname: "suiyu"      # 主数据库名称

  replica_count: 0              # 从库数量，设置为 0 表示不启用读写分离
  replicas:
    - host: "*********"
      port: 5432
      user: "replica-user"
      password: "replica-pass"
      dbname: "your-db-name"
    - host: "*********"
      port: 5432
      user: "replica-user"
      password: "replica-pass"
      dbname: "your-db-name"

  pool:
    max_open_conns: 100                      # 最大打开连接数
    max_idle_conns: 10                       # 最大空闲连接数
    conn_max_lifetime: "1h"                  # 每个连接的最大生命周期，支持单位：s, m, h
    gorm_log_level: "warn"                   # GORM 日志级别，可选：silent, error, warn, info
    slow_query_threshold: "200ms"            # 慢查询阈值，日志记录用

redis:
  addr: "*************:6379"      # Redis 服务器地址（格式：host:port）
  password: ""                # Redis 密码（如无可为空）
  db: 0                       # Redis 数据库编号（默认使用 0）

http:
  host: "0.0.0.0"   # HTTP服务监听地址，建议使用 0.0.0.0 以接受所有网络请求
  port: 52618        # HTTP服务监听端口

ip2location:
  db_path: "./storage/database/IP2LOCATION.BIN"