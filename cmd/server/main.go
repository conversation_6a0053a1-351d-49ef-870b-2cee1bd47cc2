package main

import (
	"context"
	"flag"
	"fmt"

	"fp-browser/cmd/server/wire"
	"fp-browser/pkg/config"
	"fp-browser/pkg/log"
)

// @title           FP-Browser API
// @version         1.0.0
// @description     This is the FP-Browser API service.
// @termsOfService  http://swagger.io/terms/
// @contact.name    API Support
// @contact.url     http://www.swagger.io/support
// @contact.email   <EMAIL>
// @license.name    Apache 2.0
// @license.url     http://www.apache.org/licenses/LICENSE-2.0.html
// @host            localhost:8000
// @securityDefinitions.apiKey Bearer
// @in header
// @name Authorization
// @externalDocs.description  OpenAPI
// @externalDocs.url          https://swagger.io/resources/open-api/
func main() {
	var envConf = flag.String("conf", "configs/config.yaml", "config path, eg: -conf configs/config.yaml")
	flag.Parse()

	// 加载配置
	conf := config.NewConfig(*envConf)

	// 初始化日志
	logger := log.NewLog(conf)
	logger.Info("logger initialized")

	// 使用wire进行依赖注入
	app, cleanup, err := wire.NewWire(conf, logger)
	defer cleanup()
	if err != nil {
		logger.Fatal("Failed to initialize application", "error", err)
	}

	// 启动服务
	host := conf.GetString("http.host")
	port := conf.GetInt("http.port")
	addr := fmt.Sprintf("%s:%d", host, port)
	logger.Info("Starting server", "host", fmt.Sprintf("http://%s", addr))

	if err = app.Run(context.Background()); err != nil {
		logger.Fatal("Server stopped with error", "error", err)
	}
}
