# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# History and backup files
.history/
.cursor/
*.bak
*.backup
*.tmp

# Log files
*.log

# Environment variables
.env
.env.local
.env.*.local

# Build output
/dist/
/build/
/bin/

# Coverage reports
coverage.txt
coverage.html
coverage.out 
/mac
/storage/database/IP2LOCATION.BIN
