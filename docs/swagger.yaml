definitions:
  fp-browser_api_v1_auth.LoginRequest:
    properties:
      identifier:
        example: <EMAIL>
        type: string
      password:
        example: password123
        type: string
    required:
    - identifier
    - password
    type: object
  fp-browser_api_v1_auth.LoginResponse:
    properties:
      message:
        example: 用户登入成功！
        type: string
    type: object
  fp-browser_api_v1_auth.RegisterRequest:
    properties:
      email:
        description: 邮箱，可选，与手机号二选一
        example: <EMAIL>
        maxLength: 100
        type: string
      email_code:
        description: 邮箱验证码，当提供邮箱时必填，固定6位数字
        example: "123456"
        type: string
      invite_code:
        description: 邀请码，可选，固定8位字符
        example: ABC12345
        type: string
      password:
        description: 密码，必填，最少8字符，最长60字符
        example: password123
        maxLength: 60
        minLength: 8
        type: string
      telephone:
        description: 手机号，可选，与邮箱二选一
        example: "13800138000"
        maxLength: 15
        type: string
      user_name:
        description: 用户名，必填，最长20字符
        example: john
        maxLength: 20
        type: string
    required:
    - password
    - user_name
    type: object
  fp-browser_api_v1_auth.RegisterResponse:
    properties:
      message:
        example: 用户注册成功！
        type: string
    type: object
  fp-browser_api_v1_auth.Response:
    properties:
      code:
        example: 200
        type: integer
      data: {}
      message:
        example: success
        type: string
    type: object
  fp-browser_api_v1_shared.GenerateCaptchaData:
    properties:
      captcha_image:
        description: base64编码的验证码图片
        type: string
      seed:
        description: 随机种子
        type: string
      target:
        description: PoW难度目标
        type: string
    type: object
  fp-browser_api_v1_shared.Response:
    properties:
      code:
        type: integer
      data: {}
      message:
        type: string
    type: object
  fp-browser_api_v1_shared.SendVerificationEmailData:
    properties:
      message:
        description: 响应消息
        type: string
      sent:
        description: 是否发送成功
        type: boolean
    type: object
  fp-browser_api_v1_shared.SendVerificationEmailRequest:
    properties:
      answer:
        description: 验证码答案
        type: string
      email:
        description: 邮箱地址
        type: string
      nonce:
        description: PoW随机数
        type: string
      seed:
        description: 验证码种子
        type: string
      target:
        description: 难度目标
        type: string
    required:
    - answer
    - email
    - nonce
    - seed
    - target
    type: object
  fp-browser_api_v1_shared.VerifyCaptchaData:
    properties:
      valid:
        description: 是否验证通过
        type: boolean
    type: object
  fp-browser_api_v1_shared.VerifyCaptchaRequest:
    properties:
      answer:
        description: 验证码答案
        type: string
      nonce:
        description: PoW随机数
        type: string
      seed:
        description: 种子
        type: string
      target:
        description: 难度目标
        type: string
    required:
    - answer
    - nonce
    - seed
    - target
    type: object
  fp-browser_api_v1_user.AddUsersToTeamRequest:
    properties:
      users:
        items:
          $ref: '#/definitions/fp-browser_api_v1_user.UserRequest'
        type: array
    required:
    - users
    type: object
  fp-browser_api_v1_user.AuthorizeLoginRequest:
    properties:
      username:
        description: 要授权的用户名
        example: user123
        type: string
    required:
    - username
    type: object
  fp-browser_api_v1_user.CancelOrderRequest:
    properties:
      order_id:
        example: 1
        type: integer
      order_number:
        example: SY20240101120000abc123
        type: string
    type: object
  fp-browser_api_v1_user.CancelOrderResponse:
    properties:
      message:
        example: 订单取消成功
        type: string
    type: object
  fp-browser_api_v1_user.CommissionSummary:
    properties:
      total_commission:
        example: 10000
        type: integer
      total_withdrawals:
        example: 3000
        type: integer
      withdrawable_amount:
        example: 7000
        type: integer
    type: object
  fp-browser_api_v1_user.CreateCommissionRequest:
    properties:
      amount:
        example: 1000
        type: integer
      currency:
        example: CNY
        type: string
      description:
        example: 推荐用户注册奖励
        type: string
      reference_id:
        example: 123
        type: integer
      transaction_type:
        example: 1
        type: integer
    required:
    - amount
    - currency
    - transaction_type
    type: object
  fp-browser_api_v1_user.CreateCommissionResponse:
    properties:
      message:
        example: 交易创建成功
        type: string
    type: object
  fp-browser_api_v1_user.CreateEnvironmentRequest:
    properties:
      comment:
        example: 测试环境
        type: string
      group_id:
        example: 1
        type: integer
      name:
        example: 开发环境
        maxLength: 100
        type: string
      parameters:
        example: '{}'
        type: string
      platform:
        example: https://amazon.com/
        type: string
      proxy_id:
        example: 1
        type: integer
      proxy_type:
        example: 1
        type: integer
      sort:
        example: 1
        type: integer
      storage:
        example: s3://bucket/path
        type: string
      tag:
        example: '{}'
        type: string
    required:
    - group_id
    - name
    - parameters
    - platform
    - proxy_id
    - proxy_type
    type: object
  fp-browser_api_v1_user.CreateEnvironmentResponse:
    properties:
      message:
        example: 环境创建成功
        type: string
    type: object
  fp-browser_api_v1_user.CreateGroupRequest:
    properties:
      name:
        example: 开发组
        type: string
    required:
    - name
    type: object
  fp-browser_api_v1_user.CreateGroupResponse:
    properties:
      group_id:
        type: integer
      message:
        type: string
    type: object
  fp-browser_api_v1_user.CreateOrderRequest:
    properties:
      duration:
        example: 30
        type: integer
      environment_count:
        example: 5
        type: integer
      member_count:
        example: 3
        type: integer
      order_type:
        example: 1
        type: integer
      payment_method:
        example: 1
        type: integer
      proxy_count:
        example: 10
        type: integer
      proxy_id:
        example: 1
        type: integer
      recharge:
        example: 10000
        type: integer
    required:
    - order_type
    - payment_method
    type: object
  fp-browser_api_v1_user.CreateOrderResponse:
    properties:
      amount:
        example: 10000
        type: integer
      order_id:
        example: 1
        type: integer
      order_number:
        example: SY20240101120000abc123
        type: string
      payment_url:
        example: https://pay.example.com/pay?order=123
        type: string
    type: object
  fp-browser_api_v1_user.CreateRoleRequest:
    properties:
      name:
        example: 管理员
        type: string
      permissions:
        example: '{"read": true, "write": true}'
        type: string
      secure:
        example: true
        type: boolean
    required:
    - name
    - permissions
    type: object
  fp-browser_api_v1_user.CreateSelfHostProxiesRequest:
    properties:
      proxies:
        items:
          $ref: '#/definitions/fp-browser_api_v1_user.CreateSelfHostProxyRequest'
        type: array
    required:
    - proxies
    type: object
  fp-browser_api_v1_user.CreateSelfHostProxyRequest:
    properties:
      environment_id:
        example: 1
        type: integer
      host:
        example: 127.0.0.1
        type: string
      name:
        example: My Proxy
        type: string
      password:
        example: password123
        type: string
      port:
        example: 8080
        type: integer
      type:
        example: 1
        type: integer
      username:
        example: admin
        type: string
    required:
    - host
    - name
    - port
    - type
    type: object
  fp-browser_api_v1_user.CreateTeamIPWhitelistRequest:
    properties:
      ip_address:
        description: IP地址或CIDR网段
        example: *************
        type: string
    required:
    - ip_address
    type: object
  fp-browser_api_v1_user.CreateTeamIPWhitelistsRequest:
    properties:
      ip_addresses:
        description: IP地址列表
        items:
          $ref: '#/definitions/fp-browser_api_v1_user.CreateTeamIPWhitelistRequest'
        minItems: 1
        type: array
    required:
    - ip_addresses
    type: object
  fp-browser_api_v1_user.CreateUserSubscriptionRequest:
    properties:
      end_date:
        example: "2024-12-31T23:59:59Z"
        type: string
      members_count:
        example: 10
        type: integer
      order_id:
        example: 1001
        type: integer
      start_date:
        example: "2024-01-01T00:00:00Z"
        type: string
      status:
        example: 1
        type: integer
      storage_size:
        example: 107374182400
        type: integer
      team_id:
        example: 1
        type: integer
      total_price:
        example: 99900
        type: integer
    required:
    - end_date
    - members_count
    - order_id
    - start_date
    - status
    - storage_size
    - team_id
    - total_price
    type: object
  fp-browser_api_v1_user.CreateUserSubscriptionResponse:
    properties:
      message:
        example: 订阅创建成功
        type: string
      subscription_id:
        example: 123
        type: integer
    type: object
  fp-browser_api_v1_user.CreateWalletTransactionRequest:
    properties:
      amount:
        example: 1000
        type: integer
      currency:
        example: CNY
        type: string
      description:
        example: 充值
        type: string
      reference_id:
        example: 123
        type: integer
      transaction_type:
        example: 1
        type: integer
    required:
    - amount
    - currency
    - transaction_type
    type: object
  fp-browser_api_v1_user.CreateWalletTransactionResponse:
    properties:
      message:
        example: 交易创建成功
        type: string
    type: object
  fp-browser_api_v1_user.DeleteEnvironmentFileRequest:
    properties:
      environment_id:
        description: 环境ID
        example: 1
        type: integer
    required:
    - environment_id
    type: object
  fp-browser_api_v1_user.DeleteEnvironmentRequest:
    properties:
      ids:
        items:
          type: integer
        type: array
        x-example: '[1,2,3]'
    type: object
  fp-browser_api_v1_user.DeleteEnvironmentResponse:
    properties:
      message:
        example: 环境记录删除成功
        type: string
    type: object
  fp-browser_api_v1_user.DeleteGroupRequest:
    properties:
      id:
        example: 1
        type: integer
    required:
    - id
    type: object
  fp-browser_api_v1_user.DeleteGroupResponse:
    properties:
      message:
        type: string
    type: object
  fp-browser_api_v1_user.DeleteRoleRequest:
    properties:
      role_id:
        example: 1
        type: integer
    required:
    - role_id
    type: object
  fp-browser_api_v1_user.DeleteSelfHostProxyRequest:
    properties:
      ids:
        items:
          type: integer
        type: array
        x-example: '[1,2,3]'
    required:
    - ids
    type: object
  fp-browser_api_v1_user.DeleteTeamIPWhitelistRequest:
    properties:
      ids:
        description: 要删除的IP白名单ID列表
        example:
        - 1
        - 2
        - 3
        items:
          type: integer
        minItems: 1
        type: array
    required:
    - ids
    type: object
  fp-browser_api_v1_user.DeleteUsersFromTeamRequest:
    properties:
      user_ids:
        items:
          type: integer
        type: array
    required:
    - user_ids
    type: object
  fp-browser_api_v1_user.FormattedUser:
    properties:
      email:
        example: <EMAIL>
        type: string
      id:
        example: 1
        type: integer
      is_active:
        example: true
        type: boolean
      is_owner:
        example: false
        type: boolean
      is_two_factor_enabled:
        example: false
        type: boolean
      role_name:
        example: 管理员
        type: string
      username:
        example: john
        type: string
    type: object
  fp-browser_api_v1_user.GenerateEnvironmentURLRequest:
    properties:
      environment_id:
        description: 环境ID
        example: 1
        type: integer
    required:
    - environment_id
    type: object
  fp-browser_api_v1_user.GenerateEnvironmentURLResponse:
    properties:
      expire_at:
        description: 过期时间戳
        example: 1625097600
        type: integer
      url:
        description: 文件URL
        example: https://oss.example.com/uploads/...
        type: string
    type: object
  fp-browser_api_v1_user.GetCommissionSummaryResponse:
    properties:
      summary:
        $ref: '#/definitions/fp-browser_api_v1_user.CommissionSummary'
    type: object
  fp-browser_api_v1_user.GetEnvironmentsResponse:
    properties:
      environments:
        items:
          $ref: '#/definitions/fp-browser_internal_view_model.EnvironmentListItem'
        type: array
      total:
        type: integer
    type: object
  fp-browser_api_v1_user.GetGroupsResponse:
    properties:
      groups:
        items:
          $ref: '#/definitions/fp-browser_internal_view_model.GroupWithUser'
        type: array
      total:
        type: integer
    type: object
  fp-browser_api_v1_user.GetLoginLogsResponse:
    properties:
      logs:
        items:
          $ref: '#/definitions/fp-browser_internal_view_model.LoginLogWithUser'
        type: array
      total:
        type: integer
    type: object
  fp-browser_api_v1_user.GetOperationLogsResponse:
    properties:
      logs:
        items:
          $ref: '#/definitions/fp-browser_internal_dao_model.OperationLog'
        type: array
      total:
        type: integer
    type: object
  fp-browser_api_v1_user.GetOrderPriceRequest:
    properties:
      duration:
        example: 30
        type: integer
      environment_count:
        example: 5
        type: integer
      member_count:
        example: 3
        type: integer
      order_type:
        example: 1
        type: integer
      proxy_count:
        example: 10
        type: integer
      proxy_id:
        example: 1
        type: integer
      recharge:
        example: 10000
        type: integer
    required:
    - order_type
    type: object
  fp-browser_api_v1_user.GetOrderPriceResponse:
    properties:
      total_price:
        example: 10000
        type: integer
    type: object
  fp-browser_api_v1_user.GetOrderResponse:
    properties:
      order:
        $ref: '#/definitions/fp-browser_internal_dao_model.Order'
    type: object
  fp-browser_api_v1_user.GetOrdersResponse:
    properties:
      orders:
        items:
          $ref: '#/definitions/fp-browser_internal_dao_model.Order'
        type: array
    type: object
  fp-browser_api_v1_user.GetPendingLoginRequestsResponse:
    properties:
      requests:
        description: 待授权请求列表
        items:
          $ref: '#/definitions/fp-browser_api_v1_user.PendingLoginRequest'
        type: array
      total:
        description: 总数量
        type: integer
    type: object
  fp-browser_api_v1_user.GetProfileResponse:
    properties:
      commission_rate:
        type: integer
      commission_type:
        type: integer
      company_name:
        type: string
      company_unified_social_code:
        type: string
      email:
        type: string
      environment_size_sum:
        type: integer
      id_card_number:
        type: string
      invite_code:
        type: string
      invite_user_id:
        type: integer
      is_active:
        type: boolean
      is_deleted:
        type: boolean
      is_two_factor_enabled:
        type: boolean
      real_name:
        type: string
      real_name_type:
        type: integer
      role:
        $ref: '#/definitions/fp-browser_api_v1_user.ProfileRole'
      subscription:
        $ref: '#/definitions/fp-browser_api_v1_user.ProfileSubscription'
      team:
        $ref: '#/definitions/fp-browser_api_v1_user.ProfileTeam'
      telephone:
        type: string
      two_factor_secret:
        type: string
      user_name:
        type: string
      wallet_amount:
        type: integer
    type: object
  fp-browser_api_v1_user.GetRolesResponse:
    properties:
      roles:
        items:
          $ref: '#/definitions/fp-browser_internal_dao_model.Role'
        type: array
    type: object
  fp-browser_api_v1_user.GetSelfHostProxiesResponse:
    properties:
      proxies:
        items:
          $ref: '#/definitions/fp-browser_api_v1_user.SelfHostProxyItem'
        type: array
      total:
        type: integer
    type: object
  fp-browser_api_v1_user.GetSubscriptionsResponse:
    properties:
      subscriptions:
        items:
          $ref: '#/definitions/fp-browser_internal_dao_model.Subscription'
        type: array
    type: object
  fp-browser_api_v1_user.GetTeamIPWhitelistsResponse:
    properties:
      total:
        description: 总数量
        type: integer
      whitelists:
        description: IP白名单列表
        items:
          $ref: '#/definitions/fp-browser_internal_dao_model.TeamIPWhitelist'
        type: array
    type: object
  fp-browser_api_v1_user.GetTransactionsResponse:
    properties:
      transactions:
        items:
          $ref: '#/definitions/fp-browser_internal_dao_model.CommissionTransaction'
        type: array
    type: object
  fp-browser_api_v1_user.GetUserListResponse:
    properties:
      total:
        type: integer
      users:
        items:
          $ref: '#/definitions/fp-browser_api_v1_user.FormattedUser'
        type: array
    type: object
  fp-browser_api_v1_user.GetUserSubscriptionResponse:
    properties:
      subscription:
        $ref: '#/definitions/fp-browser_internal_dao_model.UserSubscription'
    type: object
  fp-browser_api_v1_user.GetUserSubscriptionsResponse:
    properties:
      subscriptions:
        items:
          $ref: '#/definitions/fp-browser_internal_dao_model.UserSubscription'
        type: array
    type: object
  fp-browser_api_v1_user.GetWalletBalanceResponse:
    properties:
      total_balance:
        example: 5000
        type: integer
    type: object
  fp-browser_api_v1_user.GetWalletTransactionsResponse:
    properties:
      transactions:
        items:
          $ref: '#/definitions/fp-browser_internal_dao_model.WalletTransaction'
        type: array
    type: object
  fp-browser_api_v1_user.GetWithdrawalTransactionsResponse:
    properties:
      transactions:
        items:
          $ref: '#/definitions/fp-browser_internal_dao_model.CommissionTransaction'
        type: array
    type: object
  fp-browser_api_v1_user.PendingLoginRequest:
    properties:
      request_at:
        description: 请求时间
        type: string
      status:
        description: 状态：pending/authorized
        type: string
      ttl:
        description: 剩余生存时间（秒）
        type: integer
      username:
        description: 用户名
        type: string
    type: object
  fp-browser_api_v1_user.ProfileRole:
    properties:
      name:
        type: string
      permissions:
        type: string
      secure:
        type: boolean
    type: object
  fp-browser_api_v1_user.ProfileSubscription:
    properties:
      end_date:
        type: string
      members_count:
        type: integer
      order_id:
        type: integer
      start_date:
        type: string
      status:
        type: integer
      storage_size:
        type: integer
      total_price:
        type: integer
    type: object
  fp-browser_api_v1_user.ProfileTeam:
    properties:
      name:
        type: string
      owner_id:
        type: integer
    type: object
  fp-browser_api_v1_user.PurchaseSubscriptionRequest:
    properties:
      quantity:
        example: 1
        minimum: 1
        type: integer
      subscription_id:
        example: 101
        type: integer
      team_id:
        example: 1
        type: integer
    required:
    - quantity
    - subscription_id
    - team_id
    type: object
  fp-browser_api_v1_user.PurchaseSubscriptionResponse:
    properties:
      message:
        example: 订阅购买成功
        type: string
      order:
        $ref: '#/definitions/fp-browser_internal_dao_model.Order'
    type: object
  fp-browser_api_v1_user.RefreshEnvironmentResponse:
    properties:
      message:
        example: 环境记录刷新成功
        type: string
    type: object
  fp-browser_api_v1_user.RemoveEnvironmentResponse:
    properties:
      message:
        example: 环境记录移除成功
        type: string
    type: object
  fp-browser_api_v1_user.Response:
    properties:
      code:
        type: integer
      data: {}
      message:
        type: string
    type: object
  fp-browser_api_v1_user.RestoreEnvironmentResponse:
    properties:
      message:
        type: string
    type: object
  fp-browser_api_v1_user.SelfHostProxyItem:
    properties:
      environment_name:
        description: 所属环境名称
        type: string
      host:
        description: 代理主机
        type: string
      id:
        type: integer
      name:
        description: 代理名称
        type: string
      port:
        description: 代理端口
        type: integer
      team_name:
        description: 所属团队名
        type: string
      type:
        description: 代理类型
        type: integer
    type: object
  fp-browser_api_v1_user.SelfHostProxyResponse:
    properties:
      environment_id:
        example: 888
        type: integer
      host:
        example: 127.0.0.1
        type: string
      id:
        example: 1
        type: integer
      name:
        example: 家庭代理
        type: string
      password:
        example: pass123
        type: string
      port:
        example: 1080
        type: integer
      team_id:
        example: 99
        type: integer
      type:
        description: '1: HTTP, 2: HTTPS, 3: SOCKS5'
        example: 3
        type: integer
      username:
        example: user1
        type: string
    type: object
  fp-browser_api_v1_user.UpdateEnvironmentItem:
    properties:
      comment:
        example: 更新测试环境
        type: string
      group_id:
        example: 1
        type: integer
      id:
        example: 1
        type: integer
      name:
        example: 更新环境
        maxLength: 100
        type: string
      parameters:
        example: '{}'
        type: string
      platform:
        example: https://amazon.com/
        type: string
      proxy_id:
        example: 1
        type: integer
      proxy_type:
        example: 1
        type: integer
      sort:
        example: 2
        type: integer
      storage:
        example: s3://bucket/path
        type: string
      tag:
        example: '{}'
        type: string
    required:
    - group_id
    - id
    - name
    - parameters
    - platform
    - proxy_id
    - proxy_type
    type: object
  fp-browser_api_v1_user.UpdateEnvironmentProxyRequest:
    properties:
      ids:
        description: 环境ID列表
        items:
          type: integer
        type: array
        x-example: '[1,2,3]'
      proxy_id:
        description: 新的代理ID
        example: 5
        type: integer
    required:
    - ids
    - proxy_id
    type: object
  fp-browser_api_v1_user.UpdateEnvironmentProxyResponse:
    properties:
      message:
        example: 环境代理更新成功
        type: string
    type: object
  fp-browser_api_v1_user.UpdateEnvironmentRequest:
    properties:
      environments:
        items:
          $ref: '#/definitions/fp-browser_api_v1_user.UpdateEnvironmentItem'
        type: array
    required:
    - environments
    type: object
  fp-browser_api_v1_user.UpdateEnvironmentResponse:
    properties:
      message:
        example: 环境记录更新成功
        type: string
    type: object
  fp-browser_api_v1_user.UpdateGroupRequest:
    properties:
      id:
        example: 1
        type: integer
      name:
        example: 测试组
        type: string
    required:
    - id
    - name
    type: object
  fp-browser_api_v1_user.UpdateGroupResponse:
    properties:
      message:
        type: string
    type: object
  fp-browser_api_v1_user.UpdateProfileRequest:
    properties:
      email:
        example: <EMAIL>
        type: string
      email_code:
        example: "123456"
        type: string
      enable_2fa:
        example: true
        type: boolean
      telephone:
        example: "13900139000"
        type: string
      telephone_code:
        example: "654321"
        type: string
    type: object
  fp-browser_api_v1_user.UpdateProfileResponse:
    properties:
      message:
        example: 用户信息更新成功
        type: string
    type: object
  fp-browser_api_v1_user.UpdateRoleRequest:
    properties:
      id:
        example: 1
        type: integer
      name:
        example: 管理员
        type: string
      permissions:
        example: '{"read": true, "write": true}'
        type: string
      secure:
        example: true
        type: boolean
    required:
    - id
    - name
    - permissions
    type: object
  fp-browser_api_v1_user.UpdateSelfHostProxiesRequest:
    properties:
      proxies:
        items:
          $ref: '#/definitions/fp-browser_api_v1_user.UpdateSelfHostProxyRequest'
        type: array
    required:
    - proxies
    type: object
  fp-browser_api_v1_user.UpdateSelfHostProxyRequest:
    properties:
      environment_id:
        example: 1
        type: integer
      host:
        example: 127.0.0.1
        type: string
      id:
        example: 1
        type: integer
      name:
        example: My Proxy Updated
        type: string
      password:
        example: password123
        type: string
      port:
        example: 8080
        type: integer
      type:
        example: 1
        type: integer
      username:
        example: admin
        type: string
    required:
    - host
    - id
    - name
    - port
    - type
    type: object
  fp-browser_api_v1_user.UpdateTeamRequest:
    properties:
      name:
        example: 新团队名称
        type: string
    required:
    - name
    type: object
  fp-browser_api_v1_user.UpdateUserRequest:
    properties:
      id:
        example: 1
        type: integer
      is_active:
        example: true
        type: boolean
      password:
        example: password123
        type: string
      role_id:
        example: 1
        type: integer
      user_name:
        example: john
        type: string
    required:
    - id
    type: object
  fp-browser_api_v1_user.UpdateUserSubscriptionRequest:
    properties:
      end_date:
        example: "2024-12-31T23:59:59Z"
        type: string
      members_count:
        example: 20
        type: integer
      start_date:
        example: "2024-01-01T00:00:00Z"
        type: string
      status:
        example: 2
        type: integer
      storage_size:
        example: 214748364800
        type: integer
      total_price:
        example: 199900
        type: integer
    type: object
  fp-browser_api_v1_user.UpdateUserSubscriptionResponse:
    properties:
      message:
        example: 订阅更新成功
        type: string
    type: object
  fp-browser_api_v1_user.UpdateUsersInTeamRequest:
    properties:
      users:
        items:
          $ref: '#/definitions/fp-browser_api_v1_user.UpdateUserRequest'
        type: array
    required:
    - users
    type: object
  fp-browser_api_v1_user.UserRequest:
    properties:
      is_active:
        example: true
        type: boolean
      password:
        example: password123
        type: string
      role_id:
        example: 1
        type: integer
      user_name:
        example: john
        type: string
    required:
    - password
    - role_id
    - user_name
    type: object
  fp-browser_internal_dao_model.CommissionTransaction:
    properties:
      amount:
        description: 交易金额，正数为收入，负数为支出
        type: integer
      created_at:
        description: 记录创建时间
        type: string
      currency:
        description: 货币类型，ISO 4217 标准，如 CNY, USD
        type: string
      description:
        description: 交易说明文本，最大100字符
        type: string
      id:
        description: 交易ID，自增主键
        type: integer
      invited_user_id:
        description: 被邀请的用户ID
        type: integer
      reference_id:
        description: 相关订单ID，0 表示无关联订单
        type: integer
      transaction_type:
        description: 交易类型，1返利，2提现，3退款
        type: integer
      unlock_time:
        description: 资金解锁时间，到账前资金冻结
        type: string
      user_id:
        description: 推荐人用户ID
        type: integer
    type: object
  fp-browser_internal_dao_model.OperationLog:
    properties:
      action:
        description: 操作行为编码
        type: integer
      category:
        description: 操作分类标识
        type: integer
      created_at:
        description: 操作事件发生的时间戳
        type: string
      id:
        description: 日志 ID，自增主键
        type: integer
      target:
        description: 操作目标标识，如资源名称或 ID
        type: string
      team_id:
        description: 操作所属团队 ID
        type: integer
      user_id:
        description: 操作发起人用户 ID
        type: integer
    type: object
  fp-browser_internal_dao_model.Order:
    properties:
      amount:
        description: 应支付金额（单位分）
        type: integer
      balance_amount:
        description: 余额抵扣数量
        type: integer
      coupon_id:
        description: 使用的优惠券ID
        type: integer
      created_at:
        description: 订单创建时间
        type: string
      currency:
        description: 货币类型（ISO 4217）
        type: string
      expires_at:
        description: 订单过期时间
        type: string
      id:
        description: 订单主键 ID
        type: integer
      order_content:
        description: 订单详细内容，JSONB 格式
        type: string
      order_number:
        description: 唯一订单号
        type: string
      order_type:
        description: 订单类型（1订阅, 2代理, 3充值）
        type: integer
      payment_method:
        description: 支付方式（1余额, 2Stripe, 3支付宝）
        type: integer
      real_amount:
        description: 实际支付金额（单位分）
        type: integer
      status:
        description: 订单状态（1待支付, 2已支付, 3失败, 4取消）
        type: integer
      team_id:
        description: 用户所属团队 ID
        type: integer
      updated_at:
        description: 订单更新时间
        type: string
      url:
        description: 支付网关的支付链接
        type: string
      user_id:
        description: 下单用户 ID
        type: integer
    type: object
  fp-browser_internal_dao_model.Role:
    properties:
      created_at:
        description: 创建时间
        type: string
      id:
        description: 角色 ID，自增主键
        type: integer
      name:
        description: 角色名称，最长 50 字符
        type: string
      permissions:
        description: 权限信息，JSONB 格式存储，如访问路径、方法等
        type: string
      secure:
        description: 是否开启安全登录
        type: boolean
      team_id:
        description: 所属团队 ID
        type: integer
      updated_at:
        description: 最近更新时间
        type: string
    type: object
  fp-browser_internal_dao_model.Subscription:
    properties:
      created_at:
        description: 创建时间
        type: string
      currency:
        description: 币种，ISO 4217 代码，如 CNY, USD, JPY
        type: string
      id:
        type: integer
      membercount:
        description: 成员数量限制
        type: integer
      name:
        description: 本地化名称，使用 JSONB 存储
        type: string
      price:
        description: 价格金额
        type: number
      storagesize:
        description: 存储容量限制，单位为字节（Byte）
        type: integer
      updated_at:
        description: 更新时间
        type: string
    type: object
  fp-browser_internal_dao_model.TeamIPWhitelist:
    properties:
      created_at:
        description: 创建时间
        type: string
      id:
        type: integer
      ip_address:
        description: 允许登录的 IP 地址
        type: string
      team_id:
        description: 关联的团队 ID
        type: integer
    type: object
  fp-browser_internal_dao_model.UserSubscription:
    properties:
      created_at:
        description: 创建时间
        type: string
      end_date:
        description: 订阅结束时间
        type: string
      id:
        description: 订阅记录主键 ID
        type: integer
      members_count:
        description: 订阅成员数量限制
        type: integer
      order_id:
        description: 对应订单 ID
        type: integer
      start_date:
        description: 订阅开始时间
        type: string
      status:
        description: '订阅状态（1: Active, 2: Expired, 3: Cancelled, 4: Pending）'
        type: integer
      storage_size:
        description: 容量限制
        type: integer
      team_id:
        description: 团队 ID
        type: integer
      total_price:
        description: 购买总价（单位分）
        type: integer
      updated_at:
        description: 更新时间
        type: string
    type: object
  fp-browser_internal_dao_model.WalletTransaction:
    properties:
      amount:
        description: 交易金额，单位为分，正数为收入，负数为支出
        type: integer
      created_at:
        description: 交易记录创建时间
        type: string
      currency:
        description: 货币类型，ISO 4217 编码（如 CNY, USD）
        type: string
      description:
        description: 交易说明，最长 100 字符
        type: string
      id:
        description: 主键，自增交易 ID
        type: integer
      reference_id:
        description: 相关订单或对象的引用 ID，默认 0 表示无
        type: integer
      transaction_type:
        description: 交易类型（1充值，2消费，3退款等）
        type: integer
      user_id:
        description: 用户 ID
        type: integer
    type: object
  fp-browser_internal_view_model.EnvironmentListItem:
    properties:
      comment:
        type: string
      created_at:
        type: string
      group_id:
        type: integer
      group_name:
        type: string
      id:
        type: integer
      name:
        type: string
      platform:
        type: string
      proxy:
        $ref: '#/definitions/fp-browser_internal_view_model.ProxyInfo'
      proxy_type:
        type: integer
      size:
        type: integer
      sort:
        type: integer
      storage:
        type: string
      tag:
        type: string
      team_id:
        type: integer
      updated_at:
        type: string
      user_id:
        type: integer
    type: object
  fp-browser_internal_view_model.EnvironmentWithProxy:
    properties:
      comment:
        description: 详情中包含备注
        type: string
      group_id:
        type: integer
      group_name:
        type: string
      id:
        type: integer
      name:
        type: string
      parameters:
        description: 详情中包含参数
        type: string
      platform:
        type: string
      proxy:
        $ref: '#/definitions/fp-browser_internal_view_model.ProxyInfo'
      proxy_type:
        type: integer
      storage:
        type: string
      tag:
        type: string
      team_id:
        type: integer
      user_id:
        type: integer
    type: object
  fp-browser_internal_view_model.GroupWithUser:
    properties:
      created_at:
        type: string
      id:
        type: integer
      name:
        type: string
      team_id:
        type: integer
      updated_at:
        type: string
      user_name:
        description: 原来的 UserID 替换成用户名
        type: string
    type: object
  fp-browser_internal_view_model.LoginLogWithUser:
    properties:
      created_at:
        type: string
      id:
        type: integer
      ip_location:
        type: string
      login_ip:
        type: string
      user_name:
        type: string
    type: object
  fp-browser_internal_view_model.ProxyInfo:
    properties:
      address:
        type: string
      name:
        type: string
      password:
        type: string
      port:
        type: integer
      type:
        type: integer
      username:
        type: string
    type: object
host: localhost:8000
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: http://www.swagger.io/support
  description: This is the FP-Browser API service.
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  termsOfService: http://swagger.io/terms/
  title: FP-Browser API
  version: 1.0.0
paths:
  /api/v1/auth/register:
    post:
      consumes:
      - application/json
      description: 用户注册，创建新用户和对应的团队。必须提供用户名，邮箱和手机号二选一，密码不少于8位，需要邮箱验证码
      parameters:
      - description: 注册信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/fp-browser_api_v1_auth.RegisterRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created
          schema:
            $ref: '#/definitions/fp-browser_api_v1_auth.RegisterResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fp-browser_api_v1_auth.Response'
        "409":
          description: 邮箱/电话/用户名已被占用
          schema:
            $ref: '#/definitions/fp-browser_api_v1_auth.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_auth.Response'
      summary: 用户注册
      tags:
      - 用户认证模块
  /api/v1/captcha/generate:
    post:
      consumes:
      - application/json
      description: 生成验证码图片和PoW挑战，返回验证码图片、种子和难度目标
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/fp-browser_api_v1_shared.Response'
            - properties:
                data:
                  $ref: '#/definitions/fp-browser_api_v1_shared.GenerateCaptchaData'
              type: object
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_shared.Response'
      summary: 生成验证码
      tags:
      - 验证码模块
  /api/v1/captcha/verify:
    post:
      consumes:
      - application/json
      description: 验证验证码答案和PoW工作量证明
      parameters:
      - description: 验证请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/fp-browser_api_v1_shared.VerifyCaptchaRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/fp-browser_api_v1_shared.Response'
            - properties:
                data:
                  $ref: '#/definitions/fp-browser_api_v1_shared.VerifyCaptchaData'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fp-browser_api_v1_shared.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_shared.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_shared.Response'
      summary: 验证验证码
      tags:
      - 验证码模块
  /api/v1/commissions:
    get:
      consumes:
      - application/json
      description: 根据用户ID获取佣金交易记录，支持分页和时间范围筛选
      parameters:
      - description: 每页数量，默认100，最大500
        in: query
        maximum: 500
        minimum: 1
        name: limit
        type: integer
      - description: 偏移量，默认0
        in: query
        minimum: 0
        name: offset
        type: integer
      - description: 开始时间，RFC3339格式
        format: date-time
        in: query
        name: start_time
        type: string
      - description: 结束时间，RFC3339格式
        format: date-time
        in: query
        name: end_time
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.GetTransactionsResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 获取佣金交易记录
      tags:
      - 佣金模块
    post:
      consumes:
      - application/json
      description: 创建新的佣金交易记录
      parameters:
      - description: 佣金交易信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/fp-browser_api_v1_user.CreateCommissionRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.CreateCommissionResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 创建佣金交易记录
      tags:
      - 佣金模块
  /api/v1/commissions/summary:
    get:
      consumes:
      - application/json
      description: 获取用户的佣金摘要信息，包括总佣金、总提现、可提现金额
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.GetCommissionSummaryResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 获取佣金摘要信息
      tags:
      - 佣金模块
  /api/v1/commissions/withdrawable:
    get:
      consumes:
      - application/json
      description: 获取用户当前可以提现的佣金记录
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.GetWithdrawalTransactionsResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 获取可提现的佣金记录
      tags:
      - 佣金模块
  /api/v1/email/send-login-verification:
    post:
      consumes:
      - application/json
      description: 验证captcha和PoW后，发送登录验证邮件
      parameters:
      - description: 发送验证邮件请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/fp-browser_api_v1_shared.SendVerificationEmailRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/fp-browser_api_v1_shared.Response'
            - properties:
                data:
                  $ref: '#/definitions/fp-browser_api_v1_shared.SendVerificationEmailData'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fp-browser_api_v1_shared.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_shared.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_shared.Response'
      summary: 发送登录验证邮件
      tags:
      - 邮件模块
  /api/v1/email/send-register-verification:
    post:
      consumes:
      - application/json
      description: 验证captcha和PoW后，发送注册验证邮件
      parameters:
      - description: 发送验证邮件请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/fp-browser_api_v1_shared.SendVerificationEmailRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/fp-browser_api_v1_shared.Response'
            - properties:
                data:
                  $ref: '#/definitions/fp-browser_api_v1_shared.SendVerificationEmailData'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fp-browser_api_v1_shared.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_shared.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_shared.Response'
      summary: 发送注册验证邮件
      tags:
      - 邮件模块
  /api/v1/email/send-reset-verification:
    post:
      consumes:
      - application/json
      description: 验证captcha和PoW后，发送重置密码验证邮件
      parameters:
      - description: 发送验证邮件请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/fp-browser_api_v1_shared.SendVerificationEmailRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/fp-browser_api_v1_shared.Response'
            - properties:
                data:
                  $ref: '#/definitions/fp-browser_api_v1_shared.SendVerificationEmailData'
              type: object
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fp-browser_api_v1_shared.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_shared.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_shared.Response'
      summary: 发送重置密码验证邮件
      tags:
      - 邮件模块
  /api/v1/environments:
    delete:
      consumes:
      - application/json
      description: 批量删除多个环境记录
      parameters:
      - description: 环境ID列表
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/fp-browser_api_v1_user.DeleteEnvironmentRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.DeleteEnvironmentResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 批量删除环境
      tags:
      - 环境模块
    get:
      consumes:
      - application/json
      description: 根据团队ID获取环境记录，支持分页和筛选
      parameters:
      - description: 环境名称筛选
        in: query
        name: name
        type: string
      - description: 用户ID筛选
        in: query
        name: user_id
        type: integer
      - description: 分组ID筛选
        in: query
        name: group_id
        type: integer
      - description: 每页数量，默认100，最大500
        in: query
        maximum: 500
        minimum: 1
        name: limit
        type: integer
      - description: 偏移量，默认0
        in: query
        minimum: 0
        name: offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.GetEnvironmentsResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 获取环境列表
      tags:
      - 环境模块
    post:
      consumes:
      - application/json
      description: 创建新的环境记录
      parameters:
      - description: 环境信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/fp-browser_api_v1_user.CreateEnvironmentRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.CreateEnvironmentResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 创建环境
      tags:
      - 环境模块
    put:
      consumes:
      - application/json
      description: 批量更新多个环境记录
      parameters:
      - description: 环境信息列表
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/fp-browser_api_v1_user.UpdateEnvironmentRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.UpdateEnvironmentResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 批量更新环境
      tags:
      - 环境模块
  /api/v1/environments/{id}:
    get:
      consumes:
      - application/json
      description: 根据环境ID获取环境记录，包含Redis占用检查
      parameters:
      - description: 环境ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_internal_view_model.EnvironmentWithProxy'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "409":
          description: 环境已被占用
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 根据ID获取环境
      tags:
      - 环境模块
  /api/v1/environments/{id}/download:
    get:
      consumes:
      - application/json
      description: 服务器从OSS获取环境文件并流式传输给客户端
      parameters:
      - description: 环境ID
        in: path
        name: id
        required: true
        type: integer
      - description: 下载令牌
        in: query
        name: token
        required: true
        type: string
      produces:
      - application/octet-stream
      responses:
        "200":
          description: 环境文件内容
          schema:
            type: file
        "400":
          description: 错误信息
          schema:
            additionalProperties: true
            type: object
        "401":
          description: 未授权
          schema:
            additionalProperties: true
            type: object
        "404":
          description: 文件不存在
          schema:
            additionalProperties: true
            type: object
        "500":
          description: 服务器错误
          schema:
            additionalProperties: true
            type: object
      summary: 下载环境文件
      tags:
      - 环境文件
  /api/v1/environments/{id}/refresh:
    post:
      consumes:
      - application/json
      description: 刷新环境在Redis中的占用记录
      parameters:
      - description: 环境ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.RefreshEnvironmentResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 刷新环境Redis记录
      tags:
      - 环境模块
  /api/v1/environments/{id}/remove:
    delete:
      consumes:
      - application/json
      description: 移除环境在Redis中的占用记录
      parameters:
      - description: 环境ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.RemoveEnvironmentResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 移除环境Redis记录
      tags:
      - 环境模块
  /api/v1/environments/deleted:
    get:
      consumes:
      - application/json
      description: 获取团队下已删除的环境记录，支持分页
      parameters:
      - default: 100
        description: 每页数量，默认100，最大500
        in: query
        maximum: 500
        minimum: 1
        name: limit
        type: integer
      - default: 0
        description: 偏移量，默认0
        in: query
        minimum: 0
        name: offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.GetEnvironmentsResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 获取已删除的环境列表
      tags:
      - 环境模块
  /api/v1/environments/file:
    delete:
      consumes:
      - application/json
      description: 删除环境文件并清除环境的存储路径
      parameters:
      - description: 用户令牌
        in: header
        name: Authorization
        required: true
        type: string
      - description: 请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/fp-browser_api_v1_user.DeleteEnvironmentFileRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 成功信息
          schema:
            additionalProperties: true
            type: object
        "400":
          description: 错误信息
          schema:
            additionalProperties: true
            type: object
        "401":
          description: 未授权
          schema:
            additionalProperties: true
            type: object
        "500":
          description: 服务器错误
          schema:
            additionalProperties: true
            type: object
      summary: 删除环境文件
      tags:
      - 环境文件
  /api/v1/environments/file/upload-url:
    post:
      consumes:
      - application/json
      description: 生成环境文件上传URL
      parameters:
      - description: 用户令牌
        in: header
        name: Authorization
        required: true
        type: string
      - description: 请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/fp-browser_api_v1_user.GenerateEnvironmentURLRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.GenerateEnvironmentURLResponse'
        "400":
          description: 错误信息
          schema:
            additionalProperties: true
            type: object
        "401":
          description: 未授权
          schema:
            additionalProperties: true
            type: object
        "500":
          description: 服务器错误
          schema:
            additionalProperties: true
            type: object
      summary: 生成环境文件上传URL
      tags:
      - 环境文件
  /api/v1/environments/permanent:
    delete:
      consumes:
      - application/json
      description: 批量永久删除环境记录，此操作不可逆
      parameters:
      - description: 环境ID列表
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/fp-browser_api_v1_user.DeleteEnvironmentRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.DeleteEnvironmentResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 永久删除环境
      tags:
      - 环境模块
  /api/v1/environments/proxy:
    put:
      consumes:
      - application/json
      description: 批量更新多个环境的代理ID
      parameters:
      - description: 环境ID列表和代理ID
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/fp-browser_api_v1_user.UpdateEnvironmentProxyRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.UpdateEnvironmentProxyResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 批量更新环境代理
      tags:
      - 环境模块
  /api/v1/environments/restore:
    put:
      consumes:
      - application/json
      description: 批量恢复已删除的环境记录
      parameters:
      - description: 环境ID列表
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/fp-browser_api_v1_user.DeleteEnvironmentRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.RestoreEnvironmentResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 恢复已删除的环境
      tags:
      - 环境模块
  /api/v1/groups:
    delete:
      consumes:
      - application/json
      description: 删除指定的分组
      parameters:
      - description: 删除分组信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/fp-browser_api_v1_user.DeleteGroupRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.DeleteGroupResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 删除分组
      tags:
      - 分组模块
    get:
      consumes:
      - application/json
      description: 根据团队ID获取分组列表，支持按用户筛选
      parameters:
      - description: 用户ID筛选，为0时获取团队所有分组
        in: query
        name: user_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.GetGroupsResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 获取分组列表
      tags:
      - 分组模块
    post:
      consumes:
      - application/json
      description: 创建一个新的分组
      parameters:
      - description: 分组信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/fp-browser_api_v1_user.CreateGroupRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.CreateGroupResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 创建新的分组
      tags:
      - 分组模块
    put:
      consumes:
      - application/json
      description: 更新指定分组的名称
      parameters:
      - description: 更新分组信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/fp-browser_api_v1_user.UpdateGroupRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.UpdateGroupResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 更新分组信息
      tags:
      - 分组模块
  /api/v1/login:
    post:
      consumes:
      - application/json
      description: 用户使用用户名和密码登录系统，支持IP白名单和授权机制
      parameters:
      - description: 登录请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/fp-browser_api_v1_auth.LoginRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_auth.LoginResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fp-browser_api_v1_auth.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_auth.Response'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/fp-browser_api_v1_auth.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_auth.Response'
      summary: 用户登录
      tags:
      - 用户认证模块
  /api/v1/login-logs:
    get:
      consumes:
      - application/json
      description: 根据团队ID和其他条件获取登录日志记录，支持分页和时间范围筛选
      parameters:
      - description: 用户ID筛选
        in: query
        name: user_id
        type: integer
      - description: 每页数量，默认100，最大500
        in: query
        maximum: 500
        minimum: 1
        name: limit
        type: integer
      - description: 偏移量，默认0
        in: query
        minimum: 0
        name: offset
        type: integer
      - description: 开始时间，RFC3339格式
        format: date-time
        in: query
        name: start_time
        type: string
      - description: 结束时间，RFC3339格式
        format: date-time
        in: query
        name: end_time
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.GetLoginLogsResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 获取登录日志
      tags:
      - 登录日志模块
  /api/v1/operation-logs:
    get:
      consumes:
      - application/json
      description: 根据团队ID和其他条件获取操作日志记录，支持分页和多种筛选条件
      parameters:
      - description: 用户ID筛选
        in: query
        name: user_id
        type: integer
      - description: 操作类型筛选
        in: query
        name: action
        type: integer
      - description: 操作分类筛选
        in: query
        name: category
        type: integer
      - description: 操作目标筛选
        in: query
        name: target
        type: string
      - description: 每页数量，默认100，最大500
        in: query
        maximum: 500
        minimum: 1
        name: limit
        type: integer
      - description: 偏移量，默认0
        in: query
        minimum: 0
        name: offset
        type: integer
      - description: 开始时间，RFC3339格式
        format: date-time
        in: query
        name: start_time
        type: string
      - description: 结束时间，RFC3339格式
        format: date-time
        in: query
        name: end_time
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.GetOperationLogsResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 获取操作日志
      tags:
      - 操作日志模块
  /api/v1/orders:
    get:
      consumes:
      - application/json
      description: 获取用户的订单列表，支持分页和筛选
      parameters:
      - description: 订单类型
        in: query
        name: order_type
        type: integer
      - description: 订单状态
        in: query
        name: status
        type: integer
      - description: 支付方式
        in: query
        name: payment_method
        type: integer
      - description: 每页数量，默认100，最大500
        in: query
        maximum: 500
        minimum: 1
        name: limit
        type: integer
      - description: 偏移量，默认0
        in: query
        minimum: 0
        name: offset
        type: integer
      - description: 开始时间，RFC3339格式
        format: date-time
        in: query
        name: start_time
        type: string
      - description: 结束时间，RFC3339格式
        format: date-time
        in: query
        name: end_time
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.GetOrdersResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 获取订单列表
      tags:
      - 订单模块
    post:
      consumes:
      - application/json
      description: 创建新订单并处理支付
      parameters:
      - description: 订单信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/fp-browser_api_v1_user.CreateOrderRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.CreateOrderResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 创建订单
      tags:
      - 订单模块
  /api/v1/orders/cancel:
    post:
      consumes:
      - application/json
      description: 取消指定的订单
      parameters:
      - description: 取消订单信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/fp-browser_api_v1_user.CancelOrderRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.CancelOrderResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 取消订单
      tags:
      - 订单模块
  /api/v1/orders/detail:
    get:
      consumes:
      - application/json
      description: 根据订单ID或订单号获取单个订单信息
      parameters:
      - description: 订单ID
        in: query
        name: order_id
        type: integer
      - description: 订单号
        in: query
        name: order_number
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.GetOrderResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 获取订单信息
      tags:
      - 订单模块
  /api/v1/orders/price:
    post:
      consumes:
      - application/json
      description: 根据订单信息计算总价格
      parameters:
      - description: 订单信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/fp-browser_api_v1_user.GetOrderPriceRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.GetOrderPriceResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 计算订单价格
      tags:
      - 订单模块
  /api/v1/roles:
    delete:
      consumes:
      - application/json
      description: 根据角色ID删除指定的角色
      parameters:
      - description: 删除角色信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/fp-browser_api_v1_user.DeleteRoleRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 删除角色
      tags:
      - 角色模块
    get:
      consumes:
      - application/json
      description: 根据团队ID和可选的角色ID获取角色记录
      parameters:
      - description: 角色ID，为0时获取所有角色
        in: query
        name: id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.GetRolesResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 获取角色列表
      tags:
      - 角色模块
    post:
      consumes:
      - application/json
      description: 创建一个新的角色，包含名称、权限信息和安全登录设置
      parameters:
      - description: 角色信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/fp-browser_api_v1_user.CreateRoleRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 创建新的角色
      tags:
      - 角色模块
    put:
      consumes:
      - application/json
      description: 更新指定角色的名称、权限信息和安全登录设置
      parameters:
      - description: 更新角色信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/fp-browser_api_v1_user.UpdateRoleRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 更新角色信息
      tags:
      - 角色模块
  /api/v1/self-host-proxies:
    delete:
      consumes:
      - application/json
      description: 删除一个或多个自托管代理
      parameters:
      - description: 代理ID列表
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/fp-browser_api_v1_user.DeleteSelfHostProxyRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 删除自托管代理
      tags:
      - 自托管代理模块
    get:
      consumes:
      - application/json
      description: 根据多条件筛选获取自托管代理记录列表，支持分页
      parameters:
      - description: 代理ID筛选
        in: query
        name: id
        type: integer
      - description: 环境ID筛选
        in: query
        name: environment_id
        type: integer
      - description: 代理类型筛选
        in: query
        name: type
        type: integer
      - description: 代理名称筛选
        in: query
        name: name
        type: string
      - description: 每页数量，默认100，最大500
        in: query
        maximum: 500
        minimum: 1
        name: limit
        type: integer
      - description: 偏移量，默认0
        in: query
        minimum: 0
        name: offset
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.GetSelfHostProxiesResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 获取自托管代理列表
      tags:
      - 自托管代理模块
    post:
      consumes:
      - application/json
      description: 创建新的自托管代理
      parameters:
      - description: 代理信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/fp-browser_api_v1_user.CreateSelfHostProxyRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 创建自托管代理
      tags:
      - 自托管代理模块
    put:
      consumes:
      - application/json
      description: 更新自托管代理信息
      parameters:
      - description: 代理信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/fp-browser_api_v1_user.UpdateSelfHostProxyRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 更新自托管代理
      tags:
      - 自托管代理模块
  /api/v1/self-host-proxies/{id}:
    get:
      consumes:
      - application/json
      description: 根据ID获取单个自托管代理信息
      parameters:
      - description: 代理ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.SelfHostProxyResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 根据ID获取自托管代理
      tags:
      - 自托管代理模块
  /api/v1/self-host-proxies/batch:
    post:
      consumes:
      - application/json
      description: 批量创建多个自托管代理
      parameters:
      - description: 代理信息列表
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/fp-browser_api_v1_user.CreateSelfHostProxiesRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 批量创建自托管代理
      tags:
      - 自托管代理模块
    put:
      consumes:
      - application/json
      description: 批量更新多个自托管代理
      parameters:
      - description: 代理信息列表
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/fp-browser_api_v1_user.UpdateSelfHostProxiesRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 批量更新自托管代理
      tags:
      - 自托管代理模块
  /api/v1/subscriptions:
    get:
      consumes:
      - application/json
      description: 获取系统中所有可用的订阅套餐列表
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.GetSubscriptionsResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 获取所有订阅套餐
      tags:
      - 订阅套餐模块
    post:
      consumes:
      - application/json
      description: 创建新的订阅记录
      parameters:
      - description: 订阅信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/fp-browser_api_v1_user.CreateUserSubscriptionRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.CreateUserSubscriptionResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 创建新订阅
      tags:
      - 订阅模块
  /api/v1/subscriptions/{id}:
    get:
      consumes:
      - application/json
      description: 根据订阅ID获取订阅详细信息
      parameters:
      - description: 订阅ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.GetUserSubscriptionResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 根据ID获取订阅信息
      tags:
      - 订阅模块
    put:
      consumes:
      - application/json
      description: 更新现有订阅的信息
      parameters:
      - description: 订阅ID
        in: path
        name: id
        required: true
        type: integer
      - description: 订阅更新信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/fp-browser_api_v1_user.UpdateUserSubscriptionRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.UpdateUserSubscriptionResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 更新订阅信息
      tags:
      - 订阅模块
  /api/v1/subscriptions/purchase:
    post:
      consumes:
      - application/json
      description: 为指定团队购买订阅套餐，创建相应的订单
      parameters:
      - description: 购买订阅信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/fp-browser_api_v1_user.PurchaseSubscriptionRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.PurchaseSubscriptionResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 购买订阅套餐
      tags:
      - 订阅套餐模块
  /api/v1/team-ip-whitelists:
    delete:
      consumes:
      - application/json
      description: 删除一个或多个团队IP白名单
      parameters:
      - description: IP白名单ID列表
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/fp-browser_api_v1_user.DeleteTeamIPWhitelistRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 删除团队IP白名单
      tags:
      - 团队IP白名单模块
    get:
      consumes:
      - application/json
      description: 获取当前团队的所有IP白名单记录
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.GetTeamIPWhitelistsResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 获取团队IP白名单列表
      tags:
      - 团队IP白名单模块
    post:
      consumes:
      - application/json
      description: 创建新的团队IP白名单
      parameters:
      - description: IP白名单信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/fp-browser_api_v1_user.CreateTeamIPWhitelistRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 创建团队IP白名单
      tags:
      - 团队IP白名单模块
  /api/v1/team-ip-whitelists/authorize-login:
    post:
      consumes:
      - application/json
      description: 授权用户登录请求
      parameters:
      - description: 授权请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/fp-browser_api_v1_user.AuthorizeLoginRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 授权登录请求
      tags:
      - 团队IP白名单模块
  /api/v1/team-ip-whitelists/batch:
    post:
      consumes:
      - application/json
      description: 批量创建多个团队IP白名单
      parameters:
      - description: IP白名单信息列表
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/fp-browser_api_v1_user.CreateTeamIPWhitelistsRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 批量创建团队IP白名单
      tags:
      - 团队IP白名单模块
  /api/v1/team-ip-whitelists/pending-logins:
    get:
      consumes:
      - application/json
      description: 获取当前团队所有待授权的登录请求
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.GetPendingLoginRequestsResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 获取待授权登录请求
      tags:
      - 团队IP白名单模块
  /api/v1/teams:
    put:
      consumes:
      - application/json
      description: 更新团队名称等信息
      parameters:
      - description: 团队信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/fp-browser_api_v1_user.UpdateTeamRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 更新团队信息
      tags:
      - 团队模块
  /api/v1/teams/{team_id}/subscriptions:
    get:
      consumes:
      - application/json
      description: 根据团队ID获取所有订阅记录
      parameters:
      - description: 团队ID
        in: path
        name: team_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.GetUserSubscriptionsResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 获取团队所有订阅
      tags:
      - 订阅模块
  /api/v1/teams/{team_id}/subscriptions/active:
    get:
      consumes:
      - application/json
      description: 根据团队ID获取当前活跃的订阅信息
      parameters:
      - description: 团队ID
        in: path
        name: team_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.GetUserSubscriptionResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 获取团队活跃订阅
      tags:
      - 订阅模块
  /api/v1/teams/users:
    delete:
      consumes:
      - application/json
      description: 根据团队ID删除多个用户
      parameters:
      - description: 用户ID列表
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/fp-browser_api_v1_user.DeleteUsersFromTeamRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 从团队删除用户
      tags:
      - 团队模块
    get:
      consumes:
      - application/json
      description: 根据团队ID获取所有关联的用户，支持分页和筛选
      parameters:
      - description: 用户名筛选
        in: query
        name: username
        type: string
      - description: 角色ID筛选
        in: query
        name: role_id
        type: integer
      - description: 每页数量，最大500
        in: query
        maximum: 500
        minimum: 1
        name: limit
        required: true
        type: integer
      - description: 偏移量
        in: query
        minimum: 0
        name: offset
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.GetUserListResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 获取团队用户列表
      tags:
      - 团队模块
    post:
      consumes:
      - application/json
      description: 添加多个新用户到指定的团队
      parameters:
      - description: 用户信息列表
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/fp-browser_api_v1_user.AddUsersToTeamRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "403":
          description: Forbidden
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 添加用户到团队
      tags:
      - 团队模块
    put:
      consumes:
      - application/json
      description: 更新团队下多个用户的信息（只允许更新用户名、密码、角色ID和启用状态）
      parameters:
      - description: 用户信息列表
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/fp-browser_api_v1_user.UpdateUsersInTeamRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 更新团队用户信息
      tags:
      - 团队模块
  /api/v1/users/profile:
    get:
      consumes:
      - application/json
      description: 获取当前登录用户的完整个人信息，包括团队、角色、钱包余额等
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.GetProfileResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 获取用户个人信息
      tags:
      - 用户模块
    put:
      consumes:
      - application/json
      description: 更新当前登录用户的个人信息，支持邮箱和手机号验证码验证
      parameters:
      - description: 更新信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/fp-browser_api_v1_user.UpdateProfileRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.UpdateProfileResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 更新用户个人信息
      tags:
      - 用户模块
  /api/v1/wallet/balance:
    get:
      consumes:
      - application/json
      description: 获取当前用户的钱包总余额
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.GetWalletBalanceResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 获取钱包余额
      tags:
      - 钱包模块
  /api/v1/wallet/transactions:
    get:
      consumes:
      - application/json
      description: 获取当前用户的钱包交易记录列表
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.GetWalletTransactionsResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 获取钱包交易记录
      tags:
      - 钱包模块
    post:
      consumes:
      - application/json
      description: 创建新的钱包交易记录
      parameters:
      - description: 钱包交易信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/fp-browser_api_v1_user.CreateWalletTransactionRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.CreateWalletTransactionResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/fp-browser_api_v1_user.Response'
      summary: 创建钱包交易记录
      tags:
      - 钱包模块
securityDefinitions:
  Bearer:
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
