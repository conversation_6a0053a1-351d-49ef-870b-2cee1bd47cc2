{"swagger": "2.0", "info": {"description": "This is the FP-Browser API service.", "title": "FP-Browser API", "termsOfService": "http://swagger.io/terms/", "contact": {"name": "API Support", "url": "http://www.swagger.io/support", "email": "<EMAIL>"}, "license": {"name": "Apache 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.html"}, "version": "1.0.0"}, "host": "localhost:8000", "paths": {"/api/v1/auth/register": {"post": {"description": "用户注册，创建新用户和对应的团队。必须提供用户名，邮箱和手机号二选一，密码不少于8位，需要邮箱验证码", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["用户认证模块"], "summary": "用户注册", "parameters": [{"description": "注册信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/fp-browser_api_v1_auth.RegisterRequest"}}], "responses": {"201": {"description": "Created", "schema": {"$ref": "#/definitions/fp-browser_api_v1_auth.RegisterResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fp-browser_api_v1_auth.Response"}}, "409": {"description": "邮箱/电话/用户名已被占用", "schema": {"$ref": "#/definitions/fp-browser_api_v1_auth.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_auth.Response"}}}}}, "/api/v1/captcha/generate": {"post": {"description": "生成验证码图片和PoW挑战，返回验证码图片、种子和难度目标", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["验证码模块"], "summary": "生成验证码", "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/fp-browser_api_v1_shared.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/fp-browser_api_v1_shared.GenerateCaptchaData"}}}]}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_shared.Response"}}}}}, "/api/v1/captcha/verify": {"post": {"description": "验证验证码答案和PoW工作量证明", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["验证码模块"], "summary": "验证验证码", "parameters": [{"description": "验证请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/fp-browser_api_v1_shared.VerifyCaptchaRequest"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/fp-browser_api_v1_shared.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/fp-browser_api_v1_shared.VerifyCaptchaData"}}}]}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fp-browser_api_v1_shared.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_shared.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_shared.Response"}}}}}, "/api/v1/commissions": {"get": {"description": "根据用户ID获取佣金交易记录，支持分页和时间范围筛选", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["佣金模块"], "summary": "获取佣金交易记录", "parameters": [{"maximum": 500, "minimum": 1, "type": "integer", "description": "每页数量，默认100，最大500", "name": "limit", "in": "query"}, {"minimum": 0, "type": "integer", "description": "偏移量，默认0", "name": "offset", "in": "query"}, {"type": "string", "format": "date-time", "description": "开始时间，RFC3339格式", "name": "start_time", "in": "query"}, {"type": "string", "format": "date-time", "description": "结束时间，RFC3339格式", "name": "end_time", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.GetTransactionsResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}, "post": {"description": "创建新的佣金交易记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["佣金模块"], "summary": "创建佣金交易记录", "parameters": [{"description": "佣金交易信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.CreateCommissionRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.CreateCommissionResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}}, "/api/v1/commissions/summary": {"get": {"description": "获取用户的佣金摘要信息，包括总佣金、总提现、可提现金额", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["佣金模块"], "summary": "获取佣金摘要信息", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.GetCommissionSummaryResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}}, "/api/v1/commissions/withdrawable": {"get": {"description": "获取用户当前可以提现的佣金记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["佣金模块"], "summary": "获取可提现的佣金记录", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.GetWithdrawalTransactionsResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}}, "/api/v1/email/send-login-verification": {"post": {"description": "验证captcha和PoW后，发送登录验证邮件", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["邮件模块"], "summary": "发送登录验证邮件", "parameters": [{"description": "发送验证邮件请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/fp-browser_api_v1_shared.SendVerificationEmailRequest"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/fp-browser_api_v1_shared.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/fp-browser_api_v1_shared.SendVerificationEmailData"}}}]}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fp-browser_api_v1_shared.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_shared.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_shared.Response"}}}}}, "/api/v1/email/send-register-verification": {"post": {"description": "验证captcha和PoW后，发送注册验证邮件", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["邮件模块"], "summary": "发送注册验证邮件", "parameters": [{"description": "发送验证邮件请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/fp-browser_api_v1_shared.SendVerificationEmailRequest"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/fp-browser_api_v1_shared.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/fp-browser_api_v1_shared.SendVerificationEmailData"}}}]}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fp-browser_api_v1_shared.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_shared.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_shared.Response"}}}}}, "/api/v1/email/send-reset-verification": {"post": {"description": "验证captcha和PoW后，发送重置密码验证邮件", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["邮件模块"], "summary": "发送重置密码验证邮件", "parameters": [{"description": "发送验证邮件请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/fp-browser_api_v1_shared.SendVerificationEmailRequest"}}], "responses": {"200": {"description": "OK", "schema": {"allOf": [{"$ref": "#/definitions/fp-browser_api_v1_shared.Response"}, {"type": "object", "properties": {"data": {"$ref": "#/definitions/fp-browser_api_v1_shared.SendVerificationEmailData"}}}]}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fp-browser_api_v1_shared.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_shared.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_shared.Response"}}}}}, "/api/v1/environments": {"get": {"description": "根据团队ID获取环境记录，支持分页和筛选", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["环境模块"], "summary": "获取环境列表", "parameters": [{"type": "string", "description": "环境名称筛选", "name": "name", "in": "query"}, {"type": "integer", "description": "用户ID筛选", "name": "user_id", "in": "query"}, {"type": "integer", "description": "分组ID筛选", "name": "group_id", "in": "query"}, {"maximum": 500, "minimum": 1, "type": "integer", "description": "每页数量，默认100，最大500", "name": "limit", "in": "query"}, {"minimum": 0, "type": "integer", "description": "偏移量，默认0", "name": "offset", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.GetEnvironmentsResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}, "put": {"description": "批量更新多个环境记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["环境模块"], "summary": "批量更新环境", "parameters": [{"description": "环境信息列表", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.UpdateEnvironmentRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.UpdateEnvironmentResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}, "post": {"description": "创建新的环境记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["环境模块"], "summary": "创建环境", "parameters": [{"description": "环境信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.CreateEnvironmentRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.CreateEnvironmentResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}, "delete": {"description": "批量删除多个环境记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["环境模块"], "summary": "批量删除环境", "parameters": [{"description": "环境ID列表", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.DeleteEnvironmentRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.DeleteEnvironmentResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}}, "/api/v1/environments/deleted": {"get": {"description": "获取团队下已删除的环境记录，支持分页", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["环境模块"], "summary": "获取已删除的环境列表", "parameters": [{"maximum": 500, "minimum": 1, "type": "integer", "default": 100, "description": "每页数量，默认100，最大500", "name": "limit", "in": "query"}, {"minimum": 0, "type": "integer", "default": 0, "description": "偏移量，默认0", "name": "offset", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.GetEnvironmentsResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}}, "/api/v1/environments/file": {"delete": {"description": "删除环境文件并清除环境的存储路径", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["环境文件"], "summary": "删除环境文件", "parameters": [{"type": "string", "description": "用户令牌", "name": "Authorization", "in": "header", "required": true}, {"description": "请求参数", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.DeleteEnvironmentFileRequest"}}], "responses": {"200": {"description": "成功信息", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "错误信息", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "未授权", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "服务器错误", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/environments/file/upload-url": {"post": {"description": "生成环境文件上传URL", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["环境文件"], "summary": "生成环境文件上传URL", "parameters": [{"type": "string", "description": "用户令牌", "name": "Authorization", "in": "header", "required": true}, {"description": "请求参数", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.GenerateEnvironmentURLRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.GenerateEnvironmentURLResponse"}}, "400": {"description": "错误信息", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "未授权", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "服务器错误", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/environments/permanent": {"delete": {"description": "批量永久删除环境记录，此操作不可逆", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["环境模块"], "summary": "永久删除环境", "parameters": [{"description": "环境ID列表", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.DeleteEnvironmentRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.DeleteEnvironmentResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}}, "/api/v1/environments/proxy": {"put": {"description": "批量更新多个环境的代理ID", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["环境模块"], "summary": "批量更新环境代理", "parameters": [{"description": "环境ID列表和代理ID", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.UpdateEnvironmentProxyRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.UpdateEnvironmentProxyResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}}, "/api/v1/environments/restore": {"put": {"description": "批量恢复已删除的环境记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["环境模块"], "summary": "恢复已删除的环境", "parameters": [{"description": "环境ID列表", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.DeleteEnvironmentRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.RestoreEnvironmentResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}}, "/api/v1/environments/{id}": {"get": {"description": "根据环境ID获取环境记录，包含Redis占用检查", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["环境模块"], "summary": "根据ID获取环境", "parameters": [{"type": "integer", "description": "环境ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_internal_view_model.EnvironmentWithProxy"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "409": {"description": "环境已被占用", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}}, "/api/v1/environments/{id}/download": {"get": {"description": "服务器从OSS获取环境文件并流式传输给客户端", "consumes": ["application/json"], "produces": ["application/octet-stream"], "tags": ["环境文件"], "summary": "下载环境文件", "parameters": [{"type": "integer", "description": "环境ID", "name": "id", "in": "path", "required": true}, {"type": "string", "description": "下载令牌", "name": "token", "in": "query", "required": true}], "responses": {"200": {"description": "环境文件内容", "schema": {"type": "file"}}, "400": {"description": "错误信息", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "未授权", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "文件不存在", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "服务器错误", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/environments/{id}/refresh": {"post": {"description": "刷新环境在Redis中的占用记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["环境模块"], "summary": "刷新环境Redis记录", "parameters": [{"type": "integer", "description": "环境ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.RefreshEnvironmentResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}}, "/api/v1/environments/{id}/remove": {"delete": {"description": "移除环境在Redis中的占用记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["环境模块"], "summary": "移除环境Redis记录", "parameters": [{"type": "integer", "description": "环境ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.RemoveEnvironmentResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}}, "/api/v1/groups": {"get": {"description": "根据团队ID获取分组列表，支持按用户筛选", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["分组模块"], "summary": "获取分组列表", "parameters": [{"type": "integer", "description": "用户ID筛选，为0时获取团队所有分组", "name": "user_id", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.GetGroupsResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}, "put": {"description": "更新指定分组的名称", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["分组模块"], "summary": "更新分组信息", "parameters": [{"description": "更新分组信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.UpdateGroupRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.UpdateGroupResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}, "post": {"description": "创建一个新的分组", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["分组模块"], "summary": "创建新的分组", "parameters": [{"description": "分组信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.CreateGroupRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.CreateGroupResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}, "delete": {"description": "删除指定的分组", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["分组模块"], "summary": "删除分组", "parameters": [{"description": "删除分组信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.DeleteGroupRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.DeleteGroupResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}}, "/api/v1/login": {"post": {"description": "用户使用用户名和密码登录系统，支持IP白名单和授权机制", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["用户认证模块"], "summary": "用户登录", "parameters": [{"description": "登录请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/fp-browser_api_v1_auth.LoginRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_auth.LoginResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fp-browser_api_v1_auth.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_auth.Response"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/fp-browser_api_v1_auth.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_auth.Response"}}}}}, "/api/v1/login-logs": {"get": {"description": "根据团队ID和其他条件获取登录日志记录，支持分页和时间范围筛选", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["登录日志模块"], "summary": "获取登录日志", "parameters": [{"type": "integer", "description": "用户ID筛选", "name": "user_id", "in": "query"}, {"maximum": 500, "minimum": 1, "type": "integer", "description": "每页数量，默认100，最大500", "name": "limit", "in": "query"}, {"minimum": 0, "type": "integer", "description": "偏移量，默认0", "name": "offset", "in": "query"}, {"type": "string", "format": "date-time", "description": "开始时间，RFC3339格式", "name": "start_time", "in": "query"}, {"type": "string", "format": "date-time", "description": "结束时间，RFC3339格式", "name": "end_time", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.GetLoginLogsResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}}, "/api/v1/operation-logs": {"get": {"description": "根据团队ID和其他条件获取操作日志记录，支持分页和多种筛选条件", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["操作日志模块"], "summary": "获取操作日志", "parameters": [{"type": "integer", "description": "用户ID筛选", "name": "user_id", "in": "query"}, {"type": "integer", "description": "操作类型筛选", "name": "action", "in": "query"}, {"type": "integer", "description": "操作分类筛选", "name": "category", "in": "query"}, {"type": "string", "description": "操作目标筛选", "name": "target", "in": "query"}, {"maximum": 500, "minimum": 1, "type": "integer", "description": "每页数量，默认100，最大500", "name": "limit", "in": "query"}, {"minimum": 0, "type": "integer", "description": "偏移量，默认0", "name": "offset", "in": "query"}, {"type": "string", "format": "date-time", "description": "开始时间，RFC3339格式", "name": "start_time", "in": "query"}, {"type": "string", "format": "date-time", "description": "结束时间，RFC3339格式", "name": "end_time", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.GetOperationLogsResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}}, "/api/v1/orders": {"get": {"description": "获取用户的订单列表，支持分页和筛选", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["订单模块"], "summary": "获取订单列表", "parameters": [{"type": "integer", "description": "订单类型", "name": "order_type", "in": "query"}, {"type": "integer", "description": "订单状态", "name": "status", "in": "query"}, {"type": "integer", "description": "支付方式", "name": "payment_method", "in": "query"}, {"maximum": 500, "minimum": 1, "type": "integer", "description": "每页数量，默认100，最大500", "name": "limit", "in": "query"}, {"minimum": 0, "type": "integer", "description": "偏移量，默认0", "name": "offset", "in": "query"}, {"type": "string", "format": "date-time", "description": "开始时间，RFC3339格式", "name": "start_time", "in": "query"}, {"type": "string", "format": "date-time", "description": "结束时间，RFC3339格式", "name": "end_time", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.GetOrdersResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}, "post": {"description": "创建新订单并处理支付", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["订单模块"], "summary": "创建订单", "parameters": [{"description": "订单信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.CreateOrderRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.CreateOrderResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}}, "/api/v1/orders/cancel": {"post": {"description": "取消指定的订单", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["订单模块"], "summary": "取消订单", "parameters": [{"description": "取消订单信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.CancelOrderRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.CancelOrderResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}}, "/api/v1/orders/detail": {"get": {"description": "根据订单ID或订单号获取单个订单信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["订单模块"], "summary": "获取订单信息", "parameters": [{"type": "integer", "description": "订单ID", "name": "order_id", "in": "query"}, {"type": "string", "description": "订单号", "name": "order_number", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.GetOrderResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}}, "/api/v1/orders/price": {"post": {"description": "根据订单信息计算总价格", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["订单模块"], "summary": "计算订单价格", "parameters": [{"description": "订单信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.GetOrderPriceRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.GetOrderPriceResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}}, "/api/v1/roles": {"get": {"description": "根据团队ID和可选的角色ID获取角色记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["角色模块"], "summary": "获取角色列表", "parameters": [{"type": "integer", "description": "角色ID，为0时获取所有角色", "name": "id", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.GetRolesResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}, "put": {"description": "更新指定角色的名称、权限信息和安全登录设置", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["角色模块"], "summary": "更新角色信息", "parameters": [{"description": "更新角色信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.UpdateRoleRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}, "post": {"description": "创建一个新的角色，包含名称、权限信息和安全登录设置", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["角色模块"], "summary": "创建新的角色", "parameters": [{"description": "角色信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.CreateRoleRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}, "delete": {"description": "根据角色ID删除指定的角色", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["角色模块"], "summary": "删除角色", "parameters": [{"description": "删除角色信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.DeleteRoleRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}}, "/api/v1/self-host-proxies": {"get": {"description": "根据多条件筛选获取自托管代理记录列表，支持分页", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["自托管代理模块"], "summary": "获取自托管代理列表", "parameters": [{"type": "integer", "description": "代理ID筛选", "name": "id", "in": "query"}, {"type": "integer", "description": "环境ID筛选", "name": "environment_id", "in": "query"}, {"type": "integer", "description": "代理类型筛选", "name": "type", "in": "query"}, {"type": "string", "description": "代理名称筛选", "name": "name", "in": "query"}, {"maximum": 500, "minimum": 1, "type": "integer", "description": "每页数量，默认100，最大500", "name": "limit", "in": "query"}, {"minimum": 0, "type": "integer", "description": "偏移量，默认0", "name": "offset", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.GetSelfHostProxiesResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}, "put": {"description": "更新自托管代理信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["自托管代理模块"], "summary": "更新自托管代理", "parameters": [{"description": "代理信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.UpdateSelfHostProxyRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}, "post": {"description": "创建新的自托管代理", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["自托管代理模块"], "summary": "创建自托管代理", "parameters": [{"description": "代理信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.CreateSelfHostProxyRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}, "delete": {"description": "删除一个或多个自托管代理", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["自托管代理模块"], "summary": "删除自托管代理", "parameters": [{"description": "代理ID列表", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.DeleteSelfHostProxyRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}}, "/api/v1/self-host-proxies/batch": {"put": {"description": "批量更新多个自托管代理", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["自托管代理模块"], "summary": "批量更新自托管代理", "parameters": [{"description": "代理信息列表", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.UpdateSelfHostProxiesRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}, "post": {"description": "批量创建多个自托管代理", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["自托管代理模块"], "summary": "批量创建自托管代理", "parameters": [{"description": "代理信息列表", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.CreateSelfHostProxiesRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}}, "/api/v1/self-host-proxies/{id}": {"get": {"description": "根据ID获取单个自托管代理信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["自托管代理模块"], "summary": "根据ID获取自托管代理", "parameters": [{"type": "integer", "description": "代理ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.SelfHostProxyResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}}, "/api/v1/subscriptions": {"get": {"description": "获取系统中所有可用的订阅套餐列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["订阅套餐模块"], "summary": "获取所有订阅套餐", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.GetSubscriptionsResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}, "post": {"description": "创建新的订阅记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["订阅模块"], "summary": "创建新订阅", "parameters": [{"description": "订阅信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.CreateUserSubscriptionRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.CreateUserSubscriptionResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}}, "/api/v1/subscriptions/purchase": {"post": {"description": "为指定团队购买订阅套餐，创建相应的订单", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["订阅套餐模块"], "summary": "购买订阅套餐", "parameters": [{"description": "购买订阅信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.PurchaseSubscriptionRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.PurchaseSubscriptionResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}}, "/api/v1/subscriptions/{id}": {"get": {"description": "根据订阅ID获取订阅详细信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["订阅模块"], "summary": "根据ID获取订阅信息", "parameters": [{"type": "integer", "description": "订阅ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.GetUserSubscriptionResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}, "put": {"description": "更新现有订阅的信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["订阅模块"], "summary": "更新订阅信息", "parameters": [{"type": "integer", "description": "订阅ID", "name": "id", "in": "path", "required": true}, {"description": "订阅更新信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.UpdateUserSubscriptionRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.UpdateUserSubscriptionResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}}, "/api/v1/team-ip-whitelists": {"get": {"description": "获取当前团队的所有IP白名单记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["团队IP白名单模块"], "summary": "获取团队IP白名单列表", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.GetTeamIPWhitelistsResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}, "post": {"description": "创建新的团队IP白名单", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["团队IP白名单模块"], "summary": "创建团队IP白名单", "parameters": [{"description": "IP白名单信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.CreateTeamIPWhitelistRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}, "delete": {"description": "删除一个或多个团队IP白名单", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["团队IP白名单模块"], "summary": "删除团队IP白名单", "parameters": [{"description": "IP白名单ID列表", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.DeleteTeamIPWhitelistRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}}, "/api/v1/team-ip-whitelists/authorize-login": {"post": {"description": "授权用户登录请求", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["团队IP白名单模块"], "summary": "授权登录请求", "parameters": [{"description": "授权请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.AuthorizeLoginRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}}, "/api/v1/team-ip-whitelists/batch": {"post": {"description": "批量创建多个团队IP白名单", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["团队IP白名单模块"], "summary": "批量创建团队IP白名单", "parameters": [{"description": "IP白名单信息列表", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.CreateTeamIPWhitelistsRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}}, "/api/v1/team-ip-whitelists/pending-logins": {"get": {"description": "获取当前团队所有待授权的登录请求", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["团队IP白名单模块"], "summary": "获取待授权登录请求", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.GetPendingLoginRequestsResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}}, "/api/v1/teams": {"put": {"description": "更新团队名称等信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["团队模块"], "summary": "更新团队信息", "parameters": [{"description": "团队信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.UpdateTeamRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}}, "/api/v1/teams/users": {"get": {"description": "根据团队ID获取所有关联的用户，支持分页和筛选", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["团队模块"], "summary": "获取团队用户列表", "parameters": [{"type": "string", "description": "用户名筛选", "name": "username", "in": "query"}, {"type": "integer", "description": "角色ID筛选", "name": "role_id", "in": "query"}, {"maximum": 500, "minimum": 1, "type": "integer", "description": "每页数量，最大500", "name": "limit", "in": "query", "required": true}, {"minimum": 0, "type": "integer", "description": "偏移量", "name": "offset", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.GetUserListResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}, "put": {"description": "更新团队下多个用户的信息（只允许更新用户名、密码、角色ID和启用状态）", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["团队模块"], "summary": "更新团队用户信息", "parameters": [{"description": "用户信息列表", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.UpdateUsersInTeamRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}, "post": {"description": "添加多个新用户到指定的团队", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["团队模块"], "summary": "添加用户到团队", "parameters": [{"description": "用户信息列表", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.AddUsersToTeamRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}, "delete": {"description": "根据团队ID删除多个用户", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["团队模块"], "summary": "从团队删除用户", "parameters": [{"description": "用户ID列表", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.DeleteUsersFromTeamRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}}, "/api/v1/teams/{team_id}/subscriptions": {"get": {"description": "根据团队ID获取所有订阅记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["订阅模块"], "summary": "获取团队所有订阅", "parameters": [{"type": "integer", "description": "团队ID", "name": "team_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.GetUserSubscriptionsResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}}, "/api/v1/teams/{team_id}/subscriptions/active": {"get": {"description": "根据团队ID获取当前活跃的订阅信息", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["订阅模块"], "summary": "获取团队活跃订阅", "parameters": [{"type": "integer", "description": "团队ID", "name": "team_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.GetUserSubscriptionResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}}, "/api/v1/users/profile": {"get": {"description": "获取当前登录用户的完整个人信息，包括团队、角色、钱包余额等", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["用户模块"], "summary": "获取用户个人信息", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.GetProfileResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}, "put": {"description": "更新当前登录用户的个人信息，支持邮箱和手机号验证码验证", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["用户模块"], "summary": "更新用户个人信息", "parameters": [{"description": "更新信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.UpdateProfileRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.UpdateProfileResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}}, "/api/v1/wallet/balance": {"get": {"description": "获取当前用户的钱包总余额", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["钱包模块"], "summary": "获取钱包余额", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.GetWalletBalanceResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}}, "/api/v1/wallet/transactions": {"get": {"description": "获取当前用户的钱包交易记录列表", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["钱包模块"], "summary": "获取钱包交易记录", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.GetWalletTransactionsResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}, "post": {"description": "创建新的钱包交易记录", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["钱包模块"], "summary": "创建钱包交易记录", "parameters": [{"description": "钱包交易信息", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.CreateWalletTransactionRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.CreateWalletTransactionResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/fp-browser_api_v1_user.Response"}}}}}}, "definitions": {"fp-browser_api_v1_auth.LoginRequest": {"type": "object", "required": ["identifier", "password"], "properties": {"identifier": {"type": "string", "example": "<EMAIL>"}, "password": {"type": "string", "example": "password123"}}}, "fp-browser_api_v1_auth.LoginResponse": {"type": "object", "properties": {"message": {"type": "string", "example": "用户登入成功！"}}}, "fp-browser_api_v1_auth.RegisterRequest": {"type": "object", "required": ["password", "user_name"], "properties": {"email": {"description": "邮箱，可选，与手机号二选一", "type": "string", "maxLength": 100, "example": "<EMAIL>"}, "email_code": {"description": "邮箱验证码，当提供邮箱时必填，固定6位数字", "type": "string", "example": "123456"}, "invite_code": {"description": "邀请码，可选，固定8位字符", "type": "string", "example": "ABC12345"}, "password": {"description": "密码，必填，最少8字符，最长60字符", "type": "string", "maxLength": 60, "minLength": 8, "example": "password123"}, "telephone": {"description": "手机号，可选，与邮箱二选一", "type": "string", "maxLength": 15, "example": "13800138000"}, "user_name": {"description": "用户名，必填，最长20字符", "type": "string", "maxLength": 20, "example": "john"}}}, "fp-browser_api_v1_auth.RegisterResponse": {"type": "object", "properties": {"message": {"type": "string", "example": "用户注册成功！"}}}, "fp-browser_api_v1_auth.Response": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "data": {}, "message": {"type": "string", "example": "success"}}}, "fp-browser_api_v1_shared.GenerateCaptchaData": {"type": "object", "properties": {"captcha_image": {"description": "base64编码的验证码图片", "type": "string"}, "seed": {"description": "随机种子", "type": "string"}, "target": {"description": "PoW难度目标", "type": "string"}}}, "fp-browser_api_v1_shared.Response": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {}, "message": {"type": "string"}}}, "fp-browser_api_v1_shared.SendVerificationEmailData": {"type": "object", "properties": {"message": {"description": "响应消息", "type": "string"}, "sent": {"description": "是否发送成功", "type": "boolean"}}}, "fp-browser_api_v1_shared.SendVerificationEmailRequest": {"type": "object", "required": ["answer", "email", "nonce", "seed", "target"], "properties": {"answer": {"description": "验证码答案", "type": "string"}, "email": {"description": "邮箱地址", "type": "string"}, "nonce": {"description": "PoW随机数", "type": "string"}, "seed": {"description": "验证码种子", "type": "string"}, "target": {"description": "难度目标", "type": "string"}}}, "fp-browser_api_v1_shared.VerifyCaptchaData": {"type": "object", "properties": {"valid": {"description": "是否验证通过", "type": "boolean"}}}, "fp-browser_api_v1_shared.VerifyCaptchaRequest": {"type": "object", "required": ["answer", "nonce", "seed", "target"], "properties": {"answer": {"description": "验证码答案", "type": "string"}, "nonce": {"description": "PoW随机数", "type": "string"}, "seed": {"description": "种子", "type": "string"}, "target": {"description": "难度目标", "type": "string"}}}, "fp-browser_api_v1_user.AddUsersToTeamRequest": {"type": "object", "required": ["users"], "properties": {"users": {"type": "array", "items": {"$ref": "#/definitions/fp-browser_api_v1_user.UserRequest"}}}}, "fp-browser_api_v1_user.AuthorizeLoginRequest": {"type": "object", "required": ["username"], "properties": {"username": {"description": "要授权的用户名", "type": "string", "example": "user123"}}}, "fp-browser_api_v1_user.CancelOrderRequest": {"type": "object", "properties": {"order_id": {"type": "integer", "example": 1}, "order_number": {"type": "string", "example": "SY20240101120000abc123"}}}, "fp-browser_api_v1_user.CancelOrderResponse": {"type": "object", "properties": {"message": {"type": "string", "example": "订单取消成功"}}}, "fp-browser_api_v1_user.CommissionSummary": {"type": "object", "properties": {"total_commission": {"type": "integer", "example": 10000}, "total_withdrawals": {"type": "integer", "example": 3000}, "withdrawable_amount": {"type": "integer", "example": 7000}}}, "fp-browser_api_v1_user.CreateCommissionRequest": {"type": "object", "required": ["amount", "currency", "transaction_type"], "properties": {"amount": {"type": "integer", "example": 1000}, "currency": {"type": "string", "example": "CNY"}, "description": {"type": "string", "example": "推荐用户注册奖励"}, "reference_id": {"type": "integer", "example": 123}, "transaction_type": {"type": "integer", "example": 1}}}, "fp-browser_api_v1_user.CreateCommissionResponse": {"type": "object", "properties": {"message": {"type": "string", "example": "交易创建成功"}}}, "fp-browser_api_v1_user.CreateEnvironmentRequest": {"type": "object", "required": ["group_id", "name", "parameters", "platform", "proxy_id", "proxy_type"], "properties": {"comment": {"type": "string", "example": "测试环境"}, "group_id": {"type": "integer", "example": 1}, "name": {"type": "string", "maxLength": 100, "example": "开发环境"}, "parameters": {"type": "string", "example": "{}"}, "platform": {"type": "string", "example": "https://amazon.com/"}, "proxy_id": {"type": "integer", "example": 1}, "proxy_type": {"type": "integer", "example": 1}, "sort": {"type": "integer", "example": 1}, "storage": {"type": "string", "example": "s3://bucket/path"}, "tag": {"type": "string", "example": "{}"}}}, "fp-browser_api_v1_user.CreateEnvironmentResponse": {"type": "object", "properties": {"message": {"type": "string", "example": "环境创建成功"}}}, "fp-browser_api_v1_user.CreateGroupRequest": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string", "example": "开发组"}}}, "fp-browser_api_v1_user.CreateGroupResponse": {"type": "object", "properties": {"group_id": {"type": "integer"}, "message": {"type": "string"}}}, "fp-browser_api_v1_user.CreateOrderRequest": {"type": "object", "required": ["order_type", "payment_method"], "properties": {"duration": {"type": "integer", "example": 30}, "environment_count": {"type": "integer", "example": 5}, "member_count": {"type": "integer", "example": 3}, "order_type": {"type": "integer", "example": 1}, "payment_method": {"type": "integer", "example": 1}, "proxy_count": {"type": "integer", "example": 10}, "proxy_id": {"type": "integer", "example": 1}, "recharge": {"type": "integer", "example": 10000}}}, "fp-browser_api_v1_user.CreateOrderResponse": {"type": "object", "properties": {"amount": {"type": "integer", "example": 10000}, "order_id": {"type": "integer", "example": 1}, "order_number": {"type": "string", "example": "SY20240101120000abc123"}, "payment_url": {"type": "string", "example": "https://pay.example.com/pay?order=123"}}}, "fp-browser_api_v1_user.CreateRoleRequest": {"type": "object", "required": ["name", "permissions"], "properties": {"name": {"type": "string", "example": "管理员"}, "permissions": {"type": "string", "example": "{\"read\": true, \"write\": true}"}, "secure": {"type": "boolean", "example": true}}}, "fp-browser_api_v1_user.CreateSelfHostProxiesRequest": {"type": "object", "required": ["proxies"], "properties": {"proxies": {"type": "array", "items": {"$ref": "#/definitions/fp-browser_api_v1_user.CreateSelfHostProxyRequest"}}}}, "fp-browser_api_v1_user.CreateSelfHostProxyRequest": {"type": "object", "required": ["host", "name", "port", "type"], "properties": {"environment_id": {"type": "integer", "example": 1}, "host": {"type": "string", "example": "127.0.0.1"}, "name": {"type": "string", "example": "My Proxy"}, "password": {"type": "string", "example": "password123"}, "port": {"type": "integer", "example": 8080}, "type": {"type": "integer", "example": 1}, "username": {"type": "string", "example": "admin"}}}, "fp-browser_api_v1_user.CreateTeamIPWhitelistRequest": {"type": "object", "required": ["ip_address"], "properties": {"ip_address": {"description": "IP地址或CIDR网段", "type": "string", "example": "*************"}}}, "fp-browser_api_v1_user.CreateTeamIPWhitelistsRequest": {"type": "object", "required": ["ip_addresses"], "properties": {"ip_addresses": {"description": "IP地址列表", "type": "array", "minItems": 1, "items": {"$ref": "#/definitions/fp-browser_api_v1_user.CreateTeamIPWhitelistRequest"}}}}, "fp-browser_api_v1_user.CreateUserSubscriptionRequest": {"type": "object", "required": ["end_date", "members_count", "order_id", "start_date", "status", "storage_size", "team_id", "total_price"], "properties": {"end_date": {"type": "string", "example": "2024-12-31T23:59:59Z"}, "members_count": {"type": "integer", "example": 10}, "order_id": {"type": "integer", "example": 1001}, "start_date": {"type": "string", "example": "2024-01-01T00:00:00Z"}, "status": {"type": "integer", "example": 1}, "storage_size": {"type": "integer", "example": 107374182400}, "team_id": {"type": "integer", "example": 1}, "total_price": {"type": "integer", "example": 99900}}}, "fp-browser_api_v1_user.CreateUserSubscriptionResponse": {"type": "object", "properties": {"message": {"type": "string", "example": "订阅创建成功"}, "subscription_id": {"type": "integer", "example": 123}}}, "fp-browser_api_v1_user.CreateWalletTransactionRequest": {"type": "object", "required": ["amount", "currency", "transaction_type"], "properties": {"amount": {"type": "integer", "example": 1000}, "currency": {"type": "string", "example": "CNY"}, "description": {"type": "string", "example": "充值"}, "reference_id": {"type": "integer", "example": 123}, "transaction_type": {"type": "integer", "example": 1}}}, "fp-browser_api_v1_user.CreateWalletTransactionResponse": {"type": "object", "properties": {"message": {"type": "string", "example": "交易创建成功"}}}, "fp-browser_api_v1_user.DeleteEnvironmentFileRequest": {"type": "object", "required": ["environment_id"], "properties": {"environment_id": {"description": "环境ID", "type": "integer", "example": 1}}}, "fp-browser_api_v1_user.DeleteEnvironmentRequest": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "integer"}, "x-example": "[1,2,3]"}}}, "fp-browser_api_v1_user.DeleteEnvironmentResponse": {"type": "object", "properties": {"message": {"type": "string", "example": "环境记录删除成功"}}}, "fp-browser_api_v1_user.DeleteGroupRequest": {"type": "object", "required": ["id"], "properties": {"id": {"type": "integer", "example": 1}}}, "fp-browser_api_v1_user.DeleteGroupResponse": {"type": "object", "properties": {"message": {"type": "string"}}}, "fp-browser_api_v1_user.DeleteRoleRequest": {"type": "object", "required": ["role_id"], "properties": {"role_id": {"type": "integer", "example": 1}}}, "fp-browser_api_v1_user.DeleteSelfHostProxyRequest": {"type": "object", "required": ["ids"], "properties": {"ids": {"type": "array", "items": {"type": "integer"}, "x-example": "[1,2,3]"}}}, "fp-browser_api_v1_user.DeleteTeamIPWhitelistRequest": {"type": "object", "required": ["ids"], "properties": {"ids": {"description": "要删除的IP白名单ID列表", "type": "array", "minItems": 1, "items": {"type": "integer"}, "example": [1, 2, 3]}}}, "fp-browser_api_v1_user.DeleteUsersFromTeamRequest": {"type": "object", "required": ["user_ids"], "properties": {"user_ids": {"type": "array", "items": {"type": "integer"}}}}, "fp-browser_api_v1_user.FormattedUser": {"type": "object", "properties": {"email": {"type": "string", "example": "<EMAIL>"}, "id": {"type": "integer", "example": 1}, "is_active": {"type": "boolean", "example": true}, "is_owner": {"type": "boolean", "example": false}, "is_two_factor_enabled": {"type": "boolean", "example": false}, "role_name": {"type": "string", "example": "管理员"}, "username": {"type": "string", "example": "john"}}}, "fp-browser_api_v1_user.GenerateEnvironmentURLRequest": {"type": "object", "required": ["environment_id"], "properties": {"environment_id": {"description": "环境ID", "type": "integer", "example": 1}}}, "fp-browser_api_v1_user.GenerateEnvironmentURLResponse": {"type": "object", "properties": {"expire_at": {"description": "过期时间戳", "type": "integer", "example": 1625097600}, "url": {"description": "文件URL", "type": "string", "example": "https://oss.example.com/uploads/..."}}}, "fp-browser_api_v1_user.GetCommissionSummaryResponse": {"type": "object", "properties": {"summary": {"$ref": "#/definitions/fp-browser_api_v1_user.CommissionSummary"}}}, "fp-browser_api_v1_user.GetEnvironmentsResponse": {"type": "object", "properties": {"environments": {"type": "array", "items": {"$ref": "#/definitions/fp-browser_internal_view_model.EnvironmentListItem"}}, "total": {"type": "integer"}}}, "fp-browser_api_v1_user.GetGroupsResponse": {"type": "object", "properties": {"groups": {"type": "array", "items": {"$ref": "#/definitions/fp-browser_internal_view_model.GroupWithUser"}}, "total": {"type": "integer"}}}, "fp-browser_api_v1_user.GetLoginLogsResponse": {"type": "object", "properties": {"logs": {"type": "array", "items": {"$ref": "#/definitions/fp-browser_internal_view_model.LoginLogWithUser"}}, "total": {"type": "integer"}}}, "fp-browser_api_v1_user.GetOperationLogsResponse": {"type": "object", "properties": {"logs": {"type": "array", "items": {"$ref": "#/definitions/fp-browser_internal_dao_model.OperationLog"}}, "total": {"type": "integer"}}}, "fp-browser_api_v1_user.GetOrderPriceRequest": {"type": "object", "required": ["order_type"], "properties": {"duration": {"type": "integer", "example": 30}, "environment_count": {"type": "integer", "example": 5}, "member_count": {"type": "integer", "example": 3}, "order_type": {"type": "integer", "example": 1}, "proxy_count": {"type": "integer", "example": 10}, "proxy_id": {"type": "integer", "example": 1}, "recharge": {"type": "integer", "example": 10000}}}, "fp-browser_api_v1_user.GetOrderPriceResponse": {"type": "object", "properties": {"total_price": {"type": "integer", "example": 10000}}}, "fp-browser_api_v1_user.GetOrderResponse": {"type": "object", "properties": {"order": {"$ref": "#/definitions/fp-browser_internal_dao_model.Order"}}}, "fp-browser_api_v1_user.GetOrdersResponse": {"type": "object", "properties": {"orders": {"type": "array", "items": {"$ref": "#/definitions/fp-browser_internal_dao_model.Order"}}}}, "fp-browser_api_v1_user.GetPendingLoginRequestsResponse": {"type": "object", "properties": {"requests": {"description": "待授权请求列表", "type": "array", "items": {"$ref": "#/definitions/fp-browser_api_v1_user.PendingLoginRequest"}}, "total": {"description": "总数量", "type": "integer"}}}, "fp-browser_api_v1_user.GetProfileResponse": {"type": "object", "properties": {"commission_rate": {"type": "integer"}, "commission_type": {"type": "integer"}, "company_name": {"type": "string"}, "company_unified_social_code": {"type": "string"}, "email": {"type": "string"}, "environment_size_sum": {"type": "integer"}, "id_card_number": {"type": "string"}, "invite_code": {"type": "string"}, "invite_user_id": {"type": "integer"}, "is_active": {"type": "boolean"}, "is_deleted": {"type": "boolean"}, "is_two_factor_enabled": {"type": "boolean"}, "real_name": {"type": "string"}, "real_name_type": {"type": "integer"}, "role": {"$ref": "#/definitions/fp-browser_api_v1_user.ProfileRole"}, "subscription": {"$ref": "#/definitions/fp-browser_api_v1_user.ProfileSubscription"}, "team": {"$ref": "#/definitions/fp-browser_api_v1_user.ProfileTeam"}, "telephone": {"type": "string"}, "two_factor_secret": {"type": "string"}, "user_name": {"type": "string"}, "wallet_amount": {"type": "integer"}}}, "fp-browser_api_v1_user.GetRolesResponse": {"type": "object", "properties": {"roles": {"type": "array", "items": {"$ref": "#/definitions/fp-browser_internal_dao_model.Role"}}}}, "fp-browser_api_v1_user.GetSelfHostProxiesResponse": {"type": "object", "properties": {"proxies": {"type": "array", "items": {"$ref": "#/definitions/fp-browser_api_v1_user.SelfHostProxyItem"}}, "total": {"type": "integer"}}}, "fp-browser_api_v1_user.GetSubscriptionsResponse": {"type": "object", "properties": {"subscriptions": {"type": "array", "items": {"$ref": "#/definitions/fp-browser_internal_dao_model.Subscription"}}}}, "fp-browser_api_v1_user.GetTeamIPWhitelistsResponse": {"type": "object", "properties": {"total": {"description": "总数量", "type": "integer"}, "whitelists": {"description": "IP白名单列表", "type": "array", "items": {"$ref": "#/definitions/fp-browser_internal_dao_model.TeamIPWhitelist"}}}}, "fp-browser_api_v1_user.GetTransactionsResponse": {"type": "object", "properties": {"transactions": {"type": "array", "items": {"$ref": "#/definitions/fp-browser_internal_dao_model.CommissionTransaction"}}}}, "fp-browser_api_v1_user.GetUserListResponse": {"type": "object", "properties": {"total": {"type": "integer"}, "users": {"type": "array", "items": {"$ref": "#/definitions/fp-browser_api_v1_user.FormattedUser"}}}}, "fp-browser_api_v1_user.GetUserSubscriptionResponse": {"type": "object", "properties": {"subscription": {"$ref": "#/definitions/fp-browser_internal_dao_model.UserSubscription"}}}, "fp-browser_api_v1_user.GetUserSubscriptionsResponse": {"type": "object", "properties": {"subscriptions": {"type": "array", "items": {"$ref": "#/definitions/fp-browser_internal_dao_model.UserSubscription"}}}}, "fp-browser_api_v1_user.GetWalletBalanceResponse": {"type": "object", "properties": {"total_balance": {"type": "integer", "example": 5000}}}, "fp-browser_api_v1_user.GetWalletTransactionsResponse": {"type": "object", "properties": {"transactions": {"type": "array", "items": {"$ref": "#/definitions/fp-browser_internal_dao_model.WalletTransaction"}}}}, "fp-browser_api_v1_user.GetWithdrawalTransactionsResponse": {"type": "object", "properties": {"transactions": {"type": "array", "items": {"$ref": "#/definitions/fp-browser_internal_dao_model.CommissionTransaction"}}}}, "fp-browser_api_v1_user.PendingLoginRequest": {"type": "object", "properties": {"request_at": {"description": "请求时间", "type": "string"}, "status": {"description": "状态：pending/authorized", "type": "string"}, "ttl": {"description": "剩余生存时间（秒）", "type": "integer"}, "username": {"description": "用户名", "type": "string"}}}, "fp-browser_api_v1_user.ProfileRole": {"type": "object", "properties": {"name": {"type": "string"}, "permissions": {"type": "string"}, "secure": {"type": "boolean"}}}, "fp-browser_api_v1_user.ProfileSubscription": {"type": "object", "properties": {"end_date": {"type": "string"}, "members_count": {"type": "integer"}, "order_id": {"type": "integer"}, "start_date": {"type": "string"}, "status": {"type": "integer"}, "storage_size": {"type": "integer"}, "total_price": {"type": "integer"}}}, "fp-browser_api_v1_user.ProfileTeam": {"type": "object", "properties": {"name": {"type": "string"}, "owner_id": {"type": "integer"}}}, "fp-browser_api_v1_user.PurchaseSubscriptionRequest": {"type": "object", "required": ["quantity", "subscription_id", "team_id"], "properties": {"quantity": {"type": "integer", "minimum": 1, "example": 1}, "subscription_id": {"type": "integer", "example": 101}, "team_id": {"type": "integer", "example": 1}}}, "fp-browser_api_v1_user.PurchaseSubscriptionResponse": {"type": "object", "properties": {"message": {"type": "string", "example": "订阅购买成功"}, "order": {"$ref": "#/definitions/fp-browser_internal_dao_model.Order"}}}, "fp-browser_api_v1_user.RefreshEnvironmentResponse": {"type": "object", "properties": {"message": {"type": "string", "example": "环境记录刷新成功"}}}, "fp-browser_api_v1_user.RemoveEnvironmentResponse": {"type": "object", "properties": {"message": {"type": "string", "example": "环境记录移除成功"}}}, "fp-browser_api_v1_user.Response": {"type": "object", "properties": {"code": {"type": "integer"}, "data": {}, "message": {"type": "string"}}}, "fp-browser_api_v1_user.RestoreEnvironmentResponse": {"type": "object", "properties": {"message": {"type": "string"}}}, "fp-browser_api_v1_user.SelfHostProxyItem": {"type": "object", "properties": {"environment_name": {"description": "所属环境名称", "type": "string"}, "host": {"description": "代理主机", "type": "string"}, "id": {"type": "integer"}, "name": {"description": "代理名称", "type": "string"}, "port": {"description": "代理端口", "type": "integer"}, "team_name": {"description": "所属团队名", "type": "string"}, "type": {"description": "代理类型", "type": "integer"}}}, "fp-browser_api_v1_user.SelfHostProxyResponse": {"type": "object", "properties": {"environment_id": {"type": "integer", "example": 888}, "host": {"type": "string", "example": "127.0.0.1"}, "id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "家庭代理"}, "password": {"type": "string", "example": "pass123"}, "port": {"type": "integer", "example": 1080}, "team_id": {"type": "integer", "example": 99}, "type": {"description": "1: HTT<PERSON>, 2: HTTPS, 3: SOCKS5", "type": "integer", "example": 3}, "username": {"type": "string", "example": "user1"}}}, "fp-browser_api_v1_user.UpdateEnvironmentItem": {"type": "object", "required": ["group_id", "id", "name", "parameters", "platform", "proxy_id", "proxy_type"], "properties": {"comment": {"type": "string", "example": "更新测试环境"}, "group_id": {"type": "integer", "example": 1}, "id": {"type": "integer", "example": 1}, "name": {"type": "string", "maxLength": 100, "example": "更新环境"}, "parameters": {"type": "string", "example": "{}"}, "platform": {"type": "string", "example": "https://amazon.com/"}, "proxy_id": {"type": "integer", "example": 1}, "proxy_type": {"type": "integer", "example": 1}, "sort": {"type": "integer", "example": 2}, "storage": {"type": "string", "example": "s3://bucket/path"}, "tag": {"type": "string", "example": "{}"}}}, "fp-browser_api_v1_user.UpdateEnvironmentProxyRequest": {"type": "object", "required": ["ids", "proxy_id"], "properties": {"ids": {"description": "环境ID列表", "type": "array", "items": {"type": "integer"}, "x-example": "[1,2,3]"}, "proxy_id": {"description": "新的代理ID", "type": "integer", "example": 5}}}, "fp-browser_api_v1_user.UpdateEnvironmentProxyResponse": {"type": "object", "properties": {"message": {"type": "string", "example": "环境代理更新成功"}}}, "fp-browser_api_v1_user.UpdateEnvironmentRequest": {"type": "object", "required": ["environments"], "properties": {"environments": {"type": "array", "items": {"$ref": "#/definitions/fp-browser_api_v1_user.UpdateEnvironmentItem"}}}}, "fp-browser_api_v1_user.UpdateEnvironmentResponse": {"type": "object", "properties": {"message": {"type": "string", "example": "环境记录更新成功"}}}, "fp-browser_api_v1_user.UpdateGroupRequest": {"type": "object", "required": ["id", "name"], "properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "测试组"}}}, "fp-browser_api_v1_user.UpdateGroupResponse": {"type": "object", "properties": {"message": {"type": "string"}}}, "fp-browser_api_v1_user.UpdateProfileRequest": {"type": "object", "properties": {"email": {"type": "string", "example": "<EMAIL>"}, "email_code": {"type": "string", "example": "123456"}, "enable_2fa": {"type": "boolean", "example": true}, "telephone": {"type": "string", "example": "13900139000"}, "telephone_code": {"type": "string", "example": "654321"}}}, "fp-browser_api_v1_user.UpdateProfileResponse": {"type": "object", "properties": {"message": {"type": "string", "example": "用户信息更新成功"}}}, "fp-browser_api_v1_user.UpdateRoleRequest": {"type": "object", "required": ["id", "name", "permissions"], "properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "管理员"}, "permissions": {"type": "string", "example": "{\"read\": true, \"write\": true}"}, "secure": {"type": "boolean", "example": true}}}, "fp-browser_api_v1_user.UpdateSelfHostProxiesRequest": {"type": "object", "required": ["proxies"], "properties": {"proxies": {"type": "array", "items": {"$ref": "#/definitions/fp-browser_api_v1_user.UpdateSelfHostProxyRequest"}}}}, "fp-browser_api_v1_user.UpdateSelfHostProxyRequest": {"type": "object", "required": ["host", "id", "name", "port", "type"], "properties": {"environment_id": {"type": "integer", "example": 1}, "host": {"type": "string", "example": "127.0.0.1"}, "id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "My Proxy Updated"}, "password": {"type": "string", "example": "password123"}, "port": {"type": "integer", "example": 8080}, "type": {"type": "integer", "example": 1}, "username": {"type": "string", "example": "admin"}}}, "fp-browser_api_v1_user.UpdateTeamRequest": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string", "example": "新团队名称"}}}, "fp-browser_api_v1_user.UpdateUserRequest": {"type": "object", "required": ["id"], "properties": {"id": {"type": "integer", "example": 1}, "is_active": {"type": "boolean", "example": true}, "password": {"type": "string", "example": "password123"}, "role_id": {"type": "integer", "example": 1}, "user_name": {"type": "string", "example": "john"}}}, "fp-browser_api_v1_user.UpdateUserSubscriptionRequest": {"type": "object", "properties": {"end_date": {"type": "string", "example": "2024-12-31T23:59:59Z"}, "members_count": {"type": "integer", "example": 20}, "start_date": {"type": "string", "example": "2024-01-01T00:00:00Z"}, "status": {"type": "integer", "example": 2}, "storage_size": {"type": "integer", "example": 214748364800}, "total_price": {"type": "integer", "example": 199900}}}, "fp-browser_api_v1_user.UpdateUserSubscriptionResponse": {"type": "object", "properties": {"message": {"type": "string", "example": "订阅更新成功"}}}, "fp-browser_api_v1_user.UpdateUsersInTeamRequest": {"type": "object", "required": ["users"], "properties": {"users": {"type": "array", "items": {"$ref": "#/definitions/fp-browser_api_v1_user.UpdateUserRequest"}}}}, "fp-browser_api_v1_user.UserRequest": {"type": "object", "required": ["password", "role_id", "user_name"], "properties": {"is_active": {"type": "boolean", "example": true}, "password": {"type": "string", "example": "password123"}, "role_id": {"type": "integer", "example": 1}, "user_name": {"type": "string", "example": "john"}}}, "fp-browser_internal_dao_model.CommissionTransaction": {"type": "object", "properties": {"amount": {"description": "交易金额，正数为收入，负数为支出", "type": "integer"}, "created_at": {"description": "记录创建时间", "type": "string"}, "currency": {"description": "货币类型，ISO 4217 标准，如 CNY, USD", "type": "string"}, "description": {"description": "交易说明文本，最大100字符", "type": "string"}, "id": {"description": "交易ID，自增主键", "type": "integer"}, "invited_user_id": {"description": "被邀请的用户ID", "type": "integer"}, "reference_id": {"description": "相关订单ID，0 表示无关联订单", "type": "integer"}, "transaction_type": {"description": "交易类型，1返利，2提现，3退款", "type": "integer"}, "unlock_time": {"description": "资金解锁时间，到账前资金冻结", "type": "string"}, "user_id": {"description": "推荐人用户ID", "type": "integer"}}}, "fp-browser_internal_dao_model.OperationLog": {"type": "object", "properties": {"action": {"description": "操作行为编码", "type": "integer"}, "category": {"description": "操作分类标识", "type": "integer"}, "created_at": {"description": "操作事件发生的时间戳", "type": "string"}, "id": {"description": "日志 ID，自增主键", "type": "integer"}, "target": {"description": "操作目标标识，如资源名称或 ID", "type": "string"}, "team_id": {"description": "操作所属团队 ID", "type": "integer"}, "user_id": {"description": "操作发起人用户 ID", "type": "integer"}}}, "fp-browser_internal_dao_model.Order": {"type": "object", "properties": {"amount": {"description": "应支付金额（单位分）", "type": "integer"}, "balance_amount": {"description": "余额抵扣数量", "type": "integer"}, "coupon_id": {"description": "使用的优惠券ID", "type": "integer"}, "created_at": {"description": "订单创建时间", "type": "string"}, "currency": {"description": "货币类型（ISO 4217）", "type": "string"}, "expires_at": {"description": "订单过期时间", "type": "string"}, "id": {"description": "订单主键 ID", "type": "integer"}, "order_content": {"description": "订单详细内容，JSONB 格式", "type": "string"}, "order_number": {"description": "唯一订单号", "type": "string"}, "order_type": {"description": "订单类型（1订阅, 2代理, 3充值）", "type": "integer"}, "payment_method": {"description": "支付方式（1余额, 2Stripe, 3支付宝）", "type": "integer"}, "real_amount": {"description": "实际支付金额（单位分）", "type": "integer"}, "status": {"description": "订单状态（1待支付, 2已支付, 3失败, 4取消）", "type": "integer"}, "team_id": {"description": "用户所属团队 ID", "type": "integer"}, "updated_at": {"description": "订单更新时间", "type": "string"}, "url": {"description": "支付网关的支付链接", "type": "string"}, "user_id": {"description": "下单用户 ID", "type": "integer"}}}, "fp-browser_internal_dao_model.Role": {"type": "object", "properties": {"created_at": {"description": "创建时间", "type": "string"}, "id": {"description": "角色 ID，自增主键", "type": "integer"}, "name": {"description": "角色名称，最长 50 字符", "type": "string"}, "permissions": {"description": "权限信息，JSONB 格式存储，如访问路径、方法等", "type": "string"}, "secure": {"description": "是否开启安全登录", "type": "boolean"}, "team_id": {"description": "所属团队 ID", "type": "integer"}, "updated_at": {"description": "最近更新时间", "type": "string"}}}, "fp-browser_internal_dao_model.Subscription": {"type": "object", "properties": {"created_at": {"description": "创建时间", "type": "string"}, "currency": {"description": "币种，ISO 4217 代码，如 CNY, USD, JPY", "type": "string"}, "id": {"type": "integer"}, "membercount": {"description": "成员数量限制", "type": "integer"}, "name": {"description": "本地化名称，使用 JSONB 存储", "type": "string"}, "price": {"description": "价格金额", "type": "number"}, "storagesize": {"description": "存储容量限制，单位为字节（Byte）", "type": "integer"}, "updated_at": {"description": "更新时间", "type": "string"}}}, "fp-browser_internal_dao_model.TeamIPWhitelist": {"type": "object", "properties": {"created_at": {"description": "创建时间", "type": "string"}, "id": {"type": "integer"}, "ip_address": {"description": "允许登录的 IP 地址", "type": "string"}, "team_id": {"description": "关联的团队 ID", "type": "integer"}}}, "fp-browser_internal_dao_model.UserSubscription": {"type": "object", "properties": {"created_at": {"description": "创建时间", "type": "string"}, "end_date": {"description": "订阅结束时间", "type": "string"}, "id": {"description": "订阅记录主键 ID", "type": "integer"}, "members_count": {"description": "订阅成员数量限制", "type": "integer"}, "order_id": {"description": "对应订单 ID", "type": "integer"}, "start_date": {"description": "订阅开始时间", "type": "string"}, "status": {"description": "订阅状态（1: Active, 2: Expired, 3: Cancelled, 4: Pending）", "type": "integer"}, "storage_size": {"description": "容量限制", "type": "integer"}, "team_id": {"description": "团队 ID", "type": "integer"}, "total_price": {"description": "购买总价（单位分）", "type": "integer"}, "updated_at": {"description": "更新时间", "type": "string"}}}, "fp-browser_internal_dao_model.WalletTransaction": {"type": "object", "properties": {"amount": {"description": "交易金额，单位为分，正数为收入，负数为支出", "type": "integer"}, "created_at": {"description": "交易记录创建时间", "type": "string"}, "currency": {"description": "货币类型，ISO 4217 编码（如 CNY, USD）", "type": "string"}, "description": {"description": "交易说明，最长 100 字符", "type": "string"}, "id": {"description": "主键，自增交易 ID", "type": "integer"}, "reference_id": {"description": "相关订单或对象的引用 ID，默认 0 表示无", "type": "integer"}, "transaction_type": {"description": "交易类型（1充值，2消费，3退款等）", "type": "integer"}, "user_id": {"description": "用户 ID", "type": "integer"}}}, "fp-browser_internal_view_model.EnvironmentListItem": {"type": "object", "properties": {"comment": {"type": "string"}, "created_at": {"type": "string"}, "group_id": {"type": "integer"}, "group_name": {"type": "string"}, "id": {"type": "integer"}, "name": {"type": "string"}, "platform": {"type": "string"}, "proxy": {"$ref": "#/definitions/fp-browser_internal_view_model.ProxyInfo"}, "proxy_type": {"type": "integer"}, "size": {"type": "integer"}, "sort": {"type": "integer"}, "storage": {"type": "string"}, "tag": {"type": "string"}, "team_id": {"type": "integer"}, "updated_at": {"type": "string"}, "user_id": {"type": "integer"}}}, "fp-browser_internal_view_model.EnvironmentWithProxy": {"type": "object", "properties": {"comment": {"description": "详情中包含备注", "type": "string"}, "group_id": {"type": "integer"}, "group_name": {"type": "string"}, "id": {"type": "integer"}, "name": {"type": "string"}, "parameters": {"description": "详情中包含参数", "type": "string"}, "platform": {"type": "string"}, "proxy": {"$ref": "#/definitions/fp-browser_internal_view_model.ProxyInfo"}, "proxy_type": {"type": "integer"}, "storage": {"type": "string"}, "tag": {"type": "string"}, "team_id": {"type": "integer"}, "user_id": {"type": "integer"}}}, "fp-browser_internal_view_model.GroupWithUser": {"type": "object", "properties": {"created_at": {"type": "string"}, "id": {"type": "integer"}, "name": {"type": "string"}, "team_id": {"type": "integer"}, "updated_at": {"type": "string"}, "user_name": {"description": "原来的 UserID 替换成用户名", "type": "string"}}}, "fp-browser_internal_view_model.LoginLogWithUser": {"type": "object", "properties": {"created_at": {"type": "string"}, "id": {"type": "integer"}, "ip_location": {"type": "string"}, "login_ip": {"type": "string"}, "user_name": {"type": "string"}}}, "fp-browser_internal_view_model.ProxyInfo": {"type": "object", "properties": {"address": {"type": "string"}, "name": {"type": "string"}, "password": {"type": "string"}, "port": {"type": "integer"}, "type": {"type": "integer"}, "username": {"type": "string"}}}}, "securityDefinitions": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}