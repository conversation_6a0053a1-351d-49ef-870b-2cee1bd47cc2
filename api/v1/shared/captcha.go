// api/v1/shared/captcha.go
package shared

// GenerateCaptchaData 生成验证码数据（作为Response的Data字段）
type GenerateCaptchaData struct {
	CaptchaImage string `json:"captcha_image"` // base64编码的验证码图片
	Seed         string `json:"seed"`          // 随机种子
	Target       string `json:"target"`        // PoW难度目标
}

// VerifyCaptchaRequest 验证验证码请求
type VerifyCaptchaRequest struct {
	Seed   string `json:"seed" validate:"required"`   // 种子
	Answer string `json:"answer" validate:"required"` // 验证码答案
	Nonce  string `json:"nonce" validate:"required"`  // PoW随机数
	Target string `json:"target" validate:"required"` // 难度目标
}

// VerifyCaptchaData 验证验证码数据（作为Response的Data字段）
type VerifyCaptchaData struct {
	Valid bool `json:"valid"` // 是否验证通过
}
