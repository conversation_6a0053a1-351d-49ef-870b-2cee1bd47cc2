// api/v1/shared/email.go
package shared

// SendVerificationEmailRequest 发送验证邮件请求
type SendVerificationEmailRequest struct {
	Email  string `json:"email" validate:"required,email"` // 邮箱地址
	Seed   string `json:"seed" validate:"required"`        // 验证码种子
	Answer string `json:"answer" validate:"required"`      // 验证码答案
	Nonce  string `json:"nonce" validate:"required"`       // PoW随机数
	Target string `json:"target" validate:"required"`      // 难度目标
}

// SendVerificationEmailData 发送验证邮件响应数据（作为Response.Data字段）
type SendVerificationEmailData struct {
	Message string `json:"message"` // 响应消息
	Sent    bool   `json:"sent"`    // 是否发送成功
}
