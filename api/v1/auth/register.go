package auth

// RegisterRequest 注册请求
type RegisterRequest struct {
	UserName   string `json:"user_name" binding:"required,max=20" example:"john"`                           // 用户名，必填，最长20字符
	Email      string `json:"email,omitempty" binding:"omitempty,email,max=100" example:"<EMAIL>"` // 邮箱，可选，与手机号二选一
	Telephone  string `json:"telephone,omitempty" binding:"max=15" example:"13800138000"`                   // 手机号，可选，与邮箱二选一
	Password   string `json:"password" binding:"required,min=8,max=60" example:"password123"`               // 密码，必填，最少8字符，最长60字符
	InviteCode string `json:"invite_code,omitempty" binding:"omitempty,len=8" example:"ABC12345"`           // 邀请码，可选，固定8位字符
	EmailCode  string `json:"email_code,omitempty" binding:"omitempty,len=6" example:"123456"`              // 邮箱验证码，当提供邮箱时必填，固定6位数字
}

// RegisterResponse 注册响应
type RegisterResponse struct {
	Message string `json:"message" example:"用户注册成功！"`
}
