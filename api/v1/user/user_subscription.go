package user

import (
	"fp-browser/internal/dao/model"
	"time"
)

// CreateUserSubscriptionRequest 创建订阅请求
type CreateUserSubscriptionRequest struct {
	OrderID      int32     `json:"order_id" binding:"required" example:"1001"`
	TeamID       int32     `json:"team_id" binding:"required" example:"1"`
	StartDate    time.Time `json:"start_date" binding:"required" example:"2024-01-01T00:00:00Z"`
	EndDate      time.Time `json:"end_date" binding:"required" example:"2024-12-31T23:59:59Z"`
	StorageSize  int64     `json:"storage_size" binding:"required" example:"107374182400"`
	MembersCount int16     `json:"members_count" binding:"required" example:"10"`
	TotalPrice   int64     `json:"total_price" binding:"required" example:"99900"`
	Status       int16     `json:"status" binding:"required" example:"1"`
}

// CreateUserSubscriptionResponse 创建订阅响应
type CreateUserSubscriptionResponse struct {
	Message        string `json:"message" example:"订阅创建成功"`
	SubscriptionID int32  `json:"subscription_id" example:"123"`
}

// UpdateUserSubscriptionRequest 更新订阅请求
type UpdateUserSubscriptionRequest struct {
	StartDate    *time.Time `json:"start_date,omitempty" example:"2024-01-01T00:00:00Z"`
	EndDate      *time.Time `json:"end_date,omitempty" example:"2024-12-31T23:59:59Z"`
	StorageSize  *int64     `json:"storage_size,omitempty" example:"214748364800"`
	MembersCount *int16     `json:"members_count,omitempty" example:"20"`
	TotalPrice   *int64     `json:"total_price,omitempty" example:"199900"`
	Status       *int16     `json:"status,omitempty" example:"2"`
}

// UpdateUserSubscriptionResponse 更新订阅响应
type UpdateUserSubscriptionResponse struct {
	Message string `json:"message" example:"订阅更新成功"`
}

// GetUserSubscriptionResponse 获取单个订阅响应
type GetUserSubscriptionResponse struct {
	Subscription *model.UserSubscription `json:"subscription"`
}

// GetUserSubscriptionsResponse 获取订阅列表响应
type GetUserSubscriptionsResponse struct {
	Subscriptions []*model.UserSubscription `json:"subscriptions"`
}
