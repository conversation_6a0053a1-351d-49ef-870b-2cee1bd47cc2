package user

// GetUserListRequest 获取用户列表请求
type GetUserListRequest struct {
	Username string `form:"username" example:"john"`
	RoleID   int32  `form:"role_id" example:"1"`
	Limit    int    `form:"limit" binding:"required,min=1,max=500" example:"10"`
	Offset   int    `form:"offset" binding:"min=0" example:"0"`
}

// GetUserListResponse 获取用户列表响应
type GetUserListResponse struct {
	Users []FormattedUser `json:"users"`
	Total int64           `json:"total"`
}

// FormattedUser 格式化的用户信息
type FormattedUser struct {
	ID                 int32  `json:"id" example:"1"`
	Username           string `json:"username" example:"john"`
	Email              string `json:"email" example:"<EMAIL>"`
	IsTwoFactorEnabled bool   `json:"is_two_factor_enabled" example:"false"`
	IsActive           bool   `json:"is_active" example:"true"`
	RoleName           string `json:"role_name" example:"管理员"`
	IsOwner            bool   `json:"is_owner" example:"false"`
}

// UserRequest 用户请求（简化版，只包含必要字段）
type UserRequest struct {
	UserName string `json:"user_name" binding:"required" example:"john"`
	Password string `json:"password" binding:"required" example:"password123"`
	RoleID   int32  `json:"role_id" binding:"required" example:"1"`
	IsActive *bool  `json:"is_active,omitempty" example:"true"`
}

// AddUsersToTeamRequest 添加用户到团队请求
type AddUsersToTeamRequest struct {
	Users []UserRequest `json:"users" binding:"required"`
}

// UpdateUserRequest 更新用户请求（包含ID用于识别用户）
type UpdateUserRequest struct {
	ID       int32  `json:"id" binding:"required" example:"1"`
	UserName string `json:"user_name,omitempty" example:"john"`
	Password string `json:"password,omitempty" example:"password123"`
	RoleID   int32  `json:"role_id,omitempty" example:"1"`
	IsActive *bool  `json:"is_active,omitempty" example:"true"`
}

// UpdateUsersInTeamRequest 更新团队用户请求
type UpdateUsersInTeamRequest struct {
	Users []UpdateUserRequest `json:"users" binding:"required"`
}

// DeleteUsersFromTeamRequest 从团队删除用户请求
type DeleteUsersFromTeamRequest struct {
	UserIDs []int32 `json:"user_ids" binding:"required"`
}

// UpdateTeamRequest 更新团队请求
type UpdateTeamRequest struct {
	Name string `json:"name" binding:"required" example:"新团队名称"`
}
