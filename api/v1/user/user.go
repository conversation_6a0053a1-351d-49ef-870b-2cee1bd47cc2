package user

import "time"

// UpdateProfileRequest 更新用户个人信息请求
type UpdateProfileRequest struct {
	Email         string `json:"email,omitempty" binding:"omitempty,email" example:"<EMAIL>"`
	EmailCode     string `json:"email_code,omitempty" example:"123456"`
	Telephone     string `json:"telephone,omitempty" example:"13900139000"`
	TelephoneCode string `json:"telephone_code,omitempty" example:"654321"`
	Enable2FA     *bool  `json:"enable_2fa,omitempty" example:"true"`
}

// UpdateProfileResponse 更新用户个人信息响应
type UpdateProfileResponse struct {
	Message string `json:"message" example:"用户信息更新成功"`
}

// ChangePasswordRequest 修改密码请求
type ChangePasswordRequest struct {
	OldPassword string `json:"old_password" binding:"required" example:"oldpassword123"`
	NewPassword string `json:"new_password" binding:"required" example:"newpassword123"`
}

// ChangePasswordResponse 修改密码响应
type ChangePasswordResponse struct {
	Message string `json:"message" example:"密码修改成功"`
}

// ProfileTeam 个人信息中的团队信息
type ProfileTeam struct {
	Name    string `json:"name"`
	OwnerID int32  `json:"owner_id"`
}

// ProfileRole 个人信息中的角色信息
type ProfileRole struct {
	Name        string `json:"name"`
	Permissions string `json:"permissions"`
	Secure      *bool  `json:"secure"`
}

// ProfileSubscription 个人信息中的订阅信息
type ProfileSubscription struct {
	OrderID      int32     `json:"order_id"`
	StartDate    time.Time `json:"start_date"`
	EndDate      time.Time `json:"end_date"`
	StorageSize  int64     `json:"storage_size"`
	MembersCount int16     `json:"members_count"`
	TotalPrice   int64     `json:"total_price"`
	Status       int16     `json:"status"`
}

// GetProfileResponse 获取用户个人信息响应
type GetProfileResponse struct {
	UserName                 string               `json:"user_name"`
	Email                    string               `json:"email"`
	Telephone                string               `json:"telephone"`
	IsActive                 *bool                `json:"is_active"`
	IsDeleted                *bool                `json:"is_deleted"`
	IsTwoFactorEnabled       *bool                `json:"is_two_factor_enabled"`
	TwoFactorSecret          *string              `json:"two_factor_secret"`
	RealNameType             *int32               `json:"real_name_type"`
	RealName                 string               `json:"real_name"`
	IDCardNumber             string               `json:"id_card_number"`
	CompanyName              string               `json:"company_name"`
	CompanyUnifiedSocialCode string               `json:"company_unified_social_code"`
	InviteUserID             *int32               `json:"invite_user_id"`
	CommissionType           *int16               `json:"commission_type"`
	CommissionRate           *int16               `json:"commission_rate"`
	InviteCode               *string              `json:"invite_code"`
	Team                     *ProfileTeam         `json:"team"`
	Role                     *ProfileRole         `json:"role"`
	WalletAmount             int64                `json:"wallet_amount"`
	Subscription             *ProfileSubscription `json:"subscription"`
	EnvironmentSizeSum       int64                `json:"environment_size_sum"`
}
