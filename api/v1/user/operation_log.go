package user

import (
	"fp-browser/internal/dao/model"
	"time"
)

// GetOperationLogsRequest 获取操作日志请求
type GetOperationLogsRequest struct {
	UserID    int32      `form:"user_id" example:"1"`
	Action    int32      `form:"action" example:"1"`
	Category  int32      `form:"category" example:"1"`
	Target    string     `form:"target" example:"user_management"`
	Limit     int        `form:"limit" binding:"min=1,max=500" example:"100"`
	Offset    int        `form:"offset" binding:"min=0" example:"0"`
	StartTime *time.Time `form:"start_time" time_format:"2006-01-02T15:04:05Z07:00" example:"2024-01-01T00:00:00Z"`
	EndTime   *time.Time `form:"end_time" time_format:"2006-01-02T15:04:05Z07:00" example:"2024-12-31T23:59:59Z"`
}

// GetOperationLogsResponse 获取操作日志响应
type GetOperationLogsResponse struct {
	Logs  []*model.OperationLog `json:"logs"`
	Total int64                 `json:"total"`
}
