package user

import (
	"fp-browser/internal/view_model"
)

// CreateEnvironmentRequest 创建环境请求
type CreateEnvironmentRequest struct {
	Name       string `json:"name" binding:"required,max=100" example:"开发环境"`
	GroupID    int32  `json:"group_id" binding:"required" example:"1"`
	ProxyID    int32  `json:"proxy_id" binding:"required" example:"1"`
	ProxyType  int16  `json:"proxy_type" binding:"required" example:"1"`
	Platform   string `json:"platform" binding:"required" example:"https://amazon.com/"`
	Parameters string `json:"parameters" binding:"required" example:"{}"`
	Storage    string `json:"storage" example:"s3://bucket/path"`
	Tag        string `json:"tag" example:"{}"`
	Comment    string `json:"comment" example:"测试环境"`
	Sort       int16  `json:"sort" example:"1"`
}

// CreateEnvironmentResponse 创建环境响应
type CreateEnvironmentResponse struct {
	Message string `json:"message" example:"环境创建成功"`
}

// GetEnvironmentsRequest 获取环境列表请求
type GetEnvironmentsRequest struct {
	Name    string `form:"name" example:"环境名称"`
	UserID  int32  `form:"user_id" example:"1"`
	GroupID int32  `form:"group_id" example:"1"`
	Limit   int    `form:"limit" binding:"min=1,max=500" example:"100"`
	Offset  int    `form:"offset" binding:"min=0" example:"0"`
}

// GetEnvironmentsResponse 获取环境列表响应
type GetEnvironmentsResponse struct {
	Environments []*view_model.EnvironmentListItem `json:"environments"`
	Total        int64                             `json:"total"`
}

// UpdateEnvironmentItem 更新环境项
type UpdateEnvironmentItem struct {
	ID         int32   `json:"id" binding:"required" example:"1"`
	Name       *string `json:"name,omitempty" binding:"omitempty,max=100" example:"更新环境"`
	GroupID    *int32  `json:"group_id,omitempty" example:"1"`
	ProxyID    *int32  `json:"proxy_id,omitempty" example:"1"`
	ProxyType  *int16  `json:"proxy_type,omitempty" example:"1"`
	Platform   *string `json:"platform,omitempty" example:"https://amazon.com/"`
	Parameters *string `json:"parameters,omitempty" example:"{}"`
	Storage    *string `json:"storage,omitempty" example:"s3://bucket/path"`
	Tag        *string `json:"tag,omitempty" example:"{}"`
	Comment    *string `json:"comment,omitempty" example:"更新测试环境"`
	Sort       *int16  `json:"sort,omitempty" example:"2"`
}

// UpdateEnvironmentRequest 批量更新环境请求
type UpdateEnvironmentRequest struct {
	Environments []UpdateEnvironmentItem `json:"environments" binding:"required"`
}

// UpdateEnvironmentResponse 批量更新环境响应
type UpdateEnvironmentResponse struct {
	Message string `json:"message" example:"环境记录更新成功"`
}

// UpdateEnvironmentProxyRequest 更新环境代理请求
type UpdateEnvironmentProxyRequest struct {
	IDs     []int32 `json:"ids" binding:"required" extensions:"x-example=[1,2,3]"` // 环境ID列表
	ProxyID int32   `json:"proxy_id" binding:"required" example:"5"`               // 新的代理ID
}

// UpdateEnvironmentProxyResponse 更新环境代理响应
type UpdateEnvironmentProxyResponse struct {
	Message string `json:"message" example:"环境代理更新成功"`
}

// DeleteEnvironmentRequest 批量删除环境请求
type DeleteEnvironmentRequest struct {
	IDs []int32 `json:"ids" extensions:"x-example=[1,2,3]"`
}

type RestoreEnvironmentResponse struct {
	Message string `json:"message"`
}

// DeleteEnvironmentResponse 批量删除环境响应
type DeleteEnvironmentResponse struct {
	Message string `json:"message" example:"环境记录删除成功"`
}

// RefreshEnvironmentResponse 刷新环境Redis记录响应
type RefreshEnvironmentResponse struct {
	Message string `json:"message" example:"环境记录刷新成功"`
}

// RemoveEnvironmentResponse 移除环境Redis记录响应
type RemoveEnvironmentResponse struct {
	Message string `json:"message" example:"环境记录移除成功"`
}

// GenerateEnvironmentURLRequest 生成环境文件URL请求
type GenerateEnvironmentURLRequest struct {
	EnvironmentID int32 `json:"environment_id" binding:"required" example:"1"` // 环境ID
}

// GenerateEnvironmentURLResponse 生成环境文件URL响应
type GenerateEnvironmentURLResponse struct {
	URL      string `json:"url" example:"https://oss.example.com/uploads/..."` // 文件URL
	ExpireAt int64  `json:"expire_at" example:"1625097600"`                    // 过期时间戳
}

// DeleteEnvironmentFileRequest 删除环境文件请求
type DeleteEnvironmentFileRequest struct {
	EnvironmentID int32 `json:"environment_id" binding:"required" example:"1"` // 环境ID
}
