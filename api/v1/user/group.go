package user

import (
	"fp-browser/internal/view_model"
)

// CreateGroupRequest 创建分组请求
type CreateGroupRequest struct {
	Name string `json:"name" binding:"required" example:"开发组"`
}

// CreateGroupResponse 创建分组响应
type CreateGroupResponse struct {
	Message string `json:"message"`
	GroupID int32  `json:"group_id"`
}

// UpdateGroupRequest 更新分组请求
type UpdateGroupRequest struct {
	ID   int32  `json:"id" binding:"required" example:"1"`
	Name string `json:"name" binding:"required" example:"测试组"`
}

// UpdateGroupResponse 更新分组响应
type UpdateGroupResponse struct {
	Message string `json:"message"`
}

// DeleteGroupRequest 删除分组请求
type DeleteGroupRequest struct {
	ID int32 `json:"id" binding:"required" example:"1"`
}

// DeleteGroupResponse 删除分组响应
type DeleteGroupResponse struct {
	Message string `json:"message"`
}

// GetGroupsResponse 获取分组列表响应
type GetGroupsResponse struct {
	Groups []*view_model.GroupWithUser `json:"groups"`
	Total  int64                       `json:"total"`
}
