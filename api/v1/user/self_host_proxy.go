package user

// CreateSelfHostProxyRequest 创建自托管代理请求
type CreateSelfHostProxyRequest struct {
	Name          string `json:"name" binding:"required" example:"My Proxy"`
	Type          int16  `json:"type" binding:"required" example:"1"`
	Host          string `json:"host" binding:"required" example:"127.0.0.1"`
	Port          int16  `json:"port" binding:"required" example:"8080"`
	Username      string `json:"username" example:"admin"`
	Password      string `json:"password" example:"password123"`
	EnvironmentID int32  `json:"environment_id" example:"1"`
}

// UpdateSelfHostProxyRequest 更新自托管代理请求
type UpdateSelfHostProxyRequest struct {
	ID            int32  `json:"id" binding:"required" example:"1"`
	Name          string `json:"name" binding:"required" example:"My Proxy Updated"`
	Type          int16  `json:"type" binding:"required" example:"1"`
	Host          string `json:"host" binding:"required" example:"127.0.0.1"`
	Port          int16  `json:"port" binding:"required" example:"8080"`
	Username      string `json:"username" example:"admin"`
	Password      string `json:"password" example:"password123"`
	EnvironmentID int32  `json:"environment_id" example:"1"`
}

// DeleteSelfHostProxyRequest 删除自托管代理请求
type DeleteSelfHostProxyRequest struct {
	IDs []int32 `json:"ids" binding:"required" extensions:"x-example=[1,2,3]"`
}

// CreateSelfHostProxiesRequest 批量创建自托管代理请求
type CreateSelfHostProxiesRequest struct {
	Proxies []CreateSelfHostProxyRequest `json:"proxies" binding:"required"`
}

// UpdateSelfHostProxiesRequest 批量更新自托管代理请求
type UpdateSelfHostProxiesRequest struct {
	Proxies []UpdateSelfHostProxyRequest `json:"proxies" binding:"required"`
}

// GetSelfHostProxiesRequest 获取自托管代理列表请求
type GetSelfHostProxiesRequest struct {
	ID            int32  `form:"id" example:"1"`
	EnvironmentID int32  `form:"environment_id" example:"1"`
	ProxyType     int16  `form:"type" example:"1"`
	Name          string `form:"name" example:"proxy"`
	Limit         int    `form:"limit" binding:"min=1,max=500" example:"100"`
	Offset        int    `form:"offset" binding:"min=0" example:"0"`
}

// 代理列表子项
type SelfHostProxyItem struct {
	ID              int32  `json:"id"`
	EnvironmentName string `json:"environment_name"` // 所属环境名称
	ProxyType       int16  `json:"type"`             // 代理类型
	Host            string `json:"host"`             // 代理主机
	Port            int16  `json:"port"`             // 代理端口
	Name            string `json:"name"`             // 代理名称
	TeamName        string `json:"team_name"`        // 所属团队名
}

// GetSelfHostProxiesResponse 获取自托管代理列表响应
type GetSelfHostProxiesResponse struct {
	Proxies []*SelfHostProxyItem `json:"proxies"`
	Total   int64                `json:"total"`
}

// SelfHostProxyResponse 用于 Swagger 文档展示自托管代理返回值
type SelfHostProxyResponse struct {
	ID            int32   `json:"id" example:"1"`
	Name          *string `json:"name" example:"家庭代理"`
	Type          int16   `json:"type" example:"3"` // 1: HTTP, 2: HTTPS, 3: SOCKS5
	Host          *string `json:"host" example:"127.0.0.1"`
	Port          int16   `json:"port" example:"1080"`
	Username      *string `json:"username" example:"user1"`
	Password      *string `json:"password" example:"pass123"`
	TeamID        int32   `json:"team_id" example:"99"`
	EnvironmentID int32   `json:"environment_id" example:"888"`
}
