package user

import (
	"fp-browser/internal/dao/model"
	"time"
)

// GetOrderRequest 获取订单请求
type GetOrderRequest struct {
	OrderID     int32  `form:"order_id" example:"1"`
	OrderNumber string `form:"order_number" example:"SY20240101120000abc123"`
}

// GetOrderResponse 获取订单响应
type GetOrderResponse struct {
	Order *model.Order `json:"order"`
}

// GetOrdersRequest 获取订单列表请求
type GetOrdersRequest struct {
	OrderType     int16      `form:"order_type" example:"1"`
	Status        int16      `form:"status" example:"1"`
	PaymentMethod int16      `form:"payment_method" example:"1"`
	Limit         int        `form:"limit" binding:"min=1,max=500" example:"100"`
	Offset        int        `form:"offset" binding:"min=0" example:"0"`
	StartTime     *time.Time `form:"start_time" time_format:"2006-01-02T15:04:05Z07:00" example:"2024-01-01T00:00:00Z"`
	EndTime       *time.Time `form:"end_time" time_format:"2006-01-02T15:04:05Z07:00" example:"2024-12-31T23:59:59Z"`
}

// GetOrdersResponse 获取订单列表响应
type GetOrdersResponse struct {
	Orders []*model.Order `json:"orders"`
}

// CancelOrderRequest 取消订单请求
type CancelOrderRequest struct {
	OrderID     int32  `json:"order_id,omitempty" example:"1"`
	OrderNumber string `json:"order_number,omitempty" example:"SY20240101120000abc123"`
}

// CancelOrderResponse 取消订单响应
type CancelOrderResponse struct {
	Message string `json:"message" example:"订单取消成功"`
}

// CreateOrderRequest 创建订单请求
type CreateOrderRequest struct {
	OrderType        int16 `json:"order_type" binding:"required" example:"1"`
	PaymentMethod    int16 `json:"payment_method" binding:"required" example:"1"`
	EnvironmentCount int32 `json:"environment_count,omitempty" example:"5"`
	MemberCount      int32 `json:"member_count,omitempty" example:"3"`
	Duration         int32 `json:"duration,omitempty" example:"30"`
	ProxyID          int32 `json:"proxy_id,omitempty" example:"1"`
	ProxyCount       int32 `json:"proxy_count,omitempty" example:"10"`
	Recharge         int64 `json:"recharge,omitempty" example:"10000"`
}

// CreateOrderResponse 创建订单响应
type CreateOrderResponse struct {
	OrderID     int32  `json:"order_id" example:"1"`
	OrderNumber string `json:"order_number" example:"SY20240101120000abc123"`
	PaymentURL  string `json:"payment_url" example:"https://pay.example.com/pay?order=123"`
	Amount      int64  `json:"amount" example:"10000"`
}

// GetOrderPriceRequest 获取订单价格请求
type GetOrderPriceRequest struct {
	OrderType        int16 `json:"order_type" binding:"required" example:"1"`
	EnvironmentCount int32 `json:"environment_count,omitempty" example:"5"`
	MemberCount      int32 `json:"member_count,omitempty" example:"3"`
	Duration         int32 `json:"duration,omitempty" example:"30"`
	ProxyID          int32 `json:"proxy_id,omitempty" example:"1"`
	ProxyCount       int32 `json:"proxy_count,omitempty" example:"10"`
	Recharge         int64 `json:"recharge,omitempty" example:"10000"`
}

// GetOrderPriceResponse 获取订单价格响应
type GetOrderPriceResponse struct {
	TotalPrice int64 `json:"total_price" example:"10000"`
}
