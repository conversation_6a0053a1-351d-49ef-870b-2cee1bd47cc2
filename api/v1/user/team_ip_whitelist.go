// api/v1/user/team_ip_whitelist.go
package user

import (
	"fp-browser/internal/dao/model"
	"time"
)

// CreateTeamIPWhitelistRequest 创建单个团队IP白名单请求
type CreateTeamIPWhitelistRequest struct {
	IPAddress string `json:"ip_address" validate:"required" example:"*************"` // IP地址或CIDR网段
}

// CreateTeamIPWhitelistsRequest 批量创建团队IP白名单请求
type CreateTeamIPWhitelistsRequest struct {
	IPAddresses []CreateTeamIPWhitelistRequest `json:"ip_addresses" validate:"required,min=1,dive"` // IP地址列表
}

// DeleteTeamIPWhitelistRequest 删除团队IP白名单请求
type DeleteTeamIPWhitelistRequest struct {
	IDs []int32 `json:"ids" validate:"required,min=1" example:"1,2,3"` // 要删除的IP白名单ID列表
}

// GetTeamIPWhitelistsResponse 获取团队IP白名单列表响应
type GetTeamIPWhitelistsResponse struct {
	Whitelists []*model.TeamIPWhitelist `json:"whitelists"` // IP白名单列表
	Total      int64                    `json:"total"`      // 总数量
}

// PendingLoginRequest 待授权登录请求
type PendingLoginRequest struct {
	Username  string    `json:"username"`   // 用户名
	RequestAt time.Time `json:"request_at"` // 请求时间
	Status    string    `json:"status"`     // 状态：pending/authorized
	TTL       int64     `json:"ttl"`        // 剩余生存时间（秒）
}

// GetPendingLoginRequestsResponse 获取待授权登录请求响应
type GetPendingLoginRequestsResponse struct {
	Requests []PendingLoginRequest `json:"requests"` // 待授权请求列表
	Total    int64                 `json:"total"`    // 总数量
}

// AuthorizeLoginRequest 授权登录请求
type AuthorizeLoginRequest struct {
	Username string `json:"username" validate:"required" example:"user123"` // 要授权的用户名
}
