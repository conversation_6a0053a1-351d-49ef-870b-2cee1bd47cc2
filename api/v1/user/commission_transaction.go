package user

import (
	"fp-browser/internal/dao/model"
	"time"
)

// CreateCommissionRequest 创建佣金交易记录请求
type CreateCommissionRequest struct {
	Amount          int64  `json:"amount" binding:"required" example:"1000"`
	Currency        string `json:"currency" binding:"required" example:"CNY"`
	TransactionType int16  `json:"transaction_type" binding:"required" example:"1"`
	Description     string `json:"description" example:"推荐用户注册奖励"`
	ReferenceID     int32  `json:"reference_id" example:"123"`
}

// CreateCommissionResponse 创建佣金交易记录响应
type CreateCommissionResponse struct {
	Message string `json:"message" example:"交易创建成功"`
}

// GetTransactionsRequest 获取佣金交易记录请求
type GetTransactionsRequest struct {
	Limit     int        `form:"limit" binding:"min=1,max=500" example:"100"`
	Offset    int        `form:"offset" binding:"min=0" example:"0"`
	StartTime *time.Time `form:"start_time" time_format:"2006-01-02T15:04:05Z07:00" example:"2024-01-01T00:00:00Z"`
	EndTime   *time.Time `form:"end_time" time_format:"2006-01-02T15:04:05Z07:00" example:"2024-12-31T23:59:59Z"`
}

// GetTransactionsResponse 获取佣金交易记录响应
type GetTransactionsResponse struct {
	Transactions []*model.CommissionTransaction `json:"transactions"`
}

// GetWithdrawalTransactionsResponse 获取可提现佣金记录响应
type GetWithdrawalTransactionsResponse struct {
	Transactions []*model.CommissionTransaction `json:"transactions"`
}

// CommissionSummary 佣金摘要信息
type CommissionSummary struct {
	TotalCommission    int64 `json:"total_commission" example:"10000"`
	TotalWithdrawals   int64 `json:"total_withdrawals" example:"3000"`
	WithdrawableAmount int64 `json:"withdrawable_amount" example:"7000"`
}

// GetCommissionSummaryResponse 获取佣金摘要信息响应
type GetCommissionSummaryResponse struct {
	Summary CommissionSummary `json:"summary"`
}
