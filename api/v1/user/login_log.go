package user

import (
	"fp-browser/internal/view_model"
	"time"
)

// GetLoginLogsRequest 获取登录日志请求
type GetLoginLogsRequest struct {
	UserID    int32      `form:"user_id" example:"1"`
	Limit     int        `form:"limit" binding:"min=1,max=500" example:"100"`
	Offset    int        `form:"offset" binding:"min=0" example:"0"`
	StartTime *time.Time `form:"start_time" time_format:"2006-01-02T15:04:05Z07:00" example:"2024-01-01T00:00:00Z"`
	EndTime   *time.Time `form:"end_time" time_format:"2006-01-02T15:04:05Z07:00" example:"2024-12-31T23:59:59Z"`
}

// GetLoginLogsResponse 获取登录日志响应
type GetLoginLogsResponse struct {
	Logs  []*view_model.LoginLogWithUser `json:"logs"`
	Total int64                          `json:"total"`
}
