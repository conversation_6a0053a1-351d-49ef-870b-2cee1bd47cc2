package user

import "fp-browser/internal/dao/model"

// CreateWalletTransactionRequest 创建钱包交易记录请求
type CreateWalletTransactionRequest struct {
	Amount          int64  `json:"amount" binding:"required" example:"1000"`
	Currency        string `json:"currency" binding:"required" example:"CNY"`
	TransactionType int16  `json:"transaction_type" binding:"required" example:"1"`
	Description     string `json:"description" example:"充值"`
	ReferenceID     int32  `json:"reference_id" example:"123"`
}

// CreateWalletTransactionResponse 创建钱包交易记录响应
type CreateWalletTransactionResponse struct {
	Message string `json:"message" example:"交易创建成功"`
}

// GetWalletTransactionsResponse 获取钱包交易记录响应
type GetWalletTransactionsResponse struct {
	Transactions []*model.WalletTransaction `json:"transactions"`
}

// GetWalletBalanceResponse 获取钱包余额响应
type GetWalletBalanceResponse struct {
	TotalBalance int64 `json:"total_balance" example:"5000"`
}
