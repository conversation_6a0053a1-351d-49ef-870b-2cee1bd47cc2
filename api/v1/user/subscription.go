package user

import (
	"fp-browser/internal/dao/model"
)

// GetSubscriptionsResponse 获取订阅套餐列表响应
type GetSubscriptionsResponse struct {
	Subscriptions []*model.Subscription `json:"subscriptions"`
}

// PurchaseSubscriptionRequest 购买订阅请求
type PurchaseSubscriptionRequest struct {
	TeamID         int32 `json:"team_id" binding:"required" example:"1"`
	SubscriptionID int32 `json:"subscription_id" binding:"required" example:"101"`
	Quantity       int32 `json:"quantity" binding:"required,min=1" example:"1"`
}

// PurchaseSubscriptionResponse 购买订阅响应
type PurchaseSubscriptionResponse struct {
	Message string       `json:"message" example:"订阅购买成功"`
	Order   *model.Order `json:"order"`
}
