package user

import "fp-browser/internal/dao/model"

// CreateRoleRequest 创建角色请求
type CreateRoleRequest struct {
	Name        string `json:"name" binding:"required" example:"管理员"`
	Permissions string `json:"permissions" binding:"required" example:"{\"read\": true, \"write\": true}"`
	Secure      *bool  `json:"secure,omitempty" example:"true"`
}

// UpdateRoleRequest 更新角色请求
type UpdateRoleRequest struct {
	ID          int32  `json:"id" binding:"required" example:"1"`
	Name        string `json:"name" binding:"required" example:"管理员"`
	Permissions string `json:"permissions" binding:"required" example:"{\"read\": true, \"write\": true}"`
	Secure      *bool  `json:"secure,omitempty" example:"true"`
}

// DeleteRoleRequest 删除角色请求
type DeleteRoleRequest struct {
	RoleID int32 `json:"role_id" binding:"required" example:"1"`
}

// GetRolesResponse 获取角色列表响应
type GetRolesResponse struct {
	Roles []*model.Role `json:"roles"`
}
